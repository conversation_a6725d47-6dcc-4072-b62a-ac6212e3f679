{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"widget-content\"\n};\nconst _hoisted_2 = {\n  class: \"avatar-container\"\n};\nconst _hoisted_3 = [\"src\", \"alt\"];\nconst _hoisted_4 = {\n  class: \"expression-indicator\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"status-indicator listening\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"status-indicator speaking\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"widget-info\"\n};\nconst _hoisted_8 = {\n  class: \"greeting-text\"\n};\nconst _hoisted_9 = {\n  class: \"status-badges\"\n};\nconst _hoisted_10 = {\n  class: \"mode-badge\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"quick-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"vpa-avatar-widget\", [`size-${$props.size}`, `mode-${$setup.vpaStore.currentMode}`, `expression-${$setup.vpaStore.currentExpression}`, {\n      'active': $setup.vpaStore.isActive,\n      'interactive': $props.interactive\n    }]]),\n    style: _normalizeStyle($setup.widgetStyles),\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.handleClick && $setup.handleClick(...args))\n  }, [_createCommentVNode(\" 背景光环 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"widget-aura\", {\n      'pulsing': $setup.vpaStore.isListening || $setup.vpaStore.isSpeaking\n    }])\n  }, null, 2 /* CLASS */), _createCommentVNode(\" 主要内容区 \"), _createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" VPA头像 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"img\", {\n    src: `/docs/${$setup.vpaStore.avatar}`,\n    alt: `VPA ${$setup.vpaStore.currentExpression}`,\n    class: \"avatar-image\",\n    onError: _cache[0] || (_cache[0] = (...args) => $setup.handleImageError && $setup.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_3), _createCommentVNode(\" 表情指示器 \"), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.expressionEmoji), 1 /* TEXT */), _createCommentVNode(\" 状态指示器 \"), $setup.vpaStore.isListening ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n    class: \"pulse-ring\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 🎤 \", -1 /* CACHED */)]))) : $setup.vpaStore.isSpeaking ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"wave-animation\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 🔊 \", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 信息显示（仅在较大尺寸时显示） \"), $setup.showInfo ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.shortGreeting), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.modeText), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 快速操作（仅在交互模式下显示） \"), $props.interactive && $setup.vpaStore.isActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"action-btn\", {\n      active: $setup.vpaStore.isListening\n    }]),\n    onClick: _cache[1] || (_cache[1] = _withModifiers((...args) => $setup.toggleListening && $setup.toggleListening(...args), [\"stop\"]))\n  }, _toDisplayString($setup.vpaStore.isListening ? '⏹️' : '🎤'), 3 /* TEXT, CLASS */)])) : _createCommentVNode(\"v-if\", true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "size", "$setup", "vpaStore", "currentMode", "currentExpression", "isActive", "interactive", "style", "_normalizeStyle", "widgetStyles", "onClick", "_cache", "args", "handleClick", "_createCommentVNode", "_createElementVNode", "isListening", "isSpeaking", "_hoisted_1", "_hoisted_2", "src", "avatar", "alt", "onError", "handleImageError", "_hoisted_4", "_toDisplayString", "expressionEmoji", "_hoisted_5", "_hoisted_6", "showInfo", "_hoisted_7", "_hoisted_8", "shortGreeting", "_hoisted_9", "_hoisted_10", "modeText", "_hoisted_11", "active", "_withModifiers", "toggleListening"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-avatar-widget\"\n    :class=\"[\n      `size-${size}`,\n      `mode-${vpaStore.currentMode}`,\n      `expression-${vpaStore.currentExpression}`,\n      { 'active': vpaStore.isActive, 'interactive': interactive }\n    ]\"\n    :style=\"widgetStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 背景光环 -->\n    <div class=\"widget-aura\" :class=\"{ 'pulsing': vpaStore.isListening || vpaStore.isSpeaking }\"></div>\n    \n    <!-- 主要内容区 -->\n    <div class=\"widget-content\">\n      <!-- VPA头像 -->\n      <div class=\"avatar-container\">\n        <img \n          :src=\"`/docs/${vpaStore.avatar}`\" \n          :alt=\"`VPA ${vpaStore.currentExpression}`\"\n          class=\"avatar-image\"\n          @error=\"handleImageError\"\n        />\n        \n        <!-- 表情指示器 -->\n        <div class=\"expression-indicator\">\n          {{ expressionEmoji }}\n        </div>\n        \n        <!-- 状态指示器 -->\n        <div v-if=\"vpaStore.isListening\" class=\"status-indicator listening\">\n          <div class=\"pulse-ring\"></div>\n          🎤\n        </div>\n        <div v-else-if=\"vpaStore.isSpeaking\" class=\"status-indicator speaking\">\n          <div class=\"wave-animation\"></div>\n          🔊\n        </div>\n      </div>\n      \n      <!-- 信息显示（仅在较大尺寸时显示） -->\n      <div v-if=\"showInfo\" class=\"widget-info\">\n        <div class=\"greeting-text\">{{ shortGreeting }}</div>\n        <div class=\"status-badges\">\n          <span class=\"mode-badge\">{{ modeText }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 快速操作（仅在交互模式下显示） -->\n    <div v-if=\"interactive && vpaStore.isActive\" class=\"quick-actions\">\n      <button \n        class=\"action-btn\"\n        @click.stop=\"toggleListening\"\n        :class=\"{ active: vpaStore.isListening }\"\n      >\n        {{ vpaStore.isListening ? '⏹️' : '🎤' }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPAAvatarWidget',\n  props: {\n    // 组件尺寸：2x2, 2x4, 3x3\n    size: {\n      type: String,\n      default: '2x2',\n      validator: value => ['2x2', '2x4', '3x3'].includes(value)\n    },\n    // 是否可交互\n    interactive: {\n      type: Boolean,\n      default: true\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['click', 'mode-change', 'interaction'],\n  setup(props, { emit }) {\n    const vpaStore = useVPAStore()\n    const imageError = ref(false)\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[vpaStore.currentExpression] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴',\n        'interactive': '交互',\n        'restricted': '访客'\n      }\n      return modes[vpaStore.currentMode] || '未知'\n    })\n\n    const shortGreeting = computed(() => {\n      const greeting = vpaStore.contextualGreeting\n      // 根据尺寸截断问候语\n      const maxLength = props.size === '2x2' ? 10 : props.size === '2x4' ? 20 : 30\n      return greeting.length > maxLength ? greeting.substring(0, maxLength) + '...' : greeting\n    })\n\n    const showInfo = computed(() => {\n      // 只在较大尺寸时显示信息\n      return ['2x4', '3x3'].includes(props.size)\n    })\n\n    const widgetStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position)\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      }\n    })\n\n    // 方法\n    const handleClick = () => {\n      if (!props.interactive || !vpaStore.isActive) return\n      \n      emit('click', {\n        mode: vpaStore.currentMode,\n        size: props.size,\n        position: props.position\n      })\n      \n      // 根据当前模式执行不同操作\n      if (vpaStore.currentMode === 'companion') {\n        vpaStore.switchMode('interactive')\n        emit('mode-change', 'interactive')\n      } else {\n        vpaStore.startConversation()\n        emit('interaction', 'conversation-start')\n      }\n    }\n\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start')\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    return {\n      vpaStore,\n      imageError,\n      expressionEmoji,\n      modeText,\n      shortGreeting,\n      showInfo,\n      widgetStyles,\n      handleClick,\n      toggleListening,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-avatar-widget {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.vpa-avatar-widget:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.vpa-avatar-widget.interactive:hover {\n  border-color: rgba(74, 144, 226, 0.5);\n}\n\n/* 背景光环 */\n.widget-aura {\n  position: absolute;\n  top: -5px;\n  left: -5px;\n  right: -5px;\n  bottom: -5px;\n  border-radius: 20px;\n  background: radial-gradient(\n    circle,\n    rgba(74, 144, 226, 0.2) 0%,\n    rgba(74, 144, 226, 0.05) 50%,\n    transparent 100%\n  );\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.widget-aura.pulsing {\n  opacity: 1;\n  animation: auraPulse 2s ease-in-out infinite;\n}\n\n@keyframes auraPulse {\n  0%, 100% { transform: scale(1); opacity: 0.2; }\n  50% { transform: scale(1.05); opacity: 0.4; }\n}\n\n/* 主要内容区 */\n.widget-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  padding: 8px;\n  gap: 8px;\n}\n\n/* 头像容器 */\n.avatar-container {\n  position: relative;\n  flex-shrink: 0;\n}\n\n/* 不同尺寸的头像大小 */\n.size-2x2 .avatar-container {\n  width: 40px;\n  height: 40px;\n}\n\n.size-2x4 .avatar-container {\n  width: 50px;\n  height: 50px;\n}\n\n.size-3x3 .avatar-container {\n  width: 60px;\n  height: 60px;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n  transition: all 0.3s ease;\n}\n\n.expression-indicator {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 16px;\n  height: 16px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 8px;\n}\n\n/* 状态指示器 */\n.status-indicator {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: white;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 1px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.2); }\n}\n\n/* 信息显示 */\n.widget-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  min-height: 0;\n}\n\n.greeting-text {\n  color: white;\n  font-size: 11px;\n  line-height: 1.2;\n  margin-bottom: 4px;\n  word-break: break-all;\n}\n\n.status-badges {\n  display: flex;\n  gap: 4px;\n}\n\n.mode-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 8px;\n  padding: 2px 4px;\n  border-radius: 8px;\n}\n\n/* 快速操作 */\n.quick-actions {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n}\n\n.action-btn {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(5px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.action-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n.action-btn.active {\n  background: rgba(76, 175, 80, 0.8);\n}\n\n/* 模式样式 */\n.mode-companion .avatar-image {\n  border: 2px solid rgba(76, 175, 80, 0.5);\n}\n\n.mode-interactive .avatar-image {\n  border: 2px solid rgba(33, 150, 243, 0.5);\n}\n\n.mode-restricted .avatar-image {\n  border: 2px solid rgba(158, 158, 158, 0.5);\n  opacity: 0.6;\n}\n\n/* 表情动画 */\n.expression-happy .avatar-image {\n  filter: brightness(1.1) saturate(1.2);\n}\n\n.expression-thinking .avatar-image {\n  filter: grayscale(0.3);\n}\n\n.expression-excited .avatar-image {\n  filter: brightness(1.2) saturate(1.3);\n  animation: excitedBounce 1s ease-in-out infinite;\n}\n\n@keyframes excitedBounce {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n/* 响应式适配 */\n@media (max-width: 768px) {\n  .size-2x2 .avatar-container {\n    width: 35px;\n    height: 35px;\n  }\n  \n  .size-2x4 .avatar-container {\n    width: 45px;\n    height: 45px;\n  }\n  \n  .size-3x3 .avatar-container {\n    width: 55px;\n    height: 55px;\n  }\n  \n  .greeting-text {\n    font-size: 10px;\n  }\n  \n  .mode-badge {\n    font-size: 7px;\n    padding: 1px 3px;\n  }\n}\n</style>\n"], "mappings": ";;EAgBSA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAkB;;;EAStBA,KAAK,EAAC;AAAsB;;;EAKAA,KAAK,EAAC;;;;EAIFA,KAAK,EAAC;;;;EAOxBA,KAAK,EAAC;;;EACpBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAY;;;EAMeA,KAAK,EAAC;;;uBAnDrDC,mBAAA,CA4DM;IA3DJD,KAAK,EAAAE,eAAA,EAAC,mBAAmB,G,QACDC,MAAA,CAAAC,IAAI,I,QAAkBC,MAAA,CAAAC,QAAQ,CAACC,WAAW,I,cAAwBF,MAAA,CAAAC,QAAQ,CAACE,iBAAiB,I;gBAAsBH,MAAA,CAAAC,QAAQ,CAACG,QAAQ;MAAA,eAAiBN,MAAA,CAAAO;IAAW,E;IAMtLC,KAAK,EAAAC,eAAA,CAAEP,MAAA,CAAAQ,YAAY;IACnBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEX,MAAA,CAAAY,WAAA,IAAAZ,MAAA,CAAAY,WAAA,IAAAD,IAAA,CAAW;MAEnBE,mBAAA,UAAa,EACbC,mBAAA,CAAmG;IAA9FnB,KAAK,EAAAE,eAAA,EAAC,aAAa;MAAA,WAAsBG,MAAA,CAAAC,QAAQ,CAACc,WAAW,IAAIf,MAAA,CAAAC,QAAQ,CAACe;IAAU;2BAEzFH,mBAAA,WAAc,EACdC,mBAAA,CAiCM,OAjCNG,UAiCM,GAhCJJ,mBAAA,WAAc,EACdC,mBAAA,CAsBM,OAtBNI,UAsBM,GArBJJ,mBAAA,CAKE;IAJCK,GAAG,WAAWnB,MAAA,CAAAC,QAAQ,CAACmB,MAAM;IAC7BC,GAAG,SAASrB,MAAA,CAAAC,QAAQ,CAACE,iBAAiB;IACvCR,KAAK,EAAC,cAAc;IACnB2B,OAAK,EAAAZ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEX,MAAA,CAAAuB,gBAAA,IAAAvB,MAAA,CAAAuB,gBAAA,IAAAZ,IAAA,CAAgB;wDAG1BE,mBAAA,WAAc,EACdC,mBAAA,CAEM,OAFNU,UAEM,EAAAC,gBAAA,CADDzB,MAAA,CAAA0B,eAAe,kBAGpBb,mBAAA,WAAc,EACHb,MAAA,CAAAC,QAAQ,CAACc,WAAW,I,cAA/BnB,mBAAA,CAGM,OAHN+B,UAGM,EAAAjB,MAAA,QAAAA,MAAA,OAFJI,mBAAA,CAA8B;IAAzBnB,KAAK,EAAC;EAAY,2B,iBAAO,MAEhC,mB,MACgBK,MAAA,CAAAC,QAAQ,CAACe,UAAU,I,cAAnCpB,mBAAA,CAGM,OAHNgC,UAGM,EAAAlB,MAAA,QAAAA,MAAA,OAFJI,mBAAA,CAAkC;IAA7BnB,KAAK,EAAC;EAAgB,2B,iBAAO,MAEpC,mB,2CAGFkB,mBAAA,qBAAwB,EACbb,MAAA,CAAA6B,QAAQ,I,cAAnBjC,mBAAA,CAKM,OALNkC,UAKM,GAJJhB,mBAAA,CAAoD,OAApDiB,UAAoD,EAAAN,gBAAA,CAAtBzB,MAAA,CAAAgC,aAAa,kBAC3ClB,mBAAA,CAEM,OAFNmB,UAEM,GADJnB,mBAAA,CAA8C,QAA9CoB,WAA8C,EAAAT,gBAAA,CAAlBzB,MAAA,CAAAmC,QAAQ,iB,4CAK1CtB,mBAAA,qBAAwB,EACbf,MAAA,CAAAO,WAAW,IAAIL,MAAA,CAAAC,QAAQ,CAACG,QAAQ,I,cAA3CR,mBAAA,CAQM,OARNwC,WAQM,GAPJtB,mBAAA,CAMS;IALPnB,KAAK,EAAAE,eAAA,EAAC,YAAY;MAAAwC,MAAA,EAEArC,MAAA,CAAAC,QAAQ,CAACc;IAAW;IADrCN,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA4B,cAAA,KAAA3B,IAAA,KAAOX,MAAA,CAAAuC,eAAA,IAAAvC,MAAA,CAAAuC,eAAA,IAAA5B,IAAA,CAAe;sBAGzBX,MAAA,CAAAC,QAAQ,CAACc,WAAW,sC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}