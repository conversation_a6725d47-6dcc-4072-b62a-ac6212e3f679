{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, with<PERSON>eys as _withKeys, withDirectives as _withDirectives, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"test-scene-generation\"\n};\nconst _hoisted_2 = {\n  class: \"test-controls\"\n};\nconst _hoisted_3 = {\n  class: \"input-section\"\n};\nconst _hoisted_4 = {\n  class: \"input-group\"\n};\nconst _hoisted_5 = [\"disabled\"];\nconst _hoisted_6 = {\n  class: \"voice-group\"\n};\nconst _hoisted_7 = [\"disabled\"];\nconst _hoisted_8 = {\n  key: 0,\n  class: \"voice-result\"\n};\nconst _hoisted_9 = {\n  class: \"scene-section\"\n};\nconst _hoisted_10 = {\n  class: \"scene-buttons\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = {\n  class: \"test-results\"\n};\nconst _hoisted_13 = {\n  class: \"result-section\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"result-display\"\n};\nconst _hoisted_15 = {\n  class: \"result-item\"\n};\nconst _hoisted_16 = {\n  class: \"result-item\"\n};\nconst _hoisted_17 = {\n  class: \"result-item\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"result-item\"\n};\nconst _hoisted_19 = [\"src\"];\nconst _hoisted_20 = {\n  key: 1,\n  class: \"result-item\"\n};\nconst _hoisted_21 = {\n  class: \"color-palette\"\n};\nconst _hoisted_22 = [\"title\"];\nconst _hoisted_23 = {\n  class: \"log-section\"\n};\nconst _hoisted_24 = {\n  class: \"log-display\"\n};\nconst _hoisted_25 = {\n  class: \"log-time\"\n};\nconst _hoisted_26 = {\n  class: \"log-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"test-header\"\n  }, [_createElementVNode(\"h2\", null, \"🧪 AI-HMI 场景生成测试\"), _createElementVNode(\"p\", null, \"测试语音输入 → LLM优化 → Kolors生图 → 玻璃态主题应用的完整流程\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", null, \"📝 输入测试\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", null, \"文本输入:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.testInput = $event),\n    type: \"text\",\n    placeholder: \"例如：温暖的日落场景，现代玻璃建筑\",\n    onKeyup: _cache[1] || (_cache[1] = _withKeys((...args) => $setup.testTextToImage && $setup.testTextToImage(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.testInput]]), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.testTextToImage && $setup.testTextToImage(...args)),\n    disabled: $setup.isGenerating\n  }, _toDisplayString($setup.isGenerating ? '生成中...' : '生成壁纸'), 9 /* TEXT, PROPS */, _hoisted_5)]), _createElementVNode(\"div\", _hoisted_6, [_cache[5] || (_cache[5] = _createElementVNode(\"label\", null, \"语音输入:\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.testVoiceInput && $setup.testVoiceInput(...args)),\n    disabled: $setup.isListening\n  }, _toDisplayString($setup.isListening ? '🎤 听取中...' : '🎤 开始语音输入'), 9 /* TEXT, PROPS */, _hoisted_7), $setup.voiceResult ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, \"识别结果: \" + _toDisplayString($setup.voiceResult), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_9, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"🎭 场景测试\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.testScenes, scene => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: scene.id,\n      onClick: $event => $setup.testScene(scene),\n      class: \"scene-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(scene.icon)\n    }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString(scene.name), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_11);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"📊 测试结果\", -1 /* CACHED */)), $setup.currentResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[8] || (_cache[8] = _createElementVNode(\"strong\", null, \"原始输入:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($setup.currentResult.input), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_cache[9] || (_cache[9] = _createElementVNode(\"strong\", null, \"LLM优化后:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($setup.currentResult.optimizedPrompt), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[10] || (_cache[10] = _createElementVNode(\"strong\", null, \"生成状态:\", -1 /* CACHED */)), _createElementVNode(\"span\", {\n    class: _normalizeClass($setup.currentResult.status)\n  }, _toDisplayString($setup.currentResult.statusText), 3 /* TEXT, CLASS */)]), $setup.currentResult.imageUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_cache[11] || (_cache[11] = _createElementVNode(\"strong\", null, \"生成图片:\", -1 /* CACHED */)), _createElementVNode(\"img\", {\n    src: $setup.currentResult.imageUrl,\n    alt: \"生成的壁纸\",\n    class: \"generated-image\"\n  }, null, 8 /* PROPS */, _hoisted_19)])) : _createCommentVNode(\"v-if\", true), $setup.currentResult.colors ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_cache[12] || (_cache[12] = _createElementVNode(\"strong\", null, \"提取颜色:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_21, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentResult.colors, (color, key) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: key,\n      class: \"color-item\",\n      style: _normalizeStyle({\n        backgroundColor: color\n      }),\n      title: `${key}: ${color}`\n    }, null, 12 /* STYLE, PROPS */, _hoisted_22);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_23, [_cache[14] || (_cache[14] = _createElementVNode(\"h3\", null, \"📝 测试日志\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.testLogs, (log, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"log-item\", log.type])\n    }, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString(log.time), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString(log.message), 1 /* TEXT */)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "$setup", "testInput", "$event", "type", "placeholder", "onKeyup", "_cache", "_with<PERSON><PERSON><PERSON>", "args", "testTextToImage", "onClick", "disabled", "isGenerating", "_hoisted_5", "_hoisted_6", "testVoiceInput", "isListening", "_hoisted_7", "voiceResult", "_hoisted_8", "_toDisplayString", "_hoisted_9", "_hoisted_10", "_Fragment", "_renderList", "testScenes", "scene", "key", "id", "testScene", "_normalizeClass", "icon", "name", "_hoisted_12", "_hoisted_13", "currentResult", "_hoisted_14", "_hoisted_15", "input", "_hoisted_16", "optimizedPrompt", "_hoisted_17", "status", "statusText", "imageUrl", "_hoisted_18", "src", "alt", "colors", "_hoisted_20", "_hoisted_21", "color", "style", "_normalizeStyle", "backgroundColor", "title", "_hoisted_23", "_hoisted_24", "testLogs", "log", "index", "_hoisted_25", "time", "_hoisted_26", "message"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue"], "sourcesContent": ["<template>\r\n  <div class=\"test-scene-generation\">\r\n    <div class=\"test-header\">\r\n      <h2>🧪 AI-HMI 场景生成测试</h2>\r\n      <p>测试语音输入 → LLM优化 → Kolors生图 → 玻璃态主题应用的完整流程</p>\r\n    </div>\r\n\r\n    <div class=\"test-controls\">\r\n      <div class=\"input-section\">\r\n        <h3>📝 输入测试</h3>\r\n        <div class=\"input-group\">\r\n          <label>文本输入:</label>\r\n          <input \r\n            v-model=\"testInput\" \r\n            type=\"text\" \r\n            placeholder=\"例如：温暖的日落场景，现代玻璃建筑\"\r\n            @keyup.enter=\"testTextToImage\"\r\n          />\r\n          <button @click=\"testTextToImage\" :disabled=\"isGenerating\">\r\n            {{ isGenerating ? '生成中...' : '生成壁纸' }}\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"voice-group\">\r\n          <label>语音输入:</label>\r\n          <button @click=\"testVoiceInput\" :disabled=\"isListening\">\r\n            {{ isListening ? '🎤 听取中...' : '🎤 开始语音输入' }}\r\n          </button>\r\n          <span v-if=\"voiceResult\" class=\"voice-result\">识别结果: {{ voiceResult }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"scene-section\">\r\n        <h3>🎭 场景测试</h3>\r\n        <div class=\"scene-buttons\">\r\n          <button \r\n            v-for=\"scene in testScenes\" \r\n            :key=\"scene.id\"\r\n            @click=\"testScene(scene)\"\r\n            class=\"scene-btn\"\r\n          >\r\n            <i :class=\"scene.icon\"></i>\r\n            {{ scene.name }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"test-results\">\r\n      <div class=\"result-section\">\r\n        <h3>📊 测试结果</h3>\r\n        <div v-if=\"currentResult\" class=\"result-display\">\r\n          <div class=\"result-item\">\r\n            <strong>原始输入:</strong> {{ currentResult.input }}\r\n          </div>\r\n          <div class=\"result-item\">\r\n            <strong>LLM优化后:</strong> {{ currentResult.optimizedPrompt }}\r\n          </div>\r\n          <div class=\"result-item\">\r\n            <strong>生成状态:</strong> \r\n            <span :class=\"currentResult.status\">{{ currentResult.statusText }}</span>\r\n          </div>\r\n          <div v-if=\"currentResult.imageUrl\" class=\"result-item\">\r\n            <strong>生成图片:</strong>\r\n            <img :src=\"currentResult.imageUrl\" alt=\"生成的壁纸\" class=\"generated-image\" />\r\n          </div>\r\n          <div v-if=\"currentResult.colors\" class=\"result-item\">\r\n            <strong>提取颜色:</strong>\r\n            <div class=\"color-palette\">\r\n              <div \r\n                v-for=\"(color, key) in currentResult.colors\" \r\n                :key=\"key\"\r\n                class=\"color-item\"\r\n                :style=\"{ backgroundColor: color }\"\r\n                :title=\"`${key}: ${color}`\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"log-section\">\r\n        <h3>📝 测试日志</h3>\r\n        <div class=\"log-display\">\r\n          <div \r\n            v-for=\"(log, index) in testLogs\" \r\n            :key=\"index\"\r\n            class=\"log-item\"\r\n            :class=\"log.type\"\r\n          >\r\n            <span class=\"log-time\">{{ log.time }}</span>\r\n            <span class=\"log-message\">{{ log.message }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted } from 'vue'\r\nimport ImageGenerationService from '@/services/ImageGenerationService'\r\nimport LlmService from '@/services/LlmService'\r\nimport AsrService from '@/services/AsrService'\r\n// import ColorExtractor from '@/utils/ColorExtractor' // 已被AIColorAnalyzer替代\r\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer'\r\n\r\nexport default {\r\n  name: 'TestSceneGeneration',\r\n  emits: ['colors-extracted'],\r\n  setup(props, { emit }) {\r\n    const testInput = ref('温暖的日落场景，现代玻璃建筑')\r\n    const isGenerating = ref(false)\r\n    const isListening = ref(false)\r\n    const voiceResult = ref('')\r\n    const currentResult = ref(null)\r\n    const testLogs = ref([])\r\n\r\n    const testScenes = ref([\r\n      { id: 'morning', name: '早晨通勤', icon: 'fas fa-sun', prompt: '清晨阳光，现代玻璃建筑，商务氛围' },\r\n      { id: 'evening', name: '晚间放松', icon: 'fas fa-moon', prompt: '温暖夜景，柔和灯光，舒适氛围' },\r\n      { id: 'rainy', name: '雨夜模式', icon: 'fas fa-cloud-rain', prompt: '雨夜街景，霓虹灯光，玻璃反射' },\r\n      { id: 'family', name: '家庭出行', icon: 'fas fa-car', prompt: '温馨家庭，阳光明媚，愉快旅程' }\r\n    ])\r\n\r\n    const addLog = (message, type = 'info') => {\r\n      testLogs.value.unshift({\r\n        time: new Date().toLocaleTimeString(),\r\n        message,\r\n        type\r\n      })\r\n      if (testLogs.value.length > 50) {\r\n        testLogs.value.pop()\r\n      }\r\n    }\r\n\r\n    const testTextToImage = async () => {\r\n      if (!testInput.value.trim()) {\r\n        addLog('请输入测试文本', 'error')\r\n        return\r\n      }\r\n\r\n      isGenerating.value = true\r\n      addLog(`开始测试: ${testInput.value}`, 'info')\r\n\r\n      try {\r\n        currentResult.value = {\r\n          input: testInput.value,\r\n          optimizedPrompt: '',\r\n          status: 'processing',\r\n          statusText: '处理中...',\r\n          imageUrl: null,\r\n          colors: null\r\n        }\r\n\r\n        // 1. LLM优化提示词\r\n        addLog('步骤1: LLM优化提示词...', 'info')\r\n        const llmService = new LlmService()\r\n        const optimizedPrompt = await llmService.generateResponse(testInput.value)\r\n        currentResult.value.optimizedPrompt = optimizedPrompt\r\n        addLog(`LLM优化完成: ${optimizedPrompt}`, 'success')\r\n\r\n        // 2. 调用Kolors生图\r\n        addLog('步骤2: 调用Kolors生图API...', 'info')\r\n        const imageService = new ImageGenerationService()\r\n        const imageResult = await imageService.generateWallpaper(optimizedPrompt)\r\n        currentResult.value.imageUrl = imageResult.imageUrl\r\n        addLog(`图片生成完成: ${imageResult.imageUrl}`, 'success')\r\n\r\n        // 3. AI智能配色分析\r\n        addLog('步骤3: AI智能配色分析...', 'info')\r\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(\r\n          imageResult.imageUrl,\r\n          testInput.value,\r\n          optimizedPrompt\r\n        )\r\n        currentResult.value.colors = intelligentColors\r\n        addLog('AI智能配色完成', 'success')\r\n\r\n        // 显示AI分析结果\r\n        if (intelligentColors.aiAnalysis) {\r\n          addLog(`AI分析: ${intelligentColors.aiAnalysis.mood}氛围, ${intelligentColors.aiAnalysis.brightness}亮度`, 'info')\r\n        }\r\n\r\n        // 发送颜色到父组件\r\n        emit('colors-extracted', intelligentColors)\r\n\r\n        currentResult.value.status = 'success'\r\n        currentResult.value.statusText = '✅ 生成成功'\r\n        addLog('🎉 完整流程测试成功！', 'success')\r\n\r\n      } catch (error) {\r\n        console.error('测试失败:', error)\r\n        currentResult.value.status = 'error'\r\n        currentResult.value.statusText = '❌ 生成失败'\r\n        addLog(`测试失败: ${error.message}`, 'error')\r\n      } finally {\r\n        isGenerating.value = false\r\n      }\r\n    }\r\n\r\n    const testVoiceInput = async () => {\r\n      if (!AsrService.isSupported()) {\r\n        addLog('浏览器不支持语音识别', 'error')\r\n        return\r\n      }\r\n\r\n      isListening.value = true\r\n      addLog('开始语音识别...', 'info')\r\n\r\n      try {\r\n        const result = await AsrService.startListening()\r\n        voiceResult.value = result\r\n        testInput.value = result\r\n        addLog(`语音识别结果: ${result}`, 'success')\r\n        \r\n        // 自动进行文生图测试\r\n        await testTextToImage()\r\n      } catch (error) {\r\n        addLog(`语音识别失败: ${error.message}`, 'error')\r\n      } finally {\r\n        isListening.value = false\r\n      }\r\n    }\r\n\r\n    const testScene = async (scene) => {\r\n      testInput.value = scene.prompt\r\n      addLog(`测试场景: ${scene.name}`, 'info')\r\n      await testTextToImage()\r\n    }\r\n\r\n    onMounted(() => {\r\n      addLog('AI-HMI 场景生成测试组件已加载', 'info')\r\n      addLog('theme_backend 服务应运行在 http://localhost:8000', 'info')\r\n    })\r\n\r\n    return {\r\n      testInput,\r\n      isGenerating,\r\n      isListening,\r\n      voiceResult,\r\n      currentResult,\r\n      testLogs,\r\n      testScenes,\r\n      testTextToImage,\r\n      testVoiceInput,\r\n      testScene\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.test-scene-generation {\r\n  padding: 20px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n}\r\n\r\n.test-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 15px;\r\n}\r\n\r\n.test-controls {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.input-section, .scene-section {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.input-group, .voice-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 10px;\r\n  margin: 5px 0;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.scene-buttons {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 10px;\r\n}\r\n\r\n.scene-btn {\r\n  padding: 12px;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 500;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\n.scene-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.test-results {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.result-section, .log-section {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.result-display {\r\n  background: white;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.generated-image {\r\n  max-width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.color-palette {\r\n  display: flex;\r\n  gap: 5px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.color-item {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.log-display {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  background: #1a1a1a;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 12px;\r\n}\r\n\r\n.log-item {\r\n  margin-bottom: 5px;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.log-time {\r\n  color: #888;\r\n  min-width: 80px;\r\n}\r\n\r\n.log-item.info .log-message { color: #4fc3f7; }\r\n.log-item.success .log-message { color: #66bb6a; }\r\n.log-item.error .log-message { color: #ef5350; }\r\n\r\n.status.success { color: #4caf50; }\r\n.status.error { color: #f44336; }\r\n.status.processing { color: #ff9800; }\r\n\r\nbutton {\r\n  padding: 10px 15px;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  margin: 5px;\r\n  font-weight: 500;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\nbutton:hover:not(:disabled) {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\nbutton:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  background: rgba(128, 128, 128, 0.5);\r\n}\r\n\r\n.voice-result {\r\n  color: #4caf50;\r\n  font-style: italic;\r\n  margin-left: 10px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .test-controls,\r\n  .test-results {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .scene-buttons {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAM3BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAa;;;EAanBA,KAAK,EAAC;AAAa;;;;EAKGA,KAAK,EAAC;;;EAI9BA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAe;;;EAczBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;;EAECA,KAAK,EAAC;;;EACzBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;;EAIWA,KAAK,EAAC;;;;;EAIRA,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAe;;;EAa3BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAa;;EAOdA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAa;;uBA1FnCC,mBAAA,CA+FM,OA/FNC,UA+FM,G,4BA9FJC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAyB,YAArB,kBAAgB,GACpBA,mBAAA,CAA+C,WAA5C,0CAAwC,E,qBAG7CA,mBAAA,CAuCM,OAvCNC,UAuCM,GAtCJD,mBAAA,CAsBM,OAtBNE,UAsBM,G,0BArBJF,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAWM,OAXNG,UAWM,G,0BAVJH,mBAAA,CAAoB,eAAb,OAAK,qB,gBACZA,mBAAA,CAKE;+DAJSI,MAAA,CAAAC,SAAS,GAAAC,MAAA;IAClBC,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,mBAAmB;IAC9BC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,SAAA,KAAAC,IAAA,KAAQR,MAAA,CAAAS,eAAA,IAAAT,MAAA,CAAAS,eAAA,IAAAD,IAAA,CAAe;iEAHpBR,MAAA,CAAAC,SAAS,E,GAKpBL,mBAAA,CAES;IAFAc,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAER,MAAA,CAAAS,eAAA,IAAAT,MAAA,CAAAS,eAAA,IAAAD,IAAA,CAAe;IAAGG,QAAQ,EAAEX,MAAA,CAAAY;sBACvCZ,MAAA,CAAAY,YAAY,4CAAAC,UAAA,E,GAInBjB,mBAAA,CAMM,OANNkB,UAMM,G,0BALJlB,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAES;IAFAc,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAER,MAAA,CAAAe,cAAA,IAAAf,MAAA,CAAAe,cAAA,IAAAP,IAAA,CAAc;IAAGG,QAAQ,EAAEX,MAAA,CAAAgB;sBACtChB,MAAA,CAAAgB,WAAW,oDAAAC,UAAA,GAEJjB,MAAA,CAAAkB,WAAW,I,cAAvBxB,mBAAA,CAA4E,QAA5EyB,UAA4E,EAA9B,QAAM,GAAAC,gBAAA,CAAGpB,MAAA,CAAAkB,WAAW,oB,uCAItEtB,mBAAA,CAaM,OAbNyB,UAaM,G,0BAZJzB,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAUM,OAVN0B,WAUM,I,kBATJ5B,mBAAA,CAQS6B,SAAA,QAAAC,WAAA,CAPSxB,MAAA,CAAAyB,UAAU,EAAnBC,KAAK;yBADdhC,mBAAA,CAQS;MANNiC,GAAG,EAAED,KAAK,CAACE,EAAE;MACblB,OAAK,EAAAR,MAAA,IAAEF,MAAA,CAAA6B,SAAS,CAACH,KAAK;MACvBjC,KAAK,EAAC;QAENG,mBAAA,CAA2B;MAAvBH,KAAK,EAAAqC,eAAA,CAAEJ,KAAK,CAACK,IAAI;8CAAM,GAC3B,GAAAX,gBAAA,CAAGM,KAAK,CAACM,IAAI,iB;wCAMrBpC,mBAAA,CA+CM,OA/CNqC,WA+CM,GA9CJrC,mBAAA,CA8BM,OA9BNsC,WA8BM,G,4BA7BJtC,mBAAA,CAAgB,YAAZ,SAAO,qBACAI,MAAA,CAAAmC,aAAa,I,cAAxBzC,mBAAA,CA2BM,OA3BN0C,WA2BM,GA1BJxC,mBAAA,CAEM,OAFNyC,WAEM,G,0BADJzC,mBAAA,CAAsB,gBAAd,OAAK,qB,iBAAS,GAAC,GAAAwB,gBAAA,CAAGpB,MAAA,CAAAmC,aAAa,CAACG,KAAK,iB,GAE/C1C,mBAAA,CAEM,OAFN2C,WAEM,G,0BADJ3C,mBAAA,CAAwB,gBAAhB,SAAO,qB,iBAAS,GAAC,GAAAwB,gBAAA,CAAGpB,MAAA,CAAAmC,aAAa,CAACK,eAAe,iB,GAE3D5C,mBAAA,CAGM,OAHN6C,WAGM,G,4BAFJ7C,mBAAA,CAAsB,gBAAd,OAAK,qBACbA,mBAAA,CAAyE;IAAlEH,KAAK,EAAAqC,eAAA,CAAE9B,MAAA,CAAAmC,aAAa,CAACO,MAAM;sBAAK1C,MAAA,CAAAmC,aAAa,CAACQ,UAAU,wB,GAEtD3C,MAAA,CAAAmC,aAAa,CAACS,QAAQ,I,cAAjClD,mBAAA,CAGM,OAHNmD,WAGM,G,4BAFJjD,mBAAA,CAAsB,gBAAd,OAAK,qBACbA,mBAAA,CAAyE;IAAnEkD,GAAG,EAAE9C,MAAA,CAAAmC,aAAa,CAACS,QAAQ;IAAEG,GAAG,EAAC,OAAO;IAACtD,KAAK,EAAC;+EAE5CO,MAAA,CAAAmC,aAAa,CAACa,MAAM,I,cAA/BtD,mBAAA,CAWM,OAXNuD,WAWM,G,4BAVJrD,mBAAA,CAAsB,gBAAd,OAAK,qBACbA,mBAAA,CAQM,OARNsD,WAQM,I,kBAPJxD,mBAAA,CAMO6B,SAAA,QAAAC,WAAA,CALkBxB,MAAA,CAAAmC,aAAa,CAACa,MAAM,GAAnCG,KAAK,EAAExB,GAAG;yBADpBjC,mBAAA,CAMO;MAJJiC,GAAG,EAAEA,GAAG;MACTlC,KAAK,EAAC,YAAY;MACjB2D,KAAK,EAAAC,eAAA;QAAAC,eAAA,EAAqBH;MAAK;MAC/BI,KAAK,KAAK5B,GAAG,KAAKwB,KAAK;;oHAOlCvD,mBAAA,CAaM,OAbN4D,WAaM,G,4BAZJ5D,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAUM,OAVN6D,WAUM,I,kBATJ/D,mBAAA,CAQM6B,SAAA,QAAAC,WAAA,CAPmBxB,MAAA,CAAA0D,QAAQ,GAAvBC,GAAG,EAAEC,KAAK;yBADpBlE,mBAAA,CAQM;MANHiC,GAAG,EAAEiC,KAAK;MACXnE,KAAK,EAAAqC,eAAA,EAAC,UAAU,EACR6B,GAAG,CAACxD,IAAI;QAEhBP,mBAAA,CAA4C,QAA5CiE,WAA4C,EAAAzC,gBAAA,CAAlBuC,GAAG,CAACG,IAAI,kBAClClE,mBAAA,CAAkD,QAAlDmE,WAAkD,EAAA3C,gBAAA,CAArBuC,GAAG,CAACK,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}