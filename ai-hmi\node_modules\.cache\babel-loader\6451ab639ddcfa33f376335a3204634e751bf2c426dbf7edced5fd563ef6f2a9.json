{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"music-content\"\n};\nconst _hoisted_2 = {\n  class: \"music-info\"\n};\nconst _hoisted_3 = {\n  class: \"album-cover\"\n};\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  key: 1,\n  class: \"cover-placeholder\"\n};\nconst _hoisted_6 = {\n  class: \"sound-waves\"\n};\nconst _hoisted_7 = {\n  class: \"song-details\"\n};\nconst _hoisted_8 = {\n  class: \"song-title\"\n};\nconst _hoisted_9 = {\n  class: \"song-artist\"\n};\nconst _hoisted_10 = {\n  class: \"song-album\"\n};\nconst _hoisted_11 = {\n  class: \"progress-section\"\n};\nconst _hoisted_12 = {\n  class: \"time-display\"\n};\nconst _hoisted_13 = {\n  class: \"current-time\"\n};\nconst _hoisted_14 = {\n  class: \"total-time\"\n};\nconst _hoisted_15 = {\n  class: \"control-section\"\n};\nconst _hoisted_16 = {\n  class: \"main-controls\"\n};\nconst _hoisted_17 = {\n  class: \"secondary-controls\"\n};\nconst _hoisted_18 = {\n  class: \"volume-control\"\n};\nconst _hoisted_19 = {\n  class: \"volume-slider\"\n};\nconst _hoisted_20 = {\n  class: \"playlist-preview\"\n};\nconst _hoisted_21 = {\n  class: \"playlist-header\"\n};\nconst _hoisted_22 = {\n  class: \"playlist-items\"\n};\nconst _hoisted_23 = [\"onClick\"];\nconst _hoisted_24 = {\n  class: \"item-info\"\n};\nconst _hoisted_25 = {\n  class: \"item-title\"\n};\nconst _hoisted_26 = {\n  class: \"item-artist\"\n};\nconst _hoisted_27 = {\n  class: \"item-duration\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": \"music\",\n    size: \"large\",\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    title: '音乐控制',\n    icon: 'fas fa-music',\n    clickable: true,\n    \"show-header\": false,\n    onClick: $setup.handleCardClick,\n    class: \"music-control-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 专辑封面和歌曲信息 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [$setup.currentSong.cover ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $setup.currentSong.cover,\n      alt: $setup.currentSong.title,\n      class: \"cover-image\"\n    }, null, 8 /* PROPS */, _hoisted_4)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n      class: \"fas fa-music\"\n    }, null, -1 /* CACHED */)]))), _createCommentVNode(\" 播放状态覆盖层 \"), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"play-overlay\", {\n        active: $setup.isPlaying\n      }])\n    }, [_createElementVNode(\"div\", _hoisted_6, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(4, i => {\n      return _createElementVNode(\"div\", {\n        class: \"wave\",\n        key: i,\n        style: _normalizeStyle({\n          animationDelay: `${i * 0.1}s`\n        })\n      }, null, 4 /* STYLE */);\n    }), 64 /* STABLE_FRAGMENT */))])], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", _hoisted_8, _toDisplayString($setup.currentSong.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_9, _toDisplayString($setup.currentSong.artist), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString($setup.currentSong.album), 1 /* TEXT */)])]), _createCommentVNode(\" 播放进度 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString($setup.formatTime($setup.currentTime)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.formatTime($setup.currentSong.duration)), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: \"progress-bar\",\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.seekTo && $setup.seekTo(...args))\n    }, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n      class: \"progress-track\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: `${$setup.progressPercentage}%`\n      })\n    }, null, 4 /* STYLE */), _createElementVNode(\"div\", {\n      class: \"progress-thumb\",\n      style: _normalizeStyle({\n        left: `${$setup.progressPercentage}%`\n      })\n    }, null, 4 /* STYLE */)])]), _createCommentVNode(\" 播放控制 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $setup.previousSong && $setup.previousSong(...args)),\n      class: \"control-btn\"\n    }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-backward\"\n    }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $setup.togglePlay && $setup.togglePlay(...args)),\n      class: \"control-btn play-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.isPlaying ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"button\", {\n      onClick: _cache[3] || (_cache[3] = (...args) => $setup.nextSong && $setup.nextSong(...args)),\n      class: \"control-btn\"\n    }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-forward\"\n    }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"button\", {\n      onClick: _cache[4] || (_cache[4] = (...args) => $setup.toggleShuffle && $setup.toggleShuffle(...args)),\n      class: _normalizeClass(['control-btn', {\n        active: $setup.isShuffled\n      }])\n    }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n      class: \"fas fa-random\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[5] || (_cache[5] = (...args) => $setup.toggleRepeat && $setup.toggleRepeat(...args)),\n      class: _normalizeClass(['control-btn', {\n        active: $setup.isRepeating\n      }])\n    }, _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n      class: \"fas fa-redo\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"button\", {\n      onClick: _cache[6] || (_cache[6] = (...args) => $setup.toggleMute && $setup.toggleMute(...args)),\n      class: \"control-btn volume-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.volumeIcon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"range\",\n      min: \"0\",\n      max: \"100\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.volume = $event),\n      class: \"volume-input\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.volume]])])])])]), _createCommentVNode(\" 播放列表预览 \"), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"播放列表\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n      onClick: _cache[8] || (_cache[8] = (...args) => $setup.openFullPlaylist && $setup.openFullPlaylist(...args)),\n      class: \"playlist-btn\"\n    }, _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n      class: \"fas fa-list\"\n    }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.playlist.slice(0, 3), (song, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass(['playlist-item', {\n          active: $setup.currentSongIndex === index\n        }]),\n        onClick: $event => $setup.playSong(index)\n      }, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString(song.title), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString(song.artist), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($setup.formatTime(song.duration)), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_23);\n    }), 128 /* KEYED_FRAGMENT */))])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"position\", \"theme\", \"theme-colors\", \"onClick\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "position", "$props", "theme", "themeColors", "title", "icon", "clickable", "onClick", "$setup", "handleCardClick", "_createElementVNode", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "currentSong", "cover", "_createElementBlock", "src", "alt", "_hoisted_5", "_cache", "_normalizeClass", "active", "isPlaying", "_hoisted_6", "_Fragment", "_renderList", "i", "key", "style", "_normalizeStyle", "animationDelay", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "artist", "_hoisted_10", "album", "_hoisted_11", "_hoisted_12", "_hoisted_13", "formatTime", "currentTime", "_hoisted_14", "duration", "args", "seekTo", "width", "progressPercentage", "left", "_hoisted_15", "_hoisted_16", "previousSong", "togglePlay", "nextSong", "_hoisted_17", "toggleShuffle", "isShuffled", "toggleRepeat", "isRepeating", "_hoisted_18", "toggleMute", "volumeIcon", "_hoisted_19", "type", "min", "max", "volume", "$event", "_hoisted_20", "_hoisted_21", "openFullPlaylist", "_hoisted_22", "playlist", "slice", "song", "index", "currentSongIndex", "playSong", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    card-type=\"music\"\n    size=\"large\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :title=\"'音乐控制'\"\n    :icon=\"'fas fa-music'\"\n    :clickable=\"true\"\n    :show-header=\"false\"\n    @click=\"handleCardClick\"\n    class=\"music-control-card\"\n  >\n    <div class=\"music-content\">\n      <!-- 专辑封面和歌曲信息 -->\n      <div class=\"music-info\">\n        <div class=\"album-cover\">\n          <img \n            v-if=\"currentSong.cover\"\n            :src=\"currentSong.cover\"\n            :alt=\"currentSong.title\"\n            class=\"cover-image\"\n          />\n          <div v-else class=\"cover-placeholder\">\n            <i class=\"fas fa-music\"></i>\n          </div>\n          \n          <!-- 播放状态覆盖层 -->\n          <div class=\"play-overlay\" :class=\"{ active: isPlaying }\">\n            <div class=\"sound-waves\">\n              <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"song-details\">\n          <h3 class=\"song-title\">{{ currentSong.title }}</h3>\n          <p class=\"song-artist\">{{ currentSong.artist }}</p>\n          <p class=\"song-album\">{{ currentSong.album }}</p>\n        </div>\n      </div>\n\n      <!-- 播放进度 -->\n      <div class=\"progress-section\">\n        <div class=\"time-display\">\n          <span class=\"current-time\">{{ formatTime(currentTime) }}</span>\n          <span class=\"total-time\">{{ formatTime(currentSong.duration) }}</span>\n        </div>\n        <div class=\"progress-bar\" @click=\"seekTo\">\n          <div class=\"progress-track\"></div>\n          <div \n            class=\"progress-fill\" \n            :style=\"{ width: `${progressPercentage}%` }\"\n          ></div>\n          <div \n            class=\"progress-thumb\" \n            :style=\"{ left: `${progressPercentage}%` }\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- 播放控制 -->\n      <div class=\"control-section\">\n        <div class=\"main-controls\">\n          <button @click=\"previousSong\" class=\"control-btn\">\n            <i class=\"fas fa-step-backward\"></i>\n          </button>\n          \n          <button @click=\"togglePlay\" class=\"control-btn play-btn\">\n            <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n          </button>\n          \n          <button @click=\"nextSong\" class=\"control-btn\">\n            <i class=\"fas fa-step-forward\"></i>\n          </button>\n        </div>\n        \n        <div class=\"secondary-controls\">\n          <button @click=\"toggleShuffle\" :class=\"['control-btn', { active: isShuffled }]\">\n            <i class=\"fas fa-random\"></i>\n          </button>\n          \n          <button @click=\"toggleRepeat\" :class=\"['control-btn', { active: isRepeating }]\">\n            <i class=\"fas fa-redo\"></i>\n          </button>\n          \n          <div class=\"volume-control\">\n            <button @click=\"toggleMute\" class=\"control-btn volume-btn\">\n              <i :class=\"volumeIcon\"></i>\n            </button>\n            <div class=\"volume-slider\">\n              <input \n                type=\"range\" \n                min=\"0\" \n                max=\"100\" \n                v-model=\"volume\"\n                class=\"volume-input\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 播放列表预览 -->\n      <div class=\"playlist-preview\">\n        <div class=\"playlist-header\">\n          <span>播放列表</span>\n          <button @click=\"openFullPlaylist\" class=\"playlist-btn\">\n            <i class=\"fas fa-list\"></i>\n          </button>\n        </div>\n        <div class=\"playlist-items\">\n          <div \n            v-for=\"(song, index) in playlist.slice(0, 3)\" \n            :key=\"index\"\n            :class=\"['playlist-item', { active: currentSongIndex === index }]\"\n            @click=\"playSong(index)\"\n          >\n            <div class=\"item-info\">\n              <span class=\"item-title\">{{ song.title }}</span>\n              <span class=\"item-artist\">{{ song.artist }}</span>\n            </div>\n            <div class=\"item-duration\">{{ formatTime(song.duration) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '../BaseCard.vue'\n\nexport default {\n  name: 'MusicControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 2 })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#9b59b6',\n        secondary: '#e74c3c',\n        background: 'rgba(155, 89, 182, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  \n  emits: ['card-click', 'song-changed', 'play-state-changed'],\n  \n  setup(props, { emit }) {\n    // 响应式状态\n    const isPlaying = ref(false)\n    const isShuffled = ref(false)\n    const isRepeating = ref(false)\n    const isMuted = ref(false)\n    const volume = ref(75)\n    const currentTime = ref(0)\n    const currentSongIndex = ref(0)\n    \n    // 播放列表数据\n    const playlist = ref([\n      {\n        title: '夜空中最亮的星',\n        artist: '逃跑计划',\n        album: '世界',\n        duration: 245,\n        cover: null\n      },\n      {\n        title: '成都',\n        artist: '赵雷',\n        album: '无法长大',\n        duration: 327,\n        cover: null\n      },\n      {\n        title: '南山南',\n        artist: '马頔',\n        album: '孤岛',\n        duration: 290,\n        cover: null\n      },\n      {\n        title: '理想',\n        artist: '赵雷',\n        album: '赵小雷',\n        duration: 268,\n        cover: null\n      }\n    ])\n    \n    // 计算属性\n    const currentSong = computed(() => playlist.value[currentSongIndex.value])\n    \n    const progressPercentage = computed(() => {\n      if (currentSong.value.duration === 0) return 0\n      return (currentTime.value / currentSong.value.duration) * 100\n    })\n    \n    const volumeIcon = computed(() => {\n      if (isMuted.value || volume.value === 0) {\n        return 'fas fa-volume-mute'\n      } else if (volume.value < 50) {\n        return 'fas fa-volume-down'\n      } else {\n        return 'fas fa-volume-up'\n      }\n    })\n    \n    // 播放控制\n    let playTimer = null\n    \n    const togglePlay = () => {\n      isPlaying.value = !isPlaying.value\n      \n      if (isPlaying.value) {\n        startPlayTimer()\n      } else {\n        stopPlayTimer()\n      }\n      \n      emit('play-state-changed', {\n        isPlaying: isPlaying.value,\n        song: currentSong.value\n      })\n    }\n    \n    const startPlayTimer = () => {\n      playTimer = setInterval(() => {\n        currentTime.value += 1\n        \n        // 歌曲播放完毕\n        if (currentTime.value >= currentSong.value.duration) {\n          if (isRepeating.value) {\n            currentTime.value = 0\n          } else {\n            nextSong()\n          }\n        }\n      }, 1000)\n    }\n    \n    const stopPlayTimer = () => {\n      if (playTimer) {\n        clearInterval(playTimer)\n        playTimer = null\n      }\n    }\n    \n    const previousSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\n      } else {\n        currentSongIndex.value = currentSongIndex.value > 0 \n          ? currentSongIndex.value - 1 \n          : playlist.value.length - 1\n      }\n      currentTime.value = 0\n      emit('song-changed', currentSong.value)\n    }\n    \n    const nextSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\n      } else {\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 \n          ? currentSongIndex.value + 1 \n          : 0\n      }\n      currentTime.value = 0\n      emit('song-changed', currentSong.value)\n    }\n    \n    const playSong = (index) => {\n      currentSongIndex.value = index\n      currentTime.value = 0\n      if (!isPlaying.value) {\n        togglePlay()\n      }\n      emit('song-changed', currentSong.value)\n    }\n    \n    const seekTo = (event) => {\n      const progressBar = event.currentTarget\n      const rect = progressBar.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const percentage = clickX / rect.width\n      currentTime.value = Math.floor(percentage * currentSong.value.duration)\n    }\n    \n    const toggleShuffle = () => {\n      isShuffled.value = !isShuffled.value\n    }\n    \n    const toggleRepeat = () => {\n      isRepeating.value = !isRepeating.value\n    }\n    \n    const toggleMute = () => {\n      isMuted.value = !isMuted.value\n    }\n    \n    const openFullPlaylist = () => {\n      console.log('打开完整播放列表')\n    }\n    \n    const handleCardClick = () => {\n      emit('card-click', 'music')\n    }\n    \n    // 工具函数\n    const formatTime = (seconds) => {\n      const mins = Math.floor(seconds / 60)\n      const secs = seconds % 60\n      return `${mins}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      console.log('音乐控制卡片已加载')\n    })\n    \n    onUnmounted(() => {\n      stopPlayTimer()\n    })\n    \n    return {\n      isPlaying,\n      isShuffled,\n      isRepeating,\n      isMuted,\n      volume,\n      currentTime,\n      currentSongIndex,\n      playlist,\n      currentSong,\n      progressPercentage,\n      volumeIcon,\n      togglePlay,\n      previousSong,\n      nextSong,\n      playSong,\n      seekTo,\n      toggleShuffle,\n      toggleRepeat,\n      toggleMute,\n      openFullPlaylist,\n      handleCardClick,\n      formatTime\n    }\n  }\n}\n</script>\n\n<style scoped>\n.music-control-card {\n  background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);\n}\n\n.music-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n}\n\n/* 音乐信息区域 */\n.music-info {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n.album-cover {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  border-radius: 10px;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  flex-shrink: 0;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.cover-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  color: var(--card-primary-color, #9b59b6);\n}\n\n.play-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.play-overlay.active {\n  opacity: 1;\n}\n\n.sound-waves {\n  display: flex;\n  gap: 2px;\n  align-items: flex-end;\n}\n\n.wave {\n  width: 3px;\n  height: 10px;\n  background: white;\n  border-radius: 2px;\n  animation: wave 1s infinite ease-in-out;\n}\n\n.song-details {\n  flex: 1;\n  min-width: 0;\n}\n\n.song-title {\n  margin: 0 0 5px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--card-text-color, #ffffff);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.song-artist, .song-album {\n  margin: 0;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 进度区域 */\n.progress-section {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.time-display {\n  display: flex;\n  justify-content: space-between;\n  font-size: 11px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n.progress-bar {\n  position: relative;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  cursor: pointer;\n}\n\n.progress-track {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n}\n\n.progress-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 2px;\n  transition: width 0.1s ease;\n}\n\n.progress-thumb {\n  position: absolute;\n  top: -4px;\n  width: 12px;\n  height: 12px;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 50%;\n  transform: translateX(-50%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.progress-bar:hover .progress-thumb {\n  opacity: 1;\n}\n\n/* 控制区域 */\n.control-section {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.main-controls {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.control-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.control-btn.active {\n  background: var(--card-primary-color, #9b59b6);\n}\n\n.play-btn {\n  width: 50px;\n  height: 50px;\n  background: var(--card-primary-color, #9b59b6);\n  font-size: 18px;\n}\n\n.play-btn:hover {\n  background: var(--card-secondary-color, #e74c3c);\n}\n\n.secondary-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.volume-control {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.volume-btn {\n  width: 32px;\n  height: 32px;\n}\n\n.volume-slider {\n  width: 60px;\n}\n\n.volume-input {\n  width: 100%;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  outline: none;\n  -webkit-appearance: none;\n}\n\n.volume-input::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  width: 12px;\n  height: 12px;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 50%;\n  cursor: pointer;\n}\n\n/* 播放列表预览 */\n.playlist-preview {\n  flex: 1;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 10px;\n  padding: 10px;\n  min-height: 0;\n}\n\n.playlist-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n.playlist-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.playlist-items {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.playlist-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.playlist-item:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.playlist-item.active {\n  background: var(--card-primary-color, #9b59b6);\n}\n\n.item-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.item-title {\n  display: block;\n  font-size: 11px;\n  color: var(--card-text-color, #ffffff);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.item-artist {\n  display: block;\n  font-size: 10px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.7;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.item-duration {\n  font-size: 10px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.7;\n}\n\n/* 动画 */\n@keyframes wave {\n  0%, 100% { height: 10px; }\n  50% { height: 20px; }\n}\n</style>\n"], "mappings": ";;EAcSA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;;;EAOVA,KAAK,EAAC;;;EAMXA,KAAK,EAAC;AAAa;;EAMvBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAY;;EAKpBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAY;;EAgBvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAe;;EAcrBA,KAAK,EAAC;AAAoB;;EASxBA,KAAK,EAAC;AAAgB;;EAIpBA,KAAK,EAAC;AAAe;;EAc3BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC;AAAgB;;;EAOlBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAe;;;uBA1HpCC,YAAA,CA+HWC,mBAAA;IA9HT,WAAS,EAAC,OAAO;IACjBC,IAAI,EAAC,OAAO;IACXC,QAAQ,EAAEC,MAAA,CAAAD,QAAQ;IAClBE,KAAK,EAAED,MAAA,CAAAC,KAAK;IACZ,cAAY,EAAED,MAAA,CAAAE,WAAW;IACzBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,IAAI;IACf,aAAW,EAAE,KAAK;IAClBC,OAAK,EAAEC,MAAA,CAAAC,eAAe;IACvBb,KAAK,EAAC;;sBAEN,MAiHM,CAjHNc,mBAAA,CAiHM,OAjHNC,UAiHM,GAhHJC,mBAAA,eAAkB,EAClBF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAiBM,OAjBNI,UAiBM,GAfIN,MAAA,CAAAO,WAAW,CAACC,KAAK,I,cADzBC,mBAAA,CAKE;;MAHCC,GAAG,EAAEV,MAAA,CAAAO,WAAW,CAACC,KAAK;MACtBG,GAAG,EAAEX,MAAA,CAAAO,WAAW,CAACX,KAAK;MACvBR,KAAK,EAAC;0DAERqB,mBAAA,CAEM,OAFNG,UAEM,EAAAC,MAAA,QAAAA,MAAA,OADJX,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,0B,KAGzBgB,mBAAA,aAAgB,EAChBF,mBAAA,CAIM;MAJDd,KAAK,EAAA0B,eAAA,EAAC,cAAc;QAAAC,MAAA,EAAmBf,MAAA,CAAAgB;MAAS;QACnDd,mBAAA,CAEM,OAFNe,UAEM,I,cADJR,mBAAA,CAA2FS,SAAA,QAAAC,WAAA,CAA7D,CAAC,EAANC,CAAC;aAA1BlB,mBAAA,CAA2F;QAAtFd,KAAK,EAAC,MAAM;QAAiBiC,GAAG,EAAED,CAAC;QAAGE,KAAK,EAAAC,eAAA;UAAAC,cAAA,KAAuBJ,CAAC;QAAA;;yDAK9ElB,mBAAA,CAIM,OAJNuB,UAIM,GAHJvB,mBAAA,CAAmD,MAAnDwB,UAAmD,EAAAC,gBAAA,CAAzB3B,MAAA,CAAAO,WAAW,CAACX,KAAK,kBAC3CM,mBAAA,CAAmD,KAAnD0B,UAAmD,EAAAD,gBAAA,CAAzB3B,MAAA,CAAAO,WAAW,CAACsB,MAAM,kBAC5C3B,mBAAA,CAAiD,KAAjD4B,WAAiD,EAAAH,gBAAA,CAAxB3B,MAAA,CAAAO,WAAW,CAACwB,KAAK,iB,KAI9C3B,mBAAA,UAAa,EACbF,mBAAA,CAgBM,OAhBN8B,WAgBM,GAfJ9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ/B,mBAAA,CAA+D,QAA/DgC,WAA+D,EAAAP,gBAAA,CAAjC3B,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAoC,WAAW,mBACpDlC,mBAAA,CAAsE,QAAtEmC,WAAsE,EAAAV,gBAAA,CAA1C3B,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAO,WAAW,CAAC+B,QAAQ,kB,GAE7DpC,mBAAA,CAUM;MAVDd,KAAK,EAAC,cAAc;MAAEW,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAwC,MAAA,IAAAxC,MAAA,CAAAwC,MAAA,IAAAD,IAAA,CAAM;oCACtCrC,mBAAA,CAAkC;MAA7Bd,KAAK,EAAC;IAAgB,4BAC3Bc,mBAAA,CAGO;MAFLd,KAAK,EAAC,eAAe;MACpBkC,KAAK,EAAAC,eAAA;QAAAkB,KAAA,KAAczC,MAAA,CAAA0C,kBAAkB;MAAA;6BAExCxC,mBAAA,CAGO;MAFLd,KAAK,EAAC,gBAAgB;MACrBkC,KAAK,EAAAC,eAAA;QAAAoB,IAAA,KAAa3C,MAAA,CAAA0C,kBAAkB;MAAA;iCAK3CtC,mBAAA,UAAa,EACbF,mBAAA,CAuCM,OAvCN0C,WAuCM,GAtCJ1C,mBAAA,CAYM,OAZN2C,WAYM,GAXJ3C,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAA8C,YAAA,IAAA9C,MAAA,CAAA8C,YAAA,IAAAP,IAAA,CAAY;MAAEnD,KAAK,EAAC;oCAClCc,mBAAA,CAAoC;MAAjCd,KAAK,EAAC;IAAsB,0B,IAGjCc,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAA+C,UAAA,IAAA/C,MAAA,CAAA+C,UAAA,IAAAR,IAAA,CAAU;MAAEnD,KAAK,EAAC;QAChCc,mBAAA,CAA2D;MAAvDd,KAAK,EAAA0B,eAAA,CAAEd,MAAA,CAAAgB,SAAS;+BAGtBd,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAgD,QAAA,IAAAhD,MAAA,CAAAgD,QAAA,IAAAT,IAAA,CAAQ;MAAEnD,KAAK,EAAC;oCAC9Bc,mBAAA,CAAmC;MAAhCd,KAAK,EAAC;IAAqB,0B,MAIlCc,mBAAA,CAuBM,OAvBN+C,WAuBM,GAtBJ/C,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAkD,aAAA,IAAAlD,MAAA,CAAAkD,aAAA,IAAAX,IAAA,CAAa;MAAGnD,KAAK,EAAA0B,eAAA;QAAAC,MAAA,EAA4Bf,MAAA,CAAAmD;MAAU;oCACzEjD,mBAAA,CAA6B;MAA1Bd,KAAK,EAAC;IAAe,0B,mBAG1Bc,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAoD,YAAA,IAAApD,MAAA,CAAAoD,YAAA,IAAAb,IAAA,CAAY;MAAGnD,KAAK,EAAA0B,eAAA;QAAAC,MAAA,EAA4Bf,MAAA,CAAAqD;MAAW;oCACzEnD,mBAAA,CAA2B;MAAxBd,KAAK,EAAC;IAAa,0B,mBAGxBc,mBAAA,CAaM,OAbNoD,WAaM,GAZJpD,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAuD,UAAA,IAAAvD,MAAA,CAAAuD,UAAA,IAAAhB,IAAA,CAAU;MAAEnD,KAAK,EAAC;QAChCc,mBAAA,CAA2B;MAAvBd,KAAK,EAAA0B,eAAA,CAAEd,MAAA,CAAAwD,UAAU;+BAEvBtD,mBAAA,CAQM,OARNuD,WAQM,G,gBAPJvD,mBAAA,CAME;MALAwD,IAAI,EAAC,OAAO;MACZC,GAAG,EAAC,GAAG;MACPC,GAAG,EAAC,KAAK;iEACA5D,MAAA,CAAA6D,MAAM,GAAAC,MAAA;MACf1E,KAAK,EAAC;mDADGY,MAAA,CAAA6D,MAAM,E,WAQzBzD,mBAAA,YAAe,EACfF,mBAAA,CAqBM,OArBN6D,WAqBM,GApBJ7D,mBAAA,CAKM,OALN8D,WAKM,G,4BAJJ9D,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAiE,gBAAA,IAAAjE,MAAA,CAAAiE,gBAAA,IAAA1B,IAAA,CAAgB;MAAEnD,KAAK,EAAC;oCACtCc,mBAAA,CAA2B;MAAxBd,KAAK,EAAC;IAAa,0B,MAG1Bc,mBAAA,CAaM,OAbNgE,WAaM,I,kBAZJzD,mBAAA,CAWMS,SAAA,QAAAC,WAAA,CAVoBnB,MAAA,CAAAmE,QAAQ,CAACC,KAAK,SAA9BC,IAAI,EAAEC,KAAK;2BADrB7D,mBAAA,CAWM;QATHY,GAAG,EAAEiD,KAAK;QACVlF,KAAK,EAAA0B,eAAA;UAAAC,MAAA,EAA8Bf,MAAA,CAAAuE,gBAAgB,KAAKD;QAAK;QAC7DvE,OAAK,EAAA+D,MAAA,IAAE9D,MAAA,CAAAwE,QAAQ,CAACF,KAAK;UAEtBpE,mBAAA,CAGM,OAHNuE,WAGM,GAFJvE,mBAAA,CAAgD,QAAhDwE,WAAgD,EAAA/C,gBAAA,CAApB0C,IAAI,CAACzE,KAAK,kBACtCM,mBAAA,CAAkD,QAAlDyE,WAAkD,EAAAhD,gBAAA,CAArB0C,IAAI,CAACxC,MAAM,iB,GAE1C3B,mBAAA,CAAgE,OAAhE0E,WAAgE,EAAAjD,gBAAA,CAAlC3B,MAAA,CAAAmC,UAAU,CAACkC,IAAI,CAAC/B,QAAQ,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}