declare namespace gsap.core {

  class Tween extends Animation {
    
    data: any;
    vars: TweenVars;
    ratio: number;

    constructor(targets: TweenTarget, vars: TweenVars, time?: number);
    constructor(targets: TweenTarget, duration: number, vars: TweenVars);
    
    /**
     * **Deprecated method.** Use `gsap.to()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.to()
     */
    static to(targets: TweenTarget, duration: number, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.to()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween}  Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.to()
     */
    static to(targets: TweenTarget, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.from()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.from()
     */
    static from(targets: TweenTarget, duration: number, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.from()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.from()
     */
    static from(targets: TweenTarget, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.fromTo()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} fromVars
     * @param {TweenVars} toVars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.fromTo()
     */
    static fromTo(targets: TweenTarget, duration: number, fromVars: TweenVars, toVars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.fromTo()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} fromVars
     * @param {TweenVars} toVars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.fromTo()
     */
    static fromTo(targets: TweenTarget, fromVars: TweenVars, toVars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.set()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.set()
     */
    static set(targets: TweenTarget, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.from()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.from()
     */
    static staggerFrom(targets: TweenTarget, duration: number, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.from()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.from()
     */
    static staggerFrom(targets: TweenTarget, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.fromTo()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.fromTo()
     */
    static staggerFromTo(targets: TweenTarget, duration: number, fromVars: TweenVars, toVars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.fromTo()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.fromTo()
     */
    static staggerFromTo(targets: TweenTarget, fromVars: TweenVars, toVars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.to()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {number} duration
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.to()
     */
    static staggerTo(targets: TweenTarget, duration: number, vars: TweenVars): Tween;

    /**
     * **Deprecated method.** Use `gsap.to()` instead.
     * 
     * @deprecated since 3.0.0
     * @static
     * @param {TweenTarget} targets
     * @param {TweenVars} vars
     * @returns {Tween} Tween instance
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/gsap.to()
     */
    static staggerTo(targets: TweenTarget, vars: TweenVars): Tween;

    /**
     * Kills the parts of the tween specified. 
     * To kill means to immediately stop the tween, remove it from its parent timeline, and release it for garbage collection.
     *
     * ```js
     * // kills the entire tween
     * tween.kill(); 
     * 
     * // kill all parts of the tween related to the target "myObject" (if the tween has multiple targets, the others will not be affected):
     * tween.kill(myObject);
     * 
     * // kill only the "x" and "y" properties of the tween (all targets):
     * tween.kill(null, "x,y");
     * 
     * // kill only the "x" and "y" properties of tween of the target "myObject":
     * tween.kill(myObject, "x,y");
     * 
     * // kill only the "opacity" properties of the tween of the targets "myObject1" and "myObject2":
     * tween.kill([myObject1, myObject2], "opacity");
     * ```
     *
     * @returns {Tween} The tween
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/Tween/kill()
     */
    kill(target?: object, propertiesList?: string): this;

    /**
     * Redirects a property [that's already being animated by the tween] to a new value and restarts the tween in a performant way.
     * A more common technique is to use gsap.quickTo() which uses resetTo() under the hood.
     *
     * ```js
     * tween.resetTo("x", 200);
     * ```
     *
     * @param {string} property
     * @param {number} value
     * @param {number} start
     * @param {boolean} startIsRelative
     * @returns {Tween} The tween
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/Tween/resetTo()
     */
    resetTo(property: string, value: number, start?: number, startIsRelative?: boolean): this;

    /**
     * Returns an array of all of the tween's targets.
     *
     * ```js
     * tween.targets(); 
     * ```
     *
     * @returns {T[]} The array of targets
     * @memberof Tween
     * @link https://greensock.com/docs/v3/GSAP/Tween/targets()
     */
    targets<T>(): T[];
  }
}
