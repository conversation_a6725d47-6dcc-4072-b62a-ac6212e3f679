{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelCheckbox as _vModelCheckbox, withDirectives as _withDirectives, createTextVNode as _createTextVNode, vModelSelect as _vModelSelect, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dynamic-island\"\n};\nconst _hoisted_2 = {\n  class: \"island-status\"\n};\nconst _hoisted_3 = {\n  class: \"time\"\n};\nconst _hoisted_4 = {\n  class: \"scene-indicator\"\n};\nconst _hoisted_5 = {\n  class: \"vpa-widgets-demo\"\n};\nconst _hoisted_6 = {\n  class: \"driving-zone-content\"\n};\nconst _hoisted_7 = {\n  class: \"navigation-placeholder\"\n};\nconst _hoisted_8 = {\n  class: \"layout-info\"\n};\nconst _hoisted_9 = {\n  class: \"vpa-large-panel\"\n};\nconst _hoisted_10 = {\n  class: \"mode-toggle\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"test-mode-overlay\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"debug-panel\"\n};\nconst _hoisted_13 = {\n  class: \"debug-controls\"\n};\nconst _hoisted_14 = {\n  class: \"debug-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_DynamicWallpaperManager = _resolveComponent(\"DynamicWallpaperManager\");\n  const _component_VPAAvatarWidget = _resolveComponent(\"VPAAvatarWidget\");\n  const _component_VPAInteractionPanel = _resolveComponent(\"VPAInteractionPanel\");\n  const _component_SceneManager = _resolveComponent(\"SceneManager\");\n  const _component_GridSystem16x9 = _resolveComponent(\"GridSystem16x9\");\n  const _component_VPADigitalHuman = _resolveComponent(\"VPADigitalHuman\");\n  const _component_MainLayoutContainer = _resolveComponent(\"MainLayoutContainer\");\n  const _component_TestSceneGeneration = _resolveComponent(\"TestSceneGeneration\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    id: \"app\",\n    style: _normalizeStyle($setup.appStyles)\n  }, [_createCommentVNode(\" 动态壁纸背景 \"), _createVNode(_component_DynamicWallpaperManager, {\n    \"scene-prompt\": $setup.currentWallpaperPrompt,\n    \"auto-generate\": true,\n    \"enable-config\": true,\n    onWallpaperChanged: $setup.handleWallpaperChanged,\n    onColorsExtracted: $setup.handleColorsExtracted\n  }, null, 8 /* PROPS */, [\"scene-prompt\", \"onWallpaperChanged\", \"onColorsExtracted\"]), _createCommentVNode(\" 主布局容器 - 16:9网格系统 \"), _createVNode(_component_MainLayoutContainer, {\n    scene: $setup.currentScene\n  }, {\n    \"dynamic-island\": _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n      class: \"island-title\"\n    }, \"AI-HMI 智能座舱\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"span\", _hoisted_3, _toDisplayString($setup.currentTime), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_4, _toDisplayString($setup.sceneText), 1 /* TEXT */)])])]),\n    \"card-zone\": _withCtx(() => [_createVNode(_component_GridSystem16x9, {\n      mode: \"standard\",\n      \"show-grid-lines\": $setup.showDebugGrid,\n      class: \"card-zone-grid\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" VPA陪伴小窗组件测试 \"), _createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" VPA陪伴小窗 - 2x2 \"), _createVNode(_component_VPAAvatarWidget, {\n        size: \"2x2\",\n        position: {\n          x: 0,\n          y: 0\n        },\n        interactive: true,\n        onClick: _ctx.handleVPAClick,\n        onModeChange: _ctx.handleModeChange,\n        onInteraction: _ctx.handleVPAInteraction\n      }, null, 8 /* PROPS */, [\"onClick\", \"onModeChange\", \"onInteraction\"]), _createCommentVNode(\" VPA陪伴小窗 - 2x4 \"), _createVNode(_component_VPAAvatarWidget, {\n        size: \"2x4\",\n        position: {\n          x: 2,\n          y: 0\n        },\n        interactive: true,\n        onClick: _ctx.handleVPAClick,\n        onModeChange: _ctx.handleModeChange,\n        onInteraction: _ctx.handleVPAInteraction\n      }, null, 8 /* PROPS */, [\"onClick\", \"onModeChange\", \"onInteraction\"]), _createCommentVNode(\" VPA陪伴小窗 - 3x3 \"), _createVNode(_component_VPAAvatarWidget, {\n        size: \"3x3\",\n        position: {\n          x: 5,\n          y: 0\n        },\n        interactive: true,\n        onClick: _ctx.handleVPAClick,\n        onModeChange: _ctx.handleModeChange,\n        onInteraction: _ctx.handleVPAInteraction\n      }, null, 8 /* PROPS */, [\"onClick\", \"onModeChange\", \"onInteraction\"]), _createCommentVNode(\" VPA交互面板 - 4x4 \"), _createVNode(_component_VPAInteractionPanel, {\n        size: \"4x4\",\n        position: {\n          x: 0,\n          y: 4\n        },\n        \"show-emotion-indicator\": true,\n        onModeChange: _ctx.handleModeChange,\n        onInteraction: _ctx.handleVPAInteraction,\n        onQuickAction: _ctx.handleQuickAction\n      }, null, 8 /* PROPS */, [\"onModeChange\", \"onInteraction\", \"onQuickAction\"])]), _createCommentVNode(\" 场景管理器 - 使用新的16:9布局 \"), _createVNode(_component_SceneManager, {\n        \"initial-scene\": $setup.initialScene,\n        \"current-scene\": $setup.currentScene,\n        \"show-indicator\": true,\n        \"auto-switch\": $setup.autoSwitchEnabled,\n        \"theme-colors\": $setup.themeColors,\n        onSceneChanged: $setup.handleSceneChanged,\n        onWallpaperPromptReady: $setup.handleWallpaperPrompt\n      }, null, 8 /* PROPS */, [\"initial-scene\", \"current-scene\", \"auto-switch\", \"theme-colors\", \"onSceneChanged\", \"onWallpaperPromptReady\"])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"show-grid-lines\"])]),\n    \"driving-zone\": _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"🗺️\", -1 /* CACHED */)), _cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"导航地图\", -1 /* CACHED */)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"16:9布局 - 驾驶信息区\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"small\", null, \"网格: \" + _toDisplayString($setup.layoutStore.gridColumns) + \"x\" + _toDisplayString($setup.layoutStore.gridRows), 1 /* TEXT */)])]), _createCommentVNode(\" VPA交互面板 - 8x9 (大尺寸) \"), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_VPAInteractionPanel, {\n      size: \"8x9\",\n      position: {\n        x: 8,\n        y: 0\n      },\n      \"show-emotion-indicator\": true,\n      onModeChange: _ctx.handleModeChange,\n      onInteraction: _ctx.handleVPAInteraction,\n      onQuickAction: _ctx.handleQuickAction\n    }, null, 8 /* PROPS */, [\"onModeChange\", \"onInteraction\", \"onQuickAction\"])])])]),\n    vpa: _withCtx(() => [_createVNode(_component_VPADigitalHuman, {\n      size: \"medium\",\n      \"show-info-panel\": true,\n      interactive: true\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"scene\"]), _createCommentVNode(\" 测试模式切换 \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleMode && $setup.toggleMode(...args)),\n    class: \"toggle-btn\"\n  }, _toDisplayString($setup.isTestMode ? '🚗 正常模式' : '🧪 测试模式'), 1 /* TEXT */)]), _createCommentVNode(\" 测试模式覆盖层 \"), $setup.isTestMode ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_TestSceneGeneration, {\n    \"theme-colors\": $setup.themeColors,\n    onColorsExtracted: $setup.handleColorsExtracted\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onColorsExtracted\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 调试控制面板 \"), $setup.showDebugPanel ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, \"🔧 调试面板\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"label\", null, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.showDebugGrid = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $setup.showDebugGrid]]), _cache[9] || (_cache[9] = _createTextVNode(\" 显示网格线 \", -1 /* CACHED */))]), _createElementVNode(\"label\", null, [_cache[11] || (_cache[11] = _createTextVNode(\" 场景: \", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.currentScene = $event),\n    onChange: _cache[3] || (_cache[3] = (...args) => $setup.handleSceneChange && $setup.handleSceneChange(...args))\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"option\", {\n    value: \"family\"\n  }, \"家庭\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"focus\"\n  }, \"专注\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"minimal\"\n  }, \"简约\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"entertainment\"\n  }, \"娱乐\", -1 /* CACHED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $setup.currentScene]])]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"small\", null, \"网格: \" + _toDisplayString($setup.layoutStore.gridColumns) + \"x\" + _toDisplayString($setup.layoutStore.gridRows), 1 /* TEXT */), _createElementVNode(\"small\", null, \"间距: \" + _toDisplayString($setup.layoutStore.gridGap) + \"px\", 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 调试按钮 \"), _createElementVNode(\"button\", {\n    class: \"debug-toggle\",\n    onClick: _cache[4] || (_cache[4] = $event => $setup.showDebugPanel = !$setup.showDebugPanel)\n  }, \" 🔧 \")], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "id", "style", "_normalizeStyle", "$setup", "appStyles", "_createCommentVNode", "_createVNode", "_component_DynamicWallpaperManager", "currentWallpaperPrompt", "onWallpaperChanged", "handleWallpaperChanged", "onColorsExtracted", "handleColorsExtracted", "_component_MainLayoutContainer", "scene", "currentScene", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "currentTime", "_hoisted_4", "sceneText", "_component_GridSystem16x9", "mode", "showDebugGrid", "default", "_hoisted_5", "_component_VPAAvatarWidget", "size", "position", "x", "y", "interactive", "onClick", "_ctx", "handleVPAClick", "onModeChange", "handleModeChange", "onInteraction", "handleVPAInteraction", "_component_VPAInteractionPanel", "onQuickAction", "handleQuickAction", "_component_SceneManager", "initialScene", "autoSwitchEnabled", "themeColors", "onSceneChanged", "handleSceneChanged", "onWallpaperPromptReady", "handleWallpaperPrompt", "_hoisted_6", "_hoisted_7", "_hoisted_8", "layoutStore", "gridColumns", "gridRows", "_hoisted_9", "vpa", "_component_VPADigitalHuman", "_hoisted_10", "_cache", "args", "toggleMode", "isTestMode", "_hoisted_11", "_component_TestSceneGeneration", "showDebugPanel", "_hoisted_12", "_hoisted_13", "type", "$event", "onChange", "handleSceneChange", "value", "_hoisted_14", "gridGap"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\" :style=\"appStyles\">\r\n    <!-- 动态壁纸背景 -->\r\n    <DynamicWallpaperManager\r\n      :scene-prompt=\"currentWallpaperPrompt\"\r\n      :auto-generate=\"true\"\r\n      :enable-config=\"true\"\r\n      @wallpaper-changed=\"handleWallpaperChanged\"\r\n      @colors-extracted=\"handleColorsExtracted\"\r\n    />\r\n\r\n    <!-- 主布局容器 - 16:9网格系统 -->\r\n    <MainLayoutContainer :scene=\"currentScene\">\r\n      <!-- 灵动岛 -->\r\n      <template #dynamic-island>\r\n        <div class=\"dynamic-island\">\r\n          <span class=\"island-title\">AI-HMI 智能座舱</span>\r\n          <div class=\"island-status\">\r\n            <span class=\"time\">{{ currentTime }}</span>\r\n            <span class=\"scene-indicator\">{{ sceneText }}</span>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 卡片区域 -->\r\n      <template #card-zone>\r\n        <GridSystem16x9\r\n          mode=\"standard\"\r\n          :show-grid-lines=\"showDebugGrid\"\r\n          class=\"card-zone-grid\"\r\n        >\r\n          <template #default>\r\n            <!-- VPA陪伴小窗组件测试 -->\r\n            <div class=\"vpa-widgets-demo\">\r\n              <!-- VPA陪伴小窗 - 2x2 -->\r\n              <VPAAvatarWidget\r\n                size=\"2x2\"\r\n                :position=\"{ x: 0, y: 0 }\"\r\n                :interactive=\"true\"\r\n                @click=\"handleVPAClick\"\r\n                @mode-change=\"handleModeChange\"\r\n                @interaction=\"handleVPAInteraction\"\r\n              />\r\n\r\n              <!-- VPA陪伴小窗 - 2x4 -->\r\n              <VPAAvatarWidget\r\n                size=\"2x4\"\r\n                :position=\"{ x: 2, y: 0 }\"\r\n                :interactive=\"true\"\r\n                @click=\"handleVPAClick\"\r\n                @mode-change=\"handleModeChange\"\r\n                @interaction=\"handleVPAInteraction\"\r\n              />\r\n\r\n              <!-- VPA陪伴小窗 - 3x3 -->\r\n              <VPAAvatarWidget\r\n                size=\"3x3\"\r\n                :position=\"{ x: 5, y: 0 }\"\r\n                :interactive=\"true\"\r\n                @click=\"handleVPAClick\"\r\n                @mode-change=\"handleModeChange\"\r\n                @interaction=\"handleVPAInteraction\"\r\n              />\r\n\r\n              <!-- VPA交互面板 - 4x4 -->\r\n              <VPAInteractionPanel\r\n                size=\"4x4\"\r\n                :position=\"{ x: 0, y: 4 }\"\r\n                :show-emotion-indicator=\"true\"\r\n                @mode-change=\"handleModeChange\"\r\n                @interaction=\"handleVPAInteraction\"\r\n                @quick-action=\"handleQuickAction\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 场景管理器 - 使用新的16:9布局 -->\r\n            <SceneManager\r\n              :initial-scene=\"initialScene\"\r\n              :current-scene=\"currentScene\"\r\n              :show-indicator=\"true\"\r\n              :auto-switch=\"autoSwitchEnabled\"\r\n              :theme-colors=\"themeColors\"\r\n              @scene-changed=\"handleSceneChanged\"\r\n              @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n            />\r\n          </template>\r\n        </GridSystem16x9>\r\n      </template>\r\n\r\n      <!-- 驾驶区域 -->\r\n      <template #driving-zone>\r\n        <div class=\"driving-zone-content\">\r\n          <div class=\"navigation-placeholder\">\r\n            <div class=\"nav-icon\">🗺️</div>\r\n            <h3>导航地图</h3>\r\n            <p>16:9布局 - 驾驶信息区</p>\r\n            <div class=\"layout-info\">\r\n              <small>网格: {{ layoutStore.gridColumns }}x{{ layoutStore.gridRows }}</small>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- VPA交互面板 - 8x9 (大尺寸) -->\r\n          <div class=\"vpa-large-panel\">\r\n            <VPAInteractionPanel\r\n              size=\"8x9\"\r\n              :position=\"{ x: 8, y: 0 }\"\r\n              :show-emotion-indicator=\"true\"\r\n              @mode-change=\"handleModeChange\"\r\n              @interaction=\"handleVPAInteraction\"\r\n              @quick-action=\"handleQuickAction\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- VPA数字人 -->\r\n      <template #vpa>\r\n        <VPADigitalHuman\r\n          size=\"medium\"\r\n          :show-info-panel=\"true\"\r\n          :interactive=\"true\"\r\n        />\r\n      </template>\r\n    </MainLayoutContainer>\r\n\r\n    <!-- 测试模式切换 -->\r\n    <div class=\"mode-toggle\">\r\n      <button @click=\"toggleMode\" class=\"toggle-btn\">\r\n        {{ isTestMode ? '🚗 正常模式' : '🧪 测试模式' }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 测试模式覆盖层 -->\r\n    <div v-if=\"isTestMode\" class=\"test-mode-overlay\">\r\n      <TestSceneGeneration\r\n        :theme-colors=\"themeColors\"\r\n        @colors-extracted=\"handleColorsExtracted\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 调试控制面板 -->\r\n    <div v-if=\"showDebugPanel\" class=\"debug-panel\">\r\n      <h4>🔧 调试面板</h4>\r\n      <div class=\"debug-controls\">\r\n        <label>\r\n          <input type=\"checkbox\" v-model=\"showDebugGrid\" />\r\n          显示网格线\r\n        </label>\r\n        <label>\r\n          场景:\r\n          <select v-model=\"currentScene\" @change=\"handleSceneChange\">\r\n            <option value=\"family\">家庭</option>\r\n            <option value=\"focus\">专注</option>\r\n            <option value=\"minimal\">简约</option>\r\n            <option value=\"entertainment\">娱乐</option>\r\n          </select>\r\n        </label>\r\n        <div class=\"debug-info\">\r\n          <small>网格: {{ layoutStore.gridColumns }}x{{ layoutStore.gridRows }}</small>\r\n          <small>间距: {{ layoutStore.gridGap }}px</small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 调试按钮 -->\r\n    <button\r\n      class=\"debug-toggle\"\r\n      @click=\"showDebugPanel = !showDebugPanel\"\r\n    >\r\n      🔧\r\n    </button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useLayoutStore } from '@/store/modules/layout'\r\nimport { useVPAStore } from '@/store/modules/vpa'\r\nimport MainLayoutContainer from '@/components/layout/MainLayoutContainer.vue'\r\nimport GridSystem16x9 from '@/components/layout/GridSystem16x9.vue'\r\nimport VPADigitalHuman from '@/components/vpa/VPADigitalHuman.vue'\r\nimport VPAAvatarWidget from '@/components/vpa/VPAAvatarWidget.vue'\r\nimport VPAInteractionPanel from '@/components/vpa/VPAInteractionPanel.vue'\r\nimport SceneManager from './components/SceneManager.vue'\r\nimport TestSceneGeneration from './components/TestSceneGeneration.vue'\r\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    MainLayoutContainer,\r\n    GridSystem16x9,\r\n    VPADigitalHuman,\r\n    VPAAvatarWidget,\r\n    VPAInteractionPanel,\r\n    SceneManager,\r\n    TestSceneGeneration,\r\n    DynamicWallpaperManager\r\n  },\r\n\r\n  setup() {\r\n    const layoutStore = useLayoutStore()\r\n    const vpaStore = useVPAStore()\r\n\r\n    // 响应式状态\r\n    const isTestMode = ref(false)\r\n    const currentScene = ref('family')\r\n    const initialScene = ref('default')\r\n    const autoSwitchEnabled = ref(false)\r\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围')\r\n    const themeColors = ref(null)\r\n    const currentTime = ref('')\r\n    const showDebugPanel = ref(false)\r\n    const showDebugGrid = ref(false)\r\n\r\n    // 计算属性\r\n    const appStyles = computed(() => ({\r\n      ...layoutStore.gridCSSVariables,\r\n      '--current-scene': currentScene.value\r\n    }))\r\n\r\n    const sceneText = computed(() => {\r\n      const scenes = {\r\n        'family': '家庭',\r\n        'focus': '专注',\r\n        'minimal': '简约',\r\n        'entertainment': '娱乐'\r\n      }\r\n      return scenes[currentScene.value] || '通用'\r\n    })\r\n\r\n    // 方法\r\n    const updateTime = () => {\r\n      const now = new Date()\r\n      currentTime.value = now.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      })\r\n    }\r\n\r\n    const toggleMode = () => {\r\n      isTestMode.value = !isTestMode.value\r\n    }\r\n\r\n    const handleSceneChange = (newScene) => {\r\n      if (typeof newScene === 'string') {\r\n        currentScene.value = newScene\r\n      } else if (newScene && newScene.target) {\r\n        currentScene.value = newScene.target.value\r\n      }\r\n\r\n      vpaStore.updateScene(currentScene.value)\r\n      layoutStore.setLayoutMode(currentScene.value)\r\n\r\n      console.log('场景切换到:', currentScene.value)\r\n    }\r\n\r\n    const handleWallpaperPrompt = (promptData) => {\r\n      console.log('收到壁纸提示词:', promptData)\r\n\r\n      // 处理不同格式的提示词数据\r\n      if (typeof promptData === 'string') {\r\n        currentWallpaperPrompt.value = promptData\r\n      } else if (promptData && promptData.prompt) {\r\n        // 使用生成的情感化提示词\r\n        currentWallpaperPrompt.value = promptData.prompt\r\n\r\n        // 记录详细的生成信息\r\n        console.log('🎨 情感化提示词生成详情:', {\r\n          prompt: promptData.prompt,\r\n          scene: promptData.scene?.name,\r\n          context: promptData.context,\r\n          originalPrompt: promptData.originalPrompt\r\n        })\r\n      }\r\n    }\r\n\r\n    const handleSceneChanged = (event) => {\r\n      console.log('场景切换事件:', event)\r\n\r\n      // 兼容旧的事件格式\r\n      if (event && event.sceneName) {\r\n        handleSceneChange(event.sceneName)\r\n      }\r\n\r\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\r\n      console.log('等待情感化提示词生成...')\r\n    }\r\n\r\n    const handleWallpaperChanged = (wallpaper) => {\r\n      console.log('壁纸已更换:', wallpaper)\r\n    }\r\n\r\n    const handleColorsExtracted = (colors) => {\r\n      console.log('颜色已提取:', colors)\r\n      themeColors.value = colors\r\n    }\r\n\r\n    // 页面加载时的欢迎和初始化\r\n    const welcomeUser = async () => {\r\n      console.log('欢迎使用AI-HMI智能场景系统 - 16:9布局版本')\r\n\r\n      // 根据时间设置初始场景\r\n      const now = new Date()\r\n      const hour = now.getHours()\r\n\r\n      if (hour >= 7 && hour <= 9) {\r\n        initialScene.value = 'morningCommuteFamily'\r\n        currentScene.value = 'family'\r\n      } else if (hour >= 17 && hour <= 19) {\r\n        initialScene.value = 'eveningCommute'\r\n        currentScene.value = 'focus'\r\n      } else if (hour >= 20 || hour <= 6) {\r\n        initialScene.value = 'rainyNight'\r\n        currentScene.value = 'minimal'\r\n      } else {\r\n        currentScene.value = 'family'\r\n      }\r\n    }\r\n\r\n    // 生命周期\r\n    let timeInterval = null\r\n\r\n    onMounted(() => {\r\n      // 初始化布局和VPA\r\n      layoutStore.initializeLayout()\r\n      vpaStore.initializeVPA()\r\n\r\n      // 更新时间\r\n      updateTime()\r\n      timeInterval = setInterval(updateTime, 1000)\r\n\r\n      // 欢迎用户并设置初始场景\r\n      welcomeUser()\r\n\r\n      // 设置初始场景到stores\r\n      handleSceneChange(currentScene.value)\r\n    })\r\n\r\n    onUnmounted(() => {\r\n      if (timeInterval) {\r\n        clearInterval(timeInterval)\r\n      }\r\n    })\r\n\r\n    return {\r\n      layoutStore,\r\n      isTestMode,\r\n      currentScene,\r\n      initialScene,\r\n      autoSwitchEnabled,\r\n      currentWallpaperPrompt,\r\n      themeColors,\r\n      currentTime,\r\n      showDebugPanel,\r\n      showDebugGrid,\r\n      appStyles,\r\n      sceneText,\r\n      toggleMode,\r\n      handleSceneChange,\r\n      handleWallpaperPrompt,\r\n      handleSceneChanged,\r\n      handleWallpaperChanged,\r\n      handleColorsExtracted\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式重置 */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#app {\r\n  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n/* 灵动岛样式 */\r\n.dynamic-island {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 22px;\r\n  padding: 8px 24px;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  min-width: 300px;\r\n  height: 44px;\r\n}\r\n\r\n.island-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.island-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  font-size: 12px;\r\n}\r\n\r\n.time {\r\n  font-weight: 500;\r\n}\r\n\r\n.scene-indicator {\r\n  background: rgba(74, 144, 226, 0.8);\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  font-size: 11px;\r\n}\r\n\r\n/* 卡片区域网格 */\r\n.card-zone-grid {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 驾驶区域 */\r\n.driving-zone-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 15px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.navigation-placeholder {\r\n  text-align: center;\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.navigation-placeholder h3 {\r\n  margin-bottom: 5px;\r\n  font-size: 18px;\r\n}\r\n\r\n.navigation-placeholder p {\r\n  font-size: 14px;\r\n  opacity: 0.7;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.layout-info {\r\n  margin-top: 10px;\r\n  padding: 5px 10px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 模式切换按钮 */\r\n.mode-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 9999;\r\n}\r\n\r\n.toggle-btn {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #333;\r\n  font-weight: 600;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 测试模式覆盖层 */\r\n.test-mode-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 8000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 调试面板 */\r\n.debug-panel {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  color: white;\r\n  font-size: 12px;\r\n  z-index: 10000;\r\n  min-width: 200px;\r\n}\r\n\r\n.debug-panel h4 {\r\n  margin-bottom: 10px;\r\n  color: #4a90e2;\r\n}\r\n\r\n.debug-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.debug-controls label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.debug-controls input,\r\n.debug-controls select {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: white;\r\n  padding: 4px 8px;\r\n}\r\n\r\n.debug-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  margin-top: 8px;\r\n  padding-top: 8px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.debug-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 20px;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(20px);\r\n  color: white;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n  z-index: 9999;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debug-toggle:hover {\r\n  background: rgba(0, 0, 0, 0.9);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .dynamic-island {\r\n    min-width: 250px;\r\n    padding: 6px 20px;\r\n  }\r\n\r\n  .island-title {\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .dynamic-island {\r\n    min-width: 200px;\r\n    padding: 4px 16px;\r\n  }\r\n\r\n  .island-title {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .debug-panel {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 10px;\r\n    min-width: 150px;\r\n  }\r\n}\r\n\r\n/* 全局字体引入 */\r\n@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\r\n\r\n/* 图标库引入 */\r\n@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');\r\n</style>\r\n"], "mappings": ";;EAeaA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAiB;;EAcxBA,KAAK,EAAC;AAAkB;;EA0D5BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAwB;;EAI5BA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAiB;;EAwB7BA,KAAK,EAAC;AAAa;;;EAODA,KAAK,EAAC;;;;EAQFA,KAAK,EAAC;;;EAE1BA,KAAK,EAAC;AAAgB;;EAcpBA,KAAK,EAAC;AAAY;;;;;;;;;;uBA5J7BC,mBAAA,CA0KM;IA1KDC,EAAE,EAAC,KAAK;IAAEC,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,SAAS;MAC7BC,mBAAA,YAAe,EACfC,YAAA,CAMEC,kCAAA;IALC,cAAY,EAAEJ,MAAA,CAAAK,sBAAsB;IACpC,eAAa,EAAE,IAAI;IACnB,eAAa,EAAE,IAAI;IACnBC,kBAAiB,EAAEN,MAAA,CAAAO,sBAAsB;IACzCC,iBAAgB,EAAER,MAAA,CAAAS;wFAGrBP,mBAAA,sBAAyB,EACzBC,YAAA,CA+GsBO,8BAAA;IA/GAC,KAAK,EAAEX,MAAA,CAAAY;EAAY;IAE5B,gBAAc,EAAAC,QAAA,CACvB,MAMM,CANNC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAA6C;MAAvCnB,KAAK,EAAC;IAAc,GAAC,aAAW,qBACtCmB,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAA2C,QAA3CG,UAA2C,EAAAC,gBAAA,CAArBlB,MAAA,CAAAmB,WAAW,kBACjCL,mBAAA,CAAoD,QAApDM,UAAoD,EAAAF,gBAAA,CAAnBlB,MAAA,CAAAqB,SAAS,iB;IAMrC,WAAS,EAAAR,QAAA,CAClB,MA4DiB,CA5DjBV,YAAA,CA4DiBmB,yBAAA;MA3DfC,IAAI,EAAC,UAAU;MACd,iBAAe,EAAEvB,MAAA,CAAAwB,aAAa;MAC/B7B,KAAK,EAAC;;MAEK8B,OAAO,EAAAZ,QAAA,CAChB,MAAoB,CAApBX,mBAAA,iBAAoB,EACpBY,mBAAA,CAwCM,OAxCNY,UAwCM,GAvCJxB,mBAAA,mBAAsB,EACtBC,YAAA,CAOEwB,0BAAA;QANAC,IAAI,EAAC,KAAK;QACTC,QAAQ,EAAE;UAAAC,CAAA;UAAAC,CAAA;QAAA,CAAc;QACxBC,WAAW,EAAE,IAAI;QACjBC,OAAK,EAAEC,IAAA,CAAAC,cAAc;QACrBC,YAAW,EAAEF,IAAA,CAAAG,gBAAgB;QAC7BC,aAAW,EAAEJ,IAAA,CAAAK;6EAGhBrC,mBAAA,mBAAsB,EACtBC,YAAA,CAOEwB,0BAAA;QANAC,IAAI,EAAC,KAAK;QACTC,QAAQ,EAAE;UAAAC,CAAA;UAAAC,CAAA;QAAA,CAAc;QACxBC,WAAW,EAAE,IAAI;QACjBC,OAAK,EAAEC,IAAA,CAAAC,cAAc;QACrBC,YAAW,EAAEF,IAAA,CAAAG,gBAAgB;QAC7BC,aAAW,EAAEJ,IAAA,CAAAK;6EAGhBrC,mBAAA,mBAAsB,EACtBC,YAAA,CAOEwB,0BAAA;QANAC,IAAI,EAAC,KAAK;QACTC,QAAQ,EAAE;UAAAC,CAAA;UAAAC,CAAA;QAAA,CAAc;QACxBC,WAAW,EAAE,IAAI;QACjBC,OAAK,EAAEC,IAAA,CAAAC,cAAc;QACrBC,YAAW,EAAEF,IAAA,CAAAG,gBAAgB;QAC7BC,aAAW,EAAEJ,IAAA,CAAAK;6EAGhBrC,mBAAA,mBAAsB,EACtBC,YAAA,CAOEqC,8BAAA;QANAZ,IAAI,EAAC,KAAK;QACTC,QAAQ,EAAE;UAAAC,CAAA;UAAAC,CAAA;QAAA,CAAc;QACxB,wBAAsB,EAAE,IAAI;QAC5BK,YAAW,EAAEF,IAAA,CAAAG,gBAAgB;QAC7BC,aAAW,EAAEJ,IAAA,CAAAK,oBAAoB;QACjCE,aAAY,EAAEP,IAAA,CAAAQ;qFAInBxC,mBAAA,wBAA2B,EAC3BC,YAAA,CAQEwC,uBAAA;QAPC,eAAa,EAAE3C,MAAA,CAAA4C,YAAY;QAC3B,eAAa,EAAE5C,MAAA,CAAAY,YAAY;QAC3B,gBAAc,EAAE,IAAI;QACpB,aAAW,EAAEZ,MAAA,CAAA6C,iBAAiB;QAC9B,cAAY,EAAE7C,MAAA,CAAA8C,WAAW;QACzBC,cAAa,EAAE/C,MAAA,CAAAgD,kBAAkB;QACjCC,sBAAsB,EAAEjD,MAAA,CAAAkD;;;;IAOtB,cAAY,EAAArC,QAAA,CACrB,MAqBM,CArBNC,mBAAA,CAqBM,OArBNqC,UAqBM,GApBJrC,mBAAA,CAOM,OAPNsC,UAOM,G,0BANJtC,mBAAA,CAA+B;MAA1BnB,KAAK,EAAC;IAAU,GAAC,KAAG,qB,0BACzBmB,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAAqB,WAAlB,gBAAc,qBACjBA,mBAAA,CAEM,OAFNuC,UAEM,GADJvC,mBAAA,CAA2E,eAApE,MAAI,GAAAI,gBAAA,CAAGlB,MAAA,CAAAsD,WAAW,CAACC,WAAW,IAAG,GAAC,GAAArC,gBAAA,CAAGlB,MAAA,CAAAsD,WAAW,CAACE,QAAQ,iB,KAIpEtD,mBAAA,yBAA4B,EAC5BY,mBAAA,CASM,OATN2C,UASM,GARJtD,YAAA,CAOEqC,8BAAA;MANAZ,IAAI,EAAC,KAAK;MACTC,QAAQ,EAAE;QAAAC,CAAA;QAAAC,CAAA;MAAA,CAAc;MACxB,wBAAsB,EAAE,IAAI;MAC5BK,YAAW,EAAEF,IAAA,CAAAG,gBAAgB;MAC7BC,aAAW,EAAEJ,IAAA,CAAAK,oBAAoB;MACjCE,aAAY,EAAEP,IAAA,CAAAQ;;IAOZgB,GAAG,EAAA7C,QAAA,CACZ,MAIE,CAJFV,YAAA,CAIEwD,0BAAA;MAHA/B,IAAI,EAAC,QAAQ;MACZ,iBAAe,EAAE,IAAI;MACrBI,WAAW,EAAE;;;gCAKpB9B,mBAAA,YAAe,EACfY,mBAAA,CAIM,OAJN8C,WAIM,GAHJ9C,mBAAA,CAES;IAFAmB,OAAK,EAAA4B,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE9D,MAAA,CAAA+D,UAAA,IAAA/D,MAAA,CAAA+D,UAAA,IAAAD,IAAA,CAAU;IAAEnE,KAAK,EAAC;sBAC7BK,MAAA,CAAAgE,UAAU,yC,GAIjB9D,mBAAA,aAAgB,EACLF,MAAA,CAAAgE,UAAU,I,cAArBpE,mBAAA,CAKM,OALNqE,WAKM,GAJJ9D,YAAA,CAGE+D,8BAAA;IAFC,cAAY,EAAElE,MAAA,CAAA8C,WAAW;IACzBtC,iBAAgB,EAAER,MAAA,CAAAS;yGAIvBP,mBAAA,YAAe,EACJF,MAAA,CAAAmE,cAAc,I,cAAzBvE,mBAAA,CAqBM,OArBNwE,WAqBM,G,4BApBJtD,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAkBM,OAlBNuD,WAkBM,GAjBJvD,mBAAA,CAGQ,gB,gBAFNA,mBAAA,CAAiD;IAA1CwD,IAAI,EAAC,UAAU;+DAAUtE,MAAA,CAAAwB,aAAa,GAAA+C,MAAA;qDAAbvE,MAAA,CAAAwB,aAAa,E,8CAAI,SAEnD,oB,GACAV,mBAAA,CAQQ,gB,6CARD,OAEL,qB,gBAAAA,mBAAA,CAKS;+DALQd,MAAA,CAAAY,YAAY,GAAA2D,MAAA;IAAGC,QAAM,EAAAX,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE9D,MAAA,CAAAyE,iBAAA,IAAAzE,MAAA,CAAAyE,iBAAA,IAAAX,IAAA,CAAiB;kCACvDhD,mBAAA,CAAkC;IAA1B4D,KAAK,EAAC;EAAQ,GAAC,IAAE,oBACzB5D,mBAAA,CAAiC;IAAzB4D,KAAK,EAAC;EAAO,GAAC,IAAE,oBACxB5D,mBAAA,CAAmC;IAA3B4D,KAAK,EAAC;EAAS,GAAC,IAAE,oBAC1B5D,mBAAA,CAAyC;IAAjC4D,KAAK,EAAC;EAAe,GAAC,IAAE,mB,2DAJjB1E,MAAA,CAAAY,YAAY,E,KAO/BE,mBAAA,CAGM,OAHN6D,WAGM,GAFJ7D,mBAAA,CAA2E,eAApE,MAAI,GAAAI,gBAAA,CAAGlB,MAAA,CAAAsD,WAAW,CAACC,WAAW,IAAG,GAAC,GAAArC,gBAAA,CAAGlB,MAAA,CAAAsD,WAAW,CAACE,QAAQ,kBAChE1C,mBAAA,CAA8C,eAAvC,MAAI,GAAAI,gBAAA,CAAGlB,MAAA,CAAAsD,WAAW,CAACsB,OAAO,IAAG,IAAE,gB,4CAK5C1E,mBAAA,UAAa,EACbY,mBAAA,CAKS;IAJPnB,KAAK,EAAC,cAAc;IACnBsC,OAAK,EAAA4B,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEvE,MAAA,CAAAmE,cAAc,IAAInE,MAAA,CAAAmE,cAAc;KACzC,MAED,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}