{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n// VPA数字人状态管理Store\nimport { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion'); // companion, interactive, restricted\n  const avatar = ref('vpa2.gif'); // 当前使用的头像\n  const isActive = ref(true);\n  const isListening = ref(false);\n  const isSpeaking = ref(false);\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning');\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  });\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  });\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral'); // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle'); // idle, listening, speaking, thinking, gesturing\n\n  // === VPA组件配置 ===\n  const componentConfig = ref({\n    // 陪伴小窗配置\n    avatarWidget: {\n      defaultSize: '2x2',\n      availableSizes: ['2x2', '2x4', '3x3'],\n      position: {\n        x: 0,\n        y: 0\n      },\n      zIndex: 100\n    },\n    // 交互面板配置\n    interactionPanel: {\n      defaultSize: '4x4',\n      availableSizes: ['4x4', '8x9'],\n      position: {\n        x: 0,\n        y: 0\n      },\n      zIndex: 200\n    }\n  });\n\n  // === 16x9网格系统支持 ===\n  const gridConfig = ref({\n    columns: 16,\n    rows: 9,\n    cellWidth: 0,\n    cellHeight: 0,\n    gap: 16\n  });\n\n  // === 对话历史 ===\n  const conversationHistory = ref([]);\n  const currentConversation = ref(null);\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    };\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    };\n  });\n  const contextualGreeting = computed(() => {\n    const {\n      timeOfDay,\n      hasChildren\n    } = sceneContext.value;\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    };\n    return greetings[timeOfDay] || greetings.morning;\n  });\n\n  // === 方法 ===\n\n  // 切换VPA模式\n  const switchMode = newMode => {\n    currentMode.value = newMode;\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif';\n\n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false;\n    } else {\n      isActive.value = true;\n    }\n  };\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName;\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n\n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName);\n  };\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = sceneName => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    };\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus;\n    currentExpression.value = behavior.expression;\n    userPreferences.value.aiPersonality = behavior.personality;\n  };\n\n  // 更新用户偏好\n  const updatePreferences = preferences => {\n    userPreferences.value = {\n      ...userPreferences.value,\n      ...preferences\n    };\n  };\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    };\n    currentAction.value = 'listening';\n    isListening.value = true;\n  };\n\n  // 添加对话消息\n  const addMessage = message => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      });\n    }\n  };\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      });\n      currentConversation.value = null;\n    }\n    currentAction.value = 'idle';\n    isListening.value = false;\n    isSpeaking.value = false;\n  };\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true;\n    currentAction.value = 'speaking';\n    currentExpression.value = emotion;\n\n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false;\n      currentAction.value = 'idle';\n      currentExpression.value = 'neutral';\n    }, text.length * 100); // 根据文本长度估算说话时间\n  };\n\n  // 设置表情\n  const setExpression = expression => {\n    currentExpression.value = expression;\n  };\n\n  // 设置动作\n  const setAction = action => {\n    currentAction.value = action;\n  };\n\n  // 情绪感知\n  const detectEmotion = () => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired'];\n    return emotions[Math.floor(Math.random() * emotions.length)];\n  };\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    };\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n    return context;\n  };\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }));\n  };\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa');\n    if (saved) {\n      try {\n        const data = JSON.parse(saved);\n        currentMode.value = data.currentMode || 'companion';\n        userPreferences.value = {\n          ...userPreferences.value,\n          ...data.userPreferences\n        };\n        sceneContext.value = {\n          ...sceneContext.value,\n          ...data.sceneContext\n        };\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error);\n      }\n    }\n  };\n\n  // 更新网格配置\n  const updateGridConfig = config => {\n    gridConfig.value = {\n      ...gridConfig.value,\n      ...config\n    };\n  };\n\n  // 获取VPA组件配置\n  const getComponentConfig = componentType => {\n    return componentConfig.value[componentType] || null;\n  };\n\n  // 更新VPA组件配置\n  const updateComponentConfig = (componentType, config) => {\n    if (componentConfig.value[componentType]) {\n      componentConfig.value[componentType] = {\n        ...componentConfig.value[componentType],\n        ...config\n      };\n    }\n  };\n\n  // 计算网格位置\n  const calculateGridPosition = (size, position) => {\n    const [cols, rows] = size.split('x').map(Number);\n    return {\n      gridColumn: `${position.x + 1} / span ${cols}`,\n      gridRow: `${position.y + 1} / span ${rows}`,\n      width: `calc(${cols} * (100% / ${gridConfig.value.columns}) - ${gridConfig.value.gap}px)`,\n      height: `calc(${rows} * (100% / ${gridConfig.value.rows}) - ${gridConfig.value.gap}px)`\n    };\n  };\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState();\n    analyzeContext();\n\n    // 设置定期保存\n    setInterval(saveVPAState, 30000); // 每30秒保存一次\n  };\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useVPAStore", "currentMode", "avatar", "isActive", "isListening", "isSpeaking", "currentScene", "sceneContext", "timeOfDay", "weather", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSchoolDay", "userMood", "passengers", "userPreferences", "theme", "cardSizes", "aiPersonality", "voiceEnabled", "language", "currentExpression", "currentAction", "componentConfig", "avatar<PERSON><PERSON>t", "defaultSize", "availableSizes", "position", "x", "y", "zIndex", "interactionPanel", "gridConfig", "columns", "rows", "cellWidth", "cellHeight", "gap", "conversationHistory", "currentConversation", "vpaDisplayConfig", "expressions", "expression", "value", "neutral", "mode", "contextualGreeting", "greetings", "morning", "afternoon", "evening", "night", "switchMode", "newMode", "updateScene", "scene<PERSON><PERSON>", "context", "adjustVPABehavior", "scene<PERSON><PERSON><PERSON><PERSON>", "personality", "priority", "behavior", "focus", "updatePreferences", "preferences", "startConversation", "topic", "id", "Date", "now", "startTime", "messages", "addMessage", "message", "push", "timestamp", "endConversation", "endTime", "speak", "text", "emotion", "setTimeout", "length", "setExpression", "setAction", "action", "detectEmotion", "emotions", "Math", "floor", "random", "analyzeContext", "location", "traffic", "getHours", "saveVPAState", "localStorage", "setItem", "JSON", "stringify", "loadVPAState", "saved", "getItem", "data", "parse", "error", "console", "warn", "updateGridConfig", "config", "getComponentConfig", "componentType", "updateComponentConfig", "calculateGridPosition", "size", "cols", "split", "map", "Number", "gridColumn", "gridRow", "width", "height", "initializeVPA", "setInterval"], "sources": ["F:/工作/theme/ai-hmi/src/store/modules/vpa.js"], "sourcesContent": ["// VPA数字人状态管理Store\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion') // companion, interactive, restricted\n  const avatar = ref('vpa2.gif') // 当前使用的头像\n  const isActive = ref(true)\n  const isListening = ref(false)\n  const isSpeaking = ref(false)\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning')\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  })\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  })\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral') // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle') // idle, listening, speaking, thinking, gesturing\n\n  // === VPA组件配置 ===\n  const componentConfig = ref({\n    // 陪伴小窗配置\n    avatarWidget: {\n      defaultSize: '2x2',\n      availableSizes: ['2x2', '2x4', '3x3'],\n      position: { x: 0, y: 0 },\n      zIndex: 100\n    },\n    // 交互面板配置\n    interactionPanel: {\n      defaultSize: '4x4',\n      availableSizes: ['4x4', '8x9'],\n      position: { x: 0, y: 0 },\n      zIndex: 200\n    }\n  })\n\n  // === 16x9网格系统支持 ===\n  const gridConfig = ref({\n    columns: 16,\n    rows: 9,\n    cellWidth: 0,\n    cellHeight: 0,\n    gap: 16\n  })\n\n  // === 对话历史 ===\n  const conversationHistory = ref([])\n  const currentConversation = ref(null)\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    }\n\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    }\n  })\n\n  const contextualGreeting = computed(() => {\n    const { timeOfDay, hasChildren } = sceneContext.value\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    }\n\n    return greetings[timeOfDay] || greetings.morning\n  })\n\n  // === 方法 ===\n  \n  // 切换VPA模式\n  const switchMode = (newMode) => {\n    currentMode.value = newMode\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif'\n    \n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false\n    } else {\n      isActive.value = true\n    }\n  }\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName\n    sceneContext.value = { ...sceneContext.value, ...context }\n    \n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName)\n  }\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = (sceneName) => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    }\n\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus\n    currentExpression.value = behavior.expression\n    userPreferences.value.aiPersonality = behavior.personality\n  }\n\n  // 更新用户偏好\n  const updatePreferences = (preferences) => {\n    userPreferences.value = { ...userPreferences.value, ...preferences }\n  }\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    }\n    currentAction.value = 'listening'\n    isListening.value = true\n  }\n\n  // 添加对话消息\n  const addMessage = (message) => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      })\n    }\n  }\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      })\n      currentConversation.value = null\n    }\n    currentAction.value = 'idle'\n    isListening.value = false\n    isSpeaking.value = false\n  }\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true\n    currentAction.value = 'speaking'\n    currentExpression.value = emotion\n    \n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false\n      currentAction.value = 'idle'\n      currentExpression.value = 'neutral'\n    }, text.length * 100) // 根据文本长度估算说话时间\n  }\n\n  // 设置表情\n  const setExpression = (expression) => {\n    currentExpression.value = expression\n  }\n\n  // 设置动作\n  const setAction = (action) => {\n    currentAction.value = action\n  }\n\n  // 情绪感知\n  const detectEmotion = () => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired']\n    return emotions[Math.floor(Math.random() * emotions.length)]\n  }\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : \n                 new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    }\n    \n    sceneContext.value = { ...sceneContext.value, ...context }\n    return context\n  }\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }))\n  }\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa')\n    if (saved) {\n      try {\n        const data = JSON.parse(saved)\n        currentMode.value = data.currentMode || 'companion'\n        userPreferences.value = { ...userPreferences.value, ...data.userPreferences }\n        sceneContext.value = { ...sceneContext.value, ...data.sceneContext }\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error)\n      }\n    }\n  }\n\n  // 更新网格配置\n  const updateGridConfig = (config) => {\n    gridConfig.value = { ...gridConfig.value, ...config }\n  }\n\n  // 获取VPA组件配置\n  const getComponentConfig = (componentType) => {\n    return componentConfig.value[componentType] || null\n  }\n\n  // 更新VPA组件配置\n  const updateComponentConfig = (componentType, config) => {\n    if (componentConfig.value[componentType]) {\n      componentConfig.value[componentType] = {\n        ...componentConfig.value[componentType],\n        ...config\n      }\n    }\n  }\n\n  // 计算网格位置\n  const calculateGridPosition = (size, position) => {\n    const [cols, rows] = size.split('x').map(Number)\n    return {\n      gridColumn: `${position.x + 1} / span ${cols}`,\n      gridRow: `${position.y + 1} / span ${rows}`,\n      width: `calc(${cols} * (100% / ${gridConfig.value.columns}) - ${gridConfig.value.gap}px)`,\n      height: `calc(${rows} * (100% / ${gridConfig.value.rows}) - ${gridConfig.value.gap}px)`\n    }\n  }\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState()\n    analyzeContext()\n\n    // 设置定期保存\n    setInterval(saveVPAState, 30000) // 每30秒保存一次\n  }\n\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    \n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    \n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  }\n})\n"], "mappings": ";;;AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAEnC,OAAO,MAAMC,WAAW,GAAGH,WAAW,CAAC,KAAK,EAAE,MAAM;EAClD;EACA,MAAMI,WAAW,GAAGH,GAAG,CAAC,WAAW,CAAC,EAAC;EACrC,MAAMI,MAAM,GAAGJ,GAAG,CAAC,UAAU,CAAC,EAAC;EAC/B,MAAMK,QAAQ,GAAGL,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMM,WAAW,GAAGN,GAAG,CAAC,KAAK,CAAC;EAC9B,MAAMO,UAAU,GAAGP,GAAG,CAAC,KAAK,CAAC;;EAE7B;EACA,MAAMQ,YAAY,GAAGR,GAAG,CAAC,iBAAiB,CAAC;EAC3C,MAAMS,YAAY,GAAGT,GAAG,CAAC;IACvBU,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGhB,GAAG,CAAC;IAC1BiB,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE,CAAC,CAAC;IACbC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGtB,GAAG,CAAC,SAAS,CAAC,EAAC;EACzC,MAAMuB,aAAa,GAAGvB,GAAG,CAAC,MAAM,CAAC,EAAC;;EAElC;EACA,MAAMwB,eAAe,GAAGxB,GAAG,CAAC;IAC1B;IACAyB,YAAY,EAAE;MACZC,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACrCC,QAAQ,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MACxBC,MAAM,EAAE;IACV,CAAC;IACD;IACAC,gBAAgB,EAAE;MAChBN,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAC9BC,QAAQ,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MACxBC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;;EAEF;EACA,MAAME,UAAU,GAAGjC,GAAG,CAAC;IACrBkC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGvC,GAAG,CAAC,EAAE,CAAC;EACnC,MAAMwC,mBAAmB,GAAGxC,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMyC,gBAAgB,GAAGxC,QAAQ,CAAC,MAAM;IACtC,MAAMyC,WAAW,GAAG;MAClB,SAAS,EAAE,OAAO;MAClB,OAAO,EAAE,OAAO;MAChB,UAAU,EAAE,UAAU;MACtB,WAAW,EAAE,OAAO;MACpB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE;IACd,CAAC;IAED,OAAO;MACLC,UAAU,EAAED,WAAW,CAACpB,iBAAiB,CAACsB,KAAK,CAAC,IAAIF,WAAW,CAACG,OAAO;MACvEzC,MAAM,EAAEA,MAAM,CAACwC,KAAK;MACpBE,IAAI,EAAE3C,WAAW,CAACyC,KAAK;MACvBvC,QAAQ,EAAEA,QAAQ,CAACuC;IACrB,CAAC;EACH,CAAC,CAAC;EAEF,MAAMG,kBAAkB,GAAG9C,QAAQ,CAAC,MAAM;IACxC,MAAM;MAAES,SAAS;MAAEE;IAAY,CAAC,GAAGH,YAAY,CAACmC,KAAK;IACrD,MAAMI,SAAS,GAAG;MAChBC,OAAO,EAAErC,WAAW,GAAG,iBAAiB,GAAG,cAAc;MACzDsC,SAAS,EAAE,cAAc;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,KAAK,EAAE;IACT,CAAC;IAED,OAAOJ,SAAS,CAACtC,SAAS,CAAC,IAAIsC,SAAS,CAACC,OAAO;EAClD,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMI,UAAU,GAAIC,OAAO,IAAK;IAC9BnD,WAAW,CAACyC,KAAK,GAAGU,OAAO;IAC3BlD,MAAM,CAACwC,KAAK,GAAGU,OAAO,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;;IAEhE;IACA,IAAIA,OAAO,KAAK,YAAY,EAAE;MAC5B;MACAjD,QAAQ,CAACuC,KAAK,GAAG,KAAK;IACxB,CAAC,MAAM;MACLvC,QAAQ,CAACuC,KAAK,GAAG,IAAI;IACvB;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/CjD,YAAY,CAACoC,KAAK,GAAGY,SAAS;IAC9B/C,YAAY,CAACmC,KAAK,GAAG;MAAE,GAAGnC,YAAY,CAACmC,KAAK;MAAE,GAAGa;IAAQ,CAAC;;IAE1D;IACAC,iBAAiB,CAACF,SAAS,CAAC;EAC9B,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIF,SAAS,IAAK;IACvC,MAAMG,cAAc,GAAG;MACrB,QAAQ,EAAE;QACRC,WAAW,EAAE,QAAQ;QACrBjB,UAAU,EAAE,OAAO;QACnBkB,QAAQ,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe;MACzD,CAAC;MACD,OAAO,EAAE;QACPD,WAAW,EAAE,cAAc;QAC3BjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,cAAc;MACrD,CAAC;MACD,SAAS,EAAE;QACTD,WAAW,EAAE,MAAM;QACnBjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACnC,CAAC;MACD,eAAe,EAAE;QACfD,WAAW,EAAE,UAAU;QACvBjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,QAAQ;MACpD;IACF,CAAC;IAED,MAAMC,QAAQ,GAAGH,cAAc,CAACH,SAAS,CAAC,IAAIG,cAAc,CAACI,KAAK;IAClEzC,iBAAiB,CAACsB,KAAK,GAAGkB,QAAQ,CAACnB,UAAU;IAC7C3B,eAAe,CAAC4B,KAAK,CAACzB,aAAa,GAAG2C,QAAQ,CAACF,WAAW;EAC5D,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IACzCjD,eAAe,CAAC4B,KAAK,GAAG;MAAE,GAAG5B,eAAe,CAAC4B,KAAK;MAAE,GAAGqB;IAAY,CAAC;EACtE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,GAAG,IAAI,KAAK;IAC1C3B,mBAAmB,CAACI,KAAK,GAAG;MAC1BwB,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdH,KAAK;MACLI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC;MACrBG,QAAQ,EAAE;IACZ,CAAC;IACDjD,aAAa,CAACqB,KAAK,GAAG,WAAW;IACjCtC,WAAW,CAACsC,KAAK,GAAG,IAAI;EAC1B,CAAC;;EAED;EACA,MAAM6B,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAIlC,mBAAmB,CAACI,KAAK,EAAE;MAC7BJ,mBAAmB,CAACI,KAAK,CAAC4B,QAAQ,CAACG,IAAI,CAAC;QACtC,GAAGD,OAAO;QACVE,SAAS,EAAE,IAAIP,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIrC,mBAAmB,CAACI,KAAK,EAAE;MAC7BL,mBAAmB,CAACK,KAAK,CAAC+B,IAAI,CAAC;QAC7B,GAAGnC,mBAAmB,CAACI,KAAK;QAC5BkC,OAAO,EAAE,IAAIT,IAAI,CAAC;MACpB,CAAC,CAAC;MACF7B,mBAAmB,CAACI,KAAK,GAAG,IAAI;IAClC;IACArB,aAAa,CAACqB,KAAK,GAAG,MAAM;IAC5BtC,WAAW,CAACsC,KAAK,GAAG,KAAK;IACzBrC,UAAU,CAACqC,KAAK,GAAG,KAAK;EAC1B,CAAC;;EAED;EACA,MAAMmC,KAAK,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,SAAS,KAAK;IAC3C1E,UAAU,CAACqC,KAAK,GAAG,IAAI;IACvBrB,aAAa,CAACqB,KAAK,GAAG,UAAU;IAChCtB,iBAAiB,CAACsB,KAAK,GAAGqC,OAAO;;IAEjC;IACAC,UAAU,CAAC,MAAM;MACf3E,UAAU,CAACqC,KAAK,GAAG,KAAK;MACxBrB,aAAa,CAACqB,KAAK,GAAG,MAAM;MAC5BtB,iBAAiB,CAACsB,KAAK,GAAG,SAAS;IACrC,CAAC,EAAEoC,IAAI,CAACG,MAAM,GAAG,GAAG,CAAC,EAAC;EACxB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIzC,UAAU,IAAK;IACpCrB,iBAAiB,CAACsB,KAAK,GAAGD,UAAU;EACtC,CAAC;;EAED;EACA,MAAM0C,SAAS,GAAIC,MAAM,IAAK;IAC5B/D,aAAa,CAACqB,KAAK,GAAG0C,MAAM;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA;IACA,MAAMC,QAAQ,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAChE,OAAOA,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,QAAQ,CAACL,MAAM,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMnC,OAAO,GAAG;MACdoC,QAAQ,EAAE,SAAS;MACnBlF,OAAO,EAAE,SAAS;MAClBmF,OAAO,EAAE,SAAS;MAClBpF,SAAS,EAAE,IAAI2D,IAAI,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS,GACtC,IAAI1B,IAAI,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG;IACxD,CAAC;IAEDtF,YAAY,CAACmC,KAAK,GAAG;MAAE,GAAGnC,YAAY,CAACmC,KAAK;MAAE,GAAGa;IAAQ,CAAC;IAC1D,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC;MAChDjG,WAAW,EAAEA,WAAW,CAACyC,KAAK;MAC9B5B,eAAe,EAAEA,eAAe,CAAC4B,KAAK;MACtCnC,YAAY,EAAEA,YAAY,CAACmC;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMyD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;IAChD,IAAID,KAAK,EAAE;MACT,IAAI;QACF,MAAME,IAAI,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;QAC9BnG,WAAW,CAACyC,KAAK,GAAG4D,IAAI,CAACrG,WAAW,IAAI,WAAW;QACnDa,eAAe,CAAC4B,KAAK,GAAG;UAAE,GAAG5B,eAAe,CAAC4B,KAAK;UAAE,GAAG4D,IAAI,CAACxF;QAAgB,CAAC;QAC7EP,YAAY,CAACmC,KAAK,GAAG;UAAE,GAAGnC,YAAY,CAACmC,KAAK;UAAE,GAAG4D,IAAI,CAAC/F;QAAa,CAAC;MACtE,CAAC,CAAC,OAAOiG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,KAAK,CAAC;MAClD;IACF;EACF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIC,MAAM,IAAK;IACnC7E,UAAU,CAACW,KAAK,GAAG;MAAE,GAAGX,UAAU,CAACW,KAAK;MAAE,GAAGkE;IAAO,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,aAAa,IAAK;IAC5C,OAAOxF,eAAe,CAACoB,KAAK,CAACoE,aAAa,CAAC,IAAI,IAAI;EACrD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAACD,aAAa,EAAEF,MAAM,KAAK;IACvD,IAAItF,eAAe,CAACoB,KAAK,CAACoE,aAAa,CAAC,EAAE;MACxCxF,eAAe,CAACoB,KAAK,CAACoE,aAAa,CAAC,GAAG;QACrC,GAAGxF,eAAe,CAACoB,KAAK,CAACoE,aAAa,CAAC;QACvC,GAAGF;MACL,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAGA,CAACC,IAAI,EAAEvF,QAAQ,KAAK;IAChD,MAAM,CAACwF,IAAI,EAAEjF,IAAI,CAAC,GAAGgF,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAChD,OAAO;MACLC,UAAU,EAAE,GAAG5F,QAAQ,CAACC,CAAC,GAAG,CAAC,WAAWuF,IAAI,EAAE;MAC9CK,OAAO,EAAE,GAAG7F,QAAQ,CAACE,CAAC,GAAG,CAAC,WAAWK,IAAI,EAAE;MAC3CuF,KAAK,EAAE,QAAQN,IAAI,cAAcnF,UAAU,CAACW,KAAK,CAACV,OAAO,OAAOD,UAAU,CAACW,KAAK,CAACN,GAAG,KAAK;MACzFqF,MAAM,EAAE,QAAQxF,IAAI,cAAcF,UAAU,CAACW,KAAK,CAACT,IAAI,OAAOF,UAAU,CAACW,KAAK,CAACN,GAAG;IACpF,CAAC;EACH,CAAC;;EAED;EACA,MAAMsF,aAAa,GAAGA,CAAA,KAAM;IAC1BvB,YAAY,CAAC,CAAC;IACdT,cAAc,CAAC,CAAC;;IAEhB;IACAiC,WAAW,CAAC7B,YAAY,EAAE,KAAK,CAAC,EAAC;EACnC,CAAC;EAED,OAAO;IACL;IACA7F,WAAW;IACXC,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZO,eAAe;IACfM,iBAAiB;IACjBC,aAAa;IACbgB,mBAAmB;IACnBC,mBAAmB;IAEnB;IACAC,gBAAgB;IAChBM,kBAAkB;IAElB;IACAM,UAAU;IACVE,WAAW;IACXG,iBAAiB;IACjBM,iBAAiB;IACjBE,iBAAiB;IACjBO,UAAU;IACVI,eAAe;IACfE,KAAK;IACLK,aAAa;IACbC,SAAS;IACTE,aAAa;IACbK,cAAc;IACdI,YAAY;IACZK,YAAY;IACZuB;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}