{"ast": null, "code": "import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';\nimport ImageGenerationService from '@/services/ImageGenerationService';\nimport DynamicWallpaperService from '@/services/DynamicWallpaperService';\nimport ColorExtractor from '@/utils/ColorExtractor';\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer';\nexport default {\n  name: 'DynamicWallpaperManager',\n  props: {\n    scenePrompt: {\n      type: String,\n      default: '现代简约风格，商务氛围'\n    },\n    autoGenerate: {\n      type: Boolean,\n      default: true\n    },\n    enableConfig: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['wallpaper-changed', 'colors-extracted'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isLoading = ref(false);\n    const showConfig = ref(false);\n    const currentWallpaper = ref(null);\n    const extractedColors = ref(null);\n    const loadingText = ref('正在生成壁纸...');\n    const showDynamicPreview = ref(false);\n    const previewVideoUrl = ref('');\n    const pendingDynamicWallpaper = ref(null);\n\n    // 配置\n    const config = reactive({\n      enabled: true,\n      autoGenerate: true,\n      opacity: 0.8,\n      cardOpacity: 0.3,\n      contrastBoost: 1.2\n    });\n\n    // 服务实例\n    const imageService = new ImageGenerationService();\n    const dynamicWallpaperService = new DynamicWallpaperService();\n\n    // 计算属性\n    const defaultGradient = computed(() => ({\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    }));\n    const overlayStyles = computed(() => {\n      if (!extractedColors.value) return {};\n      return {\n        '--glass-bg': extractedColors.value.glassBackground,\n        '--glass-border': extractedColors.value.glassBorder,\n        '--text-color': extractedColors.value.text,\n        '--card-opacity': config.cardOpacity\n      };\n    });\n\n    // 方法\n    const generateWallpaper = async (prompt = props.scenePrompt) => {\n      if (!config.enabled) return;\n      isLoading.value = true;\n      try {\n        console.log('开始生成壁纸，提示词:', prompt);\n        const result = await imageService.generateWallpaper(prompt);\n        if (result && result.imageUrl) {\n          currentWallpaper.value = {\n            type: 'image',\n            url: result.imageUrl,\n            prompt: result.prompt,\n            taskId: result.taskId\n          };\n\n          // 提取颜色，传递提示词用于智能降级\n          await extractColors(result.imageUrl, prompt);\n\n          // 保存到历史\n          imageService.saveToHistory(result);\n          emit('wallpaper-changed', currentWallpaper.value);\n          console.log('壁纸生成成功:', result.imageUrl);\n        }\n      } catch (error) {\n        console.error('壁纸生成失败:', error);\n        // 使用默认壁纸\n        resetToDefault();\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const extractColors = async (imageUrl, prompt = props.scenePrompt, optimizedPrompt = '') => {\n      try {\n        console.log('🎨 开始AI智能配色分析...');\n\n        // 使用AI智能配色分析器\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(imageUrl, prompt, optimizedPrompt);\n        extractedColors.value = intelligentColors;\n        emit('colors-extracted', intelligentColors);\n        console.log('🎨 AI智能配色完成:', intelligentColors);\n\n        // 如果有AI分析结果，显示详细信息\n        if (intelligentColors.aiAnalysis) {\n          console.log('🧠 AI分析结果:', {\n            情感氛围: intelligentColors.aiAnalysis.mood,\n            整体亮度: intelligentColors.aiAnalysis.brightness,\n            氛围描述: intelligentColors.aiAnalysis.atmosphere,\n            主要颜色: intelligentColors.aiAnalysis.dominantColors\n          });\n        }\n      } catch (error) {\n        console.error('AI配色分析失败:', error);\n        // 降级到传统颜色提取\n        try {\n          const colors = await ColorExtractor.extractColors(imageUrl, prompt);\n          extractedColors.value = colors;\n          emit('colors-extracted', colors);\n          console.log('降级到传统颜色提取:', colors);\n        } catch (fallbackError) {\n          console.error('传统颜色提取也失败:', fallbackError);\n          // 最终降级到场景智能颜色\n          const fallbackColors = ColorExtractor.getSceneBasedColors(prompt);\n          extractedColors.value = fallbackColors;\n          emit('colors-extracted', fallbackColors);\n          console.log('使用场景智能颜色:', fallbackColors);\n        }\n      }\n    };\n    const generateNewWallpaper = () => {\n      generateWallpaper();\n    };\n    const generateDynamicWallpaper = async () => {\n      if (!currentWallpaper.value || currentWallpaper.value.type !== 'image') {\n        console.warn('当前没有可用的静态壁纸，无法生成动态壁纸');\n        return;\n      }\n      isLoading.value = true;\n      loadingText.value = '正在生成动态壁纸...这可能需要2-3分钟，请耐心等待...';\n      try {\n        console.log('开始智能生成动态壁纸');\n\n        // 获取当前壁纸信息\n        const wallpaperInfo = imageService.getCurrentWallpaperInfo();\n        const taskId = dynamicWallpaperService.generateTaskId();\n        let imageFile = null;\n        let imageUrl = null;\n\n        // 优先尝试从本地路径加载文件\n        if (wallpaperInfo && wallpaperInfo.imagePath) {\n          console.log('尝试从本地路径加载文件:', wallpaperInfo.imagePath);\n          imageFile = await imageService.loadFileFromPath(wallpaperInfo.imagePath);\n        }\n\n        // 如果本地文件加载失败，使用URL模式\n        if (!imageFile && wallpaperInfo && wallpaperInfo.imageUrl) {\n          console.log('本地文件不可用，使用URL模式:', wallpaperInfo.imageUrl);\n          imageUrl = wallpaperInfo.imageUrl;\n        }\n\n        // 如果都没有，尝试从当前壁纸URL创建文件\n        if (!imageFile && !imageUrl && currentWallpaper.value.url) {\n          console.log('从当前壁纸URL创建文件:', currentWallpaper.value.url);\n          try {\n            const response = await fetch(currentWallpaper.value.url);\n            const blob = await response.blob();\n            imageFile = new File([blob], 'wallpaper.png', {\n              type: 'image/png'\n            });\n          } catch (error) {\n            console.error('从URL创建文件失败:', error);\n            imageUrl = currentWallpaper.value.url;\n          }\n        }\n\n        // 调用智能生成服务\n        const result = await dynamicWallpaperService.generateDynamicWallpaper({\n          imageFile: imageFile,\n          imageUrl: imageUrl,\n          taskId: taskId,\n          onProgress: progress => {\n            loadingText.value = progress.message;\n            console.log(`生成进度: ${progress.percentage}% - ${progress.message}`);\n          }\n        });\n        if (result && result.url) {\n          // 保存生成的动态壁纸信息，等待用户确认\n          pendingDynamicWallpaper.value = {\n            type: 'video',\n            url: result.url,\n            prompt: currentWallpaper.value.prompt,\n            taskId: result.taskId\n          };\n\n          // 显示预览模态框\n          previewVideoUrl.value = result.url;\n          showDynamicPreview.value = true;\n          console.log('动态壁纸生成成功:', result.url);\n        }\n      } catch (error) {\n        console.error('动态壁纸生成失败:', error);\n        loadingText.value = `生成失败: ${error.message}`;\n\n        // 显示错误提示\n        setTimeout(() => {\n          loadingText.value = '正在生成壁纸...';\n        }, 3000);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const applyDynamicWallpaper = () => {\n      if (pendingDynamicWallpaper.value) {\n        // 应用动态壁纸\n        currentWallpaper.value = {\n          ...pendingDynamicWallpaper.value\n        };\n        emit('wallpaper-changed', currentWallpaper.value);\n        console.log('动态壁纸已应用:', currentWallpaper.value.url);\n\n        // 关闭预览模态框\n        closeDynamicPreview();\n      }\n    };\n    const closeDynamicPreview = () => {\n      showDynamicPreview.value = false;\n      previewVideoUrl.value = '';\n      pendingDynamicWallpaper.value = null;\n    };\n    const resetToDefault = () => {\n      currentWallpaper.value = null;\n      // 使用基于当前场景的智能颜色\n      const smartColors = ColorExtractor.getSceneBasedColors(props.scenePrompt);\n      extractedColors.value = smartColors;\n      emit('colors-extracted', smartColors);\n      console.log('重置为智能默认颜色:', smartColors);\n    };\n    const onConfigChange = () => {\n      // 保存配置到本地存储\n      localStorage.setItem('wallpaper_config', JSON.stringify(config));\n\n      // 如果禁用了动态壁纸，重置为默认\n      if (!config.enabled) {\n        resetToDefault();\n      }\n    };\n    const loadConfig = () => {\n      const saved = localStorage.getItem('wallpaper_config');\n      if (saved) {\n        Object.assign(config, JSON.parse(saved));\n      }\n    };\n\n    // 生命周期\n    onMounted(async () => {\n      loadConfig();\n\n      // 确保每次页面加载都生成壁纸（如果启用了自动生成）\n      if (config.enabled && config.autoGenerate) {\n        await nextTick();\n        console.log('页面加载，开始自动生成壁纸');\n        generateWallpaper();\n      } else {\n        resetToDefault();\n      }\n    });\n\n    // 监听场景提示词变化\n    watch(() => props.scenePrompt, newPrompt => {\n      if (config.enabled && config.autoGenerate) {\n        generateWallpaper(newPrompt);\n      }\n    });\n    return {\n      isLoading,\n      showConfig,\n      currentWallpaper,\n      extractedColors,\n      config,\n      defaultGradient,\n      overlayStyles,\n      loadingText,\n      showDynamicPreview,\n      previewVideoUrl,\n      generateNewWallpaper,\n      generateDynamicWallpaper,\n      applyDynamicWallpaper,\n      closeDynamicPreview,\n      resetToDefault,\n      onConfigChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "watch", "nextTick", "ImageGenerationService", "DynamicWallpaperService", "ColorExtractor", "AIColorAnalyzer", "name", "props", "scenePrompt", "type", "String", "default", "autoGenerate", "Boolean", "enableConfig", "emits", "setup", "emit", "isLoading", "showConfig", "currentWallpaper", "extractedColors", "loadingText", "showDynamicPreview", "previewVideoUrl", "pendingDynamicWallpaper", "config", "enabled", "opacity", "cardOpacity", "contrastBoost", "imageService", "dynamicWallpaperService", "defaultGradient", "background", "overlayStyles", "value", "glassBackground", "glassBorder", "text", "generateWallpaper", "prompt", "console", "log", "result", "imageUrl", "url", "taskId", "extractColors", "saveToHistory", "error", "resetToDefault", "optimizedPrompt", "intelligentColors", "analyzeWallpaperAndGenerateColors", "aiAnalysis", "情感氛围", "mood", "整体亮度", "brightness", "氛围描述", "atmosphere", "主要颜色", "dominantColors", "colors", "fallback<PERSON><PERSON>r", "fallbackColors", "getSceneBasedColors", "generateNewWallpaper", "generateDynamicWallpaper", "warn", "wallpaperInfo", "getCurrentWallpaperInfo", "generateTaskId", "imageFile", "imagePath", "loadFileFromPath", "response", "fetch", "blob", "File", "onProgress", "progress", "message", "percentage", "setTimeout", "applyDynamicWallpaper", "closeDynamicPreview", "smartColors", "onConfigChange", "localStorage", "setItem", "JSON", "stringify", "loadConfig", "saved", "getItem", "Object", "assign", "parse", "newPrompt"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dynamic-wallpaper-manager\">\r\n    <!-- 壁纸容器 -->\r\n    <div class=\"wallpaper-container\" :class=\"{ 'loading': isLoading }\">\r\n      <!-- 图片壁纸 -->\r\n      <div \r\n        v-if=\"currentWallpaper && currentWallpaper.type === 'image'\"\r\n        class=\"wallpaper-image\"\r\n        :style=\"{ backgroundImage: `url(${currentWallpaper.url})` }\"\r\n      ></div>\r\n      \r\n      <!-- 视频壁纸 -->\r\n      <video \r\n        v-if=\"currentWallpaper && currentWallpaper.type === 'video'\"\r\n        class=\"wallpaper-video\"\r\n        :src=\"currentWallpaper.url\"\r\n        autoplay\r\n        muted\r\n        loop\r\n        playsinline\r\n      ></video>\r\n      \r\n      <!-- 默认渐变背景 -->\r\n      <div \r\n        v-if=\"!currentWallpaper\"\r\n        class=\"wallpaper-default\"\r\n        :style=\"defaultGradient\"\r\n      ></div>\r\n      \r\n      <!-- 加载遮罩 -->\r\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n        <div class=\"loading-spinner\">\r\n          <i class=\"fas fa-spinner fa-spin\"></i>\r\n          <span>{{ loadingText }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 内容插槽 -->\r\n    <div class=\"content-overlay\" :style=\"overlayStyles\">\r\n      <slot></slot>\r\n    </div>\r\n    \r\n    <!-- 配置面板 -->\r\n    <div v-if=\"showConfig\" class=\"config-panel\">\r\n      <div class=\"config-header\">\r\n        <h3>壁纸设置</h3>\r\n        <button @click=\"showConfig = false\" class=\"close-btn\">×</button>\r\n      </div>\r\n      <div class=\"config-content\">\r\n        <div class=\"config-item\">\r\n          <label>\r\n            <input \r\n              type=\"checkbox\" \r\n              v-model=\"config.enabled\"\r\n              @change=\"onConfigChange\"\r\n            />\r\n            启用动态壁纸\r\n          </label>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>\r\n            <input \r\n              type=\"checkbox\" \r\n              v-model=\"config.autoGenerate\"\r\n              @change=\"onConfigChange\"\r\n            />\r\n            自动生成壁纸\r\n          </label>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>壁纸透明度:</label>\r\n          <input \r\n            type=\"range\" \r\n            min=\"0.1\" \r\n            max=\"1\" \r\n            step=\"0.1\"\r\n            v-model=\"config.opacity\"\r\n            @input=\"onConfigChange\"\r\n          />\r\n          <span>{{ config.opacity }}</span>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>卡片透明度:</label>\r\n          <input \r\n            type=\"range\" \r\n            min=\"0.1\" \r\n            max=\"0.8\" \r\n            step=\"0.1\"\r\n            v-model=\"config.cardOpacity\"\r\n            @input=\"onConfigChange\"\r\n          />\r\n          <span>{{ config.cardOpacity }}</span>\r\n        </div>\r\n        <div class=\"config-actions\">\r\n          <button @click=\"generateNewWallpaper\" :disabled=\"isLoading\">\r\n            重新生成壁纸\r\n          </button>\r\n          <button @click=\"generateDynamicWallpaper\" :disabled=\"isLoading || !currentWallpaper\">\r\n            生成动态壁纸\r\n          </button>\r\n          <button @click=\"resetToDefault\">\r\n            恢复默认\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 配置按钮 -->\r\n    <button \r\n      class=\"config-toggle\"\r\n      @click=\"showConfig = !showConfig\"\r\n      :class=\"{ 'active': showConfig }\"\r\n    >\r\n      <i class=\"fas fa-cog\"></i>\r\n    </button>\r\n    \r\n    <!-- 动态壁纸预览模态框 -->\r\n    <div v-if=\"showDynamicPreview\" class=\"preview-modal\">\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>动态壁纸预览</h3>\r\n          <button @click=\"closeDynamicPreview\" class=\"close-btn\">×</button>\r\n        </div>\r\n        <div class=\"preview-body\">\r\n          <div class=\"preview-video-container\">\r\n            <video \r\n              v-if=\"previewVideoUrl\"\r\n              class=\"preview-video\"\r\n              :src=\"previewVideoUrl\"\r\n              autoplay\r\n              muted\r\n              loop\r\n              playsinline\r\n            ></video>\r\n          </div>\r\n          <div class=\"preview-info\">\r\n            <p>动态壁纸已生成完成！</p>\r\n            <p>预览视频效果，决定是否应用为动态壁纸。</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"preview-actions\">\r\n          <button @click=\"applyDynamicWallpaper\" class=\"apply-btn\">\r\n            <i class=\"fas fa-check\"></i> 应用动态壁纸\r\n          </button>\r\n          <button @click=\"closeDynamicPreview\" class=\"cancel-btn\">\r\n            <i class=\"fas fa-times\"></i> 取消\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'\r\nimport ImageGenerationService from '@/services/ImageGenerationService'\r\nimport DynamicWallpaperService from '@/services/DynamicWallpaperService'\r\nimport ColorExtractor from '@/utils/ColorExtractor'\r\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer'\r\n\r\nexport default {\r\n  name: 'DynamicWallpaperManager',\r\n  \r\n  props: {\r\n    scenePrompt: {\r\n      type: String,\r\n      default: '现代简约风格，商务氛围'\r\n    },\r\n    autoGenerate: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    enableConfig: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  emits: ['wallpaper-changed', 'colors-extracted'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式数据\r\n    const isLoading = ref(false)\r\n    const showConfig = ref(false)\r\n    const currentWallpaper = ref(null)\r\n    const extractedColors = ref(null)\r\n    const loadingText = ref('正在生成壁纸...')\r\n    const showDynamicPreview = ref(false)\r\n    const previewVideoUrl = ref('')\r\n    const pendingDynamicWallpaper = ref(null)\r\n    \r\n    // 配置\r\n    const config = reactive({\r\n      enabled: true,\r\n      autoGenerate: true,\r\n      opacity: 0.8,\r\n      cardOpacity: 0.3,\r\n      contrastBoost: 1.2\r\n    })\r\n    \r\n    // 服务实例\r\n    const imageService = new ImageGenerationService()\r\n    const dynamicWallpaperService = new DynamicWallpaperService()\r\n    \r\n    // 计算属性\r\n    const defaultGradient = computed(() => ({\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\r\n    }))\r\n    \r\n    const overlayStyles = computed(() => {\r\n      if (!extractedColors.value) return {}\r\n      \r\n      return {\r\n        '--glass-bg': extractedColors.value.glassBackground,\r\n        '--glass-border': extractedColors.value.glassBorder,\r\n        '--text-color': extractedColors.value.text,\r\n        '--card-opacity': config.cardOpacity\r\n      }\r\n    })\r\n    \r\n    // 方法\r\n    const generateWallpaper = async (prompt = props.scenePrompt) => {\r\n      if (!config.enabled) return\r\n      \r\n      isLoading.value = true\r\n      \r\n      try {\r\n        console.log('开始生成壁纸，提示词:', prompt)\r\n        const result = await imageService.generateWallpaper(prompt)\r\n        \r\n        if (result && result.imageUrl) {\r\n          currentWallpaper.value = {\r\n            type: 'image',\r\n            url: result.imageUrl,\r\n            prompt: result.prompt,\r\n            taskId: result.taskId\r\n          }\r\n          \r\n          // 提取颜色，传递提示词用于智能降级\r\n          await extractColors(result.imageUrl, prompt)\r\n          \r\n          // 保存到历史\r\n          imageService.saveToHistory(result)\r\n          \r\n          emit('wallpaper-changed', currentWallpaper.value)\r\n          console.log('壁纸生成成功:', result.imageUrl)\r\n        }\r\n      } catch (error) {\r\n        console.error('壁纸生成失败:', error)\r\n        // 使用默认壁纸\r\n        resetToDefault()\r\n      } finally {\r\n        isLoading.value = false\r\n      }\r\n    }\r\n    \r\n    const extractColors = async (imageUrl, prompt = props.scenePrompt, optimizedPrompt = '') => {\r\n      try {\r\n        console.log('🎨 开始AI智能配色分析...')\r\n\r\n        // 使用AI智能配色分析器\r\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(\r\n          imageUrl,\r\n          prompt,\r\n          optimizedPrompt\r\n        )\r\n\r\n        extractedColors.value = intelligentColors\r\n        emit('colors-extracted', intelligentColors)\r\n        console.log('🎨 AI智能配色完成:', intelligentColors)\r\n\r\n        // 如果有AI分析结果，显示详细信息\r\n        if (intelligentColors.aiAnalysis) {\r\n          console.log('🧠 AI分析结果:', {\r\n            情感氛围: intelligentColors.aiAnalysis.mood,\r\n            整体亮度: intelligentColors.aiAnalysis.brightness,\r\n            氛围描述: intelligentColors.aiAnalysis.atmosphere,\r\n            主要颜色: intelligentColors.aiAnalysis.dominantColors\r\n          })\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI配色分析失败:', error)\r\n        // 降级到传统颜色提取\r\n        try {\r\n          const colors = await ColorExtractor.extractColors(imageUrl, prompt)\r\n          extractedColors.value = colors\r\n          emit('colors-extracted', colors)\r\n          console.log('降级到传统颜色提取:', colors)\r\n        } catch (fallbackError) {\r\n          console.error('传统颜色提取也失败:', fallbackError)\r\n          // 最终降级到场景智能颜色\r\n          const fallbackColors = ColorExtractor.getSceneBasedColors(prompt)\r\n          extractedColors.value = fallbackColors\r\n          emit('colors-extracted', fallbackColors)\r\n          console.log('使用场景智能颜色:', fallbackColors)\r\n        }\r\n      }\r\n    }\r\n    \r\n    const generateNewWallpaper = () => {\r\n      generateWallpaper()\r\n    }\r\n    \r\n    const generateDynamicWallpaper = async () => {\r\n      if (!currentWallpaper.value || currentWallpaper.value.type !== 'image') {\r\n        console.warn('当前没有可用的静态壁纸，无法生成动态壁纸')\r\n        return\r\n      }\r\n\r\n      isLoading.value = true\r\n      loadingText.value = '正在生成动态壁纸...这可能需要2-3分钟，请耐心等待...'\r\n\r\n      try {\r\n        console.log('开始智能生成动态壁纸')\r\n\r\n        // 获取当前壁纸信息\r\n        const wallpaperInfo = imageService.getCurrentWallpaperInfo()\r\n        const taskId = dynamicWallpaperService.generateTaskId()\r\n\r\n        let imageFile = null\r\n        let imageUrl = null\r\n\r\n        // 优先尝试从本地路径加载文件\r\n        if (wallpaperInfo && wallpaperInfo.imagePath) {\r\n          console.log('尝试从本地路径加载文件:', wallpaperInfo.imagePath)\r\n          imageFile = await imageService.loadFileFromPath(wallpaperInfo.imagePath)\r\n        }\r\n\r\n        // 如果本地文件加载失败，使用URL模式\r\n        if (!imageFile && wallpaperInfo && wallpaperInfo.imageUrl) {\r\n          console.log('本地文件不可用，使用URL模式:', wallpaperInfo.imageUrl)\r\n          imageUrl = wallpaperInfo.imageUrl\r\n        }\r\n\r\n        // 如果都没有，尝试从当前壁纸URL创建文件\r\n        if (!imageFile && !imageUrl && currentWallpaper.value.url) {\r\n          console.log('从当前壁纸URL创建文件:', currentWallpaper.value.url)\r\n          try {\r\n            const response = await fetch(currentWallpaper.value.url)\r\n            const blob = await response.blob()\r\n            imageFile = new File([blob], 'wallpaper.png', { type: 'image/png' })\r\n          } catch (error) {\r\n            console.error('从URL创建文件失败:', error)\r\n            imageUrl = currentWallpaper.value.url\r\n          }\r\n        }\r\n\r\n        // 调用智能生成服务\r\n        const result = await dynamicWallpaperService.generateDynamicWallpaper({\r\n          imageFile: imageFile,\r\n          imageUrl: imageUrl,\r\n          taskId: taskId,\r\n          onProgress: (progress) => {\r\n            loadingText.value = progress.message\r\n            console.log(`生成进度: ${progress.percentage}% - ${progress.message}`)\r\n          }\r\n        })\r\n\r\n        if (result && result.url) {\r\n          // 保存生成的动态壁纸信息，等待用户确认\r\n          pendingDynamicWallpaper.value = {\r\n            type: 'video',\r\n            url: result.url,\r\n            prompt: currentWallpaper.value.prompt,\r\n            taskId: result.taskId\r\n          }\r\n\r\n          // 显示预览模态框\r\n          previewVideoUrl.value = result.url\r\n          showDynamicPreview.value = true\r\n\r\n          console.log('动态壁纸生成成功:', result.url)\r\n        }\r\n      } catch (error) {\r\n        console.error('动态壁纸生成失败:', error)\r\n        loadingText.value = `生成失败: ${error.message}`\r\n\r\n        // 显示错误提示\r\n        setTimeout(() => {\r\n          loadingText.value = '正在生成壁纸...'\r\n        }, 3000)\r\n      } finally {\r\n        isLoading.value = false\r\n      }\r\n    }\r\n    \r\n    const applyDynamicWallpaper = () => {\r\n      if (pendingDynamicWallpaper.value) {\r\n        // 应用动态壁纸\r\n        currentWallpaper.value = { ...pendingDynamicWallpaper.value }\r\n        emit('wallpaper-changed', currentWallpaper.value)\r\n        console.log('动态壁纸已应用:', currentWallpaper.value.url)\r\n        \r\n        // 关闭预览模态框\r\n        closeDynamicPreview()\r\n      }\r\n    }\r\n    \r\n    const closeDynamicPreview = () => {\r\n      showDynamicPreview.value = false\r\n      previewVideoUrl.value = ''\r\n      pendingDynamicWallpaper.value = null\r\n    }\r\n    \r\n    const resetToDefault = () => {\r\n      currentWallpaper.value = null\r\n      // 使用基于当前场景的智能颜色\r\n      const smartColors = ColorExtractor.getSceneBasedColors(props.scenePrompt)\r\n      extractedColors.value = smartColors\r\n      emit('colors-extracted', smartColors)\r\n      console.log('重置为智能默认颜色:', smartColors)\r\n    }\r\n    \r\n    const onConfigChange = () => {\r\n      // 保存配置到本地存储\r\n      localStorage.setItem('wallpaper_config', JSON.stringify(config))\r\n      \r\n      // 如果禁用了动态壁纸，重置为默认\r\n      if (!config.enabled) {\r\n        resetToDefault()\r\n      }\r\n    }\r\n    \r\n    const loadConfig = () => {\r\n      const saved = localStorage.getItem('wallpaper_config')\r\n      if (saved) {\r\n        Object.assign(config, JSON.parse(saved))\r\n      }\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(async () => {\r\n      loadConfig()\r\n\r\n      // 确保每次页面加载都生成壁纸（如果启用了自动生成）\r\n      if (config.enabled && config.autoGenerate) {\r\n        await nextTick()\r\n        console.log('页面加载，开始自动生成壁纸')\r\n        generateWallpaper()\r\n      } else {\r\n        resetToDefault()\r\n      }\r\n    })\r\n    \r\n    // 监听场景提示词变化\r\n    watch(() => props.scenePrompt, (newPrompt) => {\r\n      if (config.enabled && config.autoGenerate) {\r\n        generateWallpaper(newPrompt)\r\n      }\r\n    })\r\n    \r\n    return {\r\n      isLoading,\r\n      showConfig,\r\n      currentWallpaper,\r\n      extractedColors,\r\n      config,\r\n      defaultGradient,\r\n      overlayStyles,\r\n      loadingText,\r\n      showDynamicPreview,\r\n      previewVideoUrl,\r\n      generateNewWallpaper,\r\n      generateDynamicWallpaper,\r\n      applyDynamicWallpaper,\r\n      closeDynamicPreview,\r\n      resetToDefault,\r\n      onConfigChange\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dynamic-wallpaper-manager {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.wallpaper-container {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1; /* 确保在最底层 */\r\n}\r\n\r\n.wallpaper-image {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.wallpaper-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.wallpaper-default {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.loading-spinner {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.loading-spinner i {\r\n  font-size: 32px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.content-overlay {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 5;\r\n}\r\n\r\n/* 全局CSS变量，供子组件使用 */\r\n.content-overlay :deep(.glass-card) {\r\n  background: var(--glass-bg, rgba(255, 255, 255, 0.15));\r\n  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.2));\r\n  color: var(--text-color, #ffffff);\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n}\r\n\r\n.config-toggle {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  width: 50px;\r\n  height: 50px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.config-toggle:hover,\r\n.config-toggle.active {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.config-panel {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: 20px;\r\n  width: 300px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  z-index: 1000;\r\n  color: #333;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #666;\r\n}\r\n\r\n.config-content {\r\n  padding: 20px;\r\n}\r\n\r\n.config-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.config-item label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.config-item input[type=\"range\"] {\r\n  flex: 1;\r\n  margin: 0 10px;\r\n}\r\n\r\n.config-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.config-actions button {\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  background: #4A90E2;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.config-actions button:hover {\r\n  background: #357ABD;\r\n}\r\n\r\n.config-actions button:disabled {\r\n  background: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 动态壁纸预览模态框 */\r\n.preview-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.preview-content {\r\n  width: 90%;\r\n  max-width: 800px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.preview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0;\r\n  color: #333;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-body {\r\n  padding: 20px;\r\n}\r\n\r\n.preview-video-container {\r\n  width: 100%;\r\n  height: 400px;\r\n  background: #000;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.preview-info {\r\n  text-align: center;\r\n  color: #666;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-info p {\r\n  margin: 5px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: center;\r\n}\r\n\r\n.apply-btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 10px;\r\n  background: #4CAF50;\r\n  color: white;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.apply-btn:hover {\r\n  background: #45a049;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.cancel-btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 10px;\r\n  background: #f44336;\r\n  color: white;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: #da190b;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 加载文本样式增强 */\r\n.loading-spinner span {\r\n  font-size: 14px;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n  max-width: 300px;\r\n}\r\n</style>\r\n"], "mappings": "AA2JA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAI;AACxE,OAAOC,sBAAqB,MAAO,mCAAkC;AACrE,OAAOC,uBAAsB,MAAO,oCAAmC;AACvE,OAAOC,cAAa,MAAO,wBAAuB;AAClD,OAAOC,eAAc,MAAO,yBAAwB;AAEpD,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAE/BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,YAAY,EAAE;MACZH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,YAAY,EAAE;MACZL,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX;EACF,CAAC;EAEDI,KAAK,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;EAEhDC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAItB,GAAG,CAAC,KAAK;IAC3B,MAAMuB,UAAS,GAAIvB,GAAG,CAAC,KAAK;IAC5B,MAAMwB,gBAAe,GAAIxB,GAAG,CAAC,IAAI;IACjC,MAAMyB,eAAc,GAAIzB,GAAG,CAAC,IAAI;IAChC,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,WAAW;IACnC,MAAM2B,kBAAiB,GAAI3B,GAAG,CAAC,KAAK;IACpC,MAAM4B,eAAc,GAAI5B,GAAG,CAAC,EAAE;IAC9B,MAAM6B,uBAAsB,GAAI7B,GAAG,CAAC,IAAI;;IAExC;IACA,MAAM8B,MAAK,GAAI7B,QAAQ,CAAC;MACtB8B,OAAO,EAAE,IAAI;MACbf,YAAY,EAAE,IAAI;MAClBgB,OAAO,EAAE,GAAG;MACZC,WAAW,EAAE,GAAG;MAChBC,aAAa,EAAE;IACjB,CAAC;;IAED;IACA,MAAMC,YAAW,GAAI,IAAI7B,sBAAsB,CAAC;IAChD,MAAM8B,uBAAsB,GAAI,IAAI7B,uBAAuB,CAAC;;IAE5D;IACA,MAAM8B,eAAc,GAAInC,QAAQ,CAAC,OAAO;MACtCoC,UAAU,EAAE;IACd,CAAC,CAAC;IAEF,MAAMC,aAAY,GAAIrC,QAAQ,CAAC,MAAM;MACnC,IAAI,CAACuB,eAAe,CAACe,KAAK,EAAE,OAAO,CAAC;MAEpC,OAAO;QACL,YAAY,EAAEf,eAAe,CAACe,KAAK,CAACC,eAAe;QACnD,gBAAgB,EAAEhB,eAAe,CAACe,KAAK,CAACE,WAAW;QACnD,cAAc,EAAEjB,eAAe,CAACe,KAAK,CAACG,IAAI;QAC1C,gBAAgB,EAAEb,MAAM,CAACG;MAC3B;IACF,CAAC;;IAED;IACA,MAAMW,iBAAgB,GAAI,MAAAA,CAAOC,MAAK,GAAIlC,KAAK,CAACC,WAAW,KAAK;MAC9D,IAAI,CAACkB,MAAM,CAACC,OAAO,EAAE;MAErBT,SAAS,CAACkB,KAAI,GAAI,IAAG;MAErB,IAAI;QACFM,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,MAAM;QACjC,MAAMG,MAAK,GAAI,MAAMb,YAAY,CAACS,iBAAiB,CAACC,MAAM;QAE1D,IAAIG,MAAK,IAAKA,MAAM,CAACC,QAAQ,EAAE;UAC7BzB,gBAAgB,CAACgB,KAAI,GAAI;YACvB3B,IAAI,EAAE,OAAO;YACbqC,GAAG,EAAEF,MAAM,CAACC,QAAQ;YACpBJ,MAAM,EAAEG,MAAM,CAACH,MAAM;YACrBM,MAAM,EAAEH,MAAM,CAACG;UACjB;;UAEA;UACA,MAAMC,aAAa,CAACJ,MAAM,CAACC,QAAQ,EAAEJ,MAAM;;UAE3C;UACAV,YAAY,CAACkB,aAAa,CAACL,MAAM;UAEjC3B,IAAI,CAAC,mBAAmB,EAAEG,gBAAgB,CAACgB,KAAK;UAChDM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,MAAM,CAACC,QAAQ;QACxC;MACF,EAAE,OAAOK,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B;QACAC,cAAc,CAAC;MACjB,UAAU;QACRjC,SAAS,CAACkB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAMY,aAAY,GAAI,MAAAA,CAAOH,QAAQ,EAAEJ,MAAK,GAAIlC,KAAK,CAACC,WAAW,EAAE4C,eAAc,GAAI,EAAE,KAAK;MAC1F,IAAI;QACFV,OAAO,CAACC,GAAG,CAAC,kBAAkB;;QAE9B;QACA,MAAMU,iBAAgB,GAAI,MAAMhD,eAAe,CAACiD,iCAAiC,CAC/ET,QAAQ,EACRJ,MAAM,EACNW,eACF;QAEA/B,eAAe,CAACe,KAAI,GAAIiB,iBAAgB;QACxCpC,IAAI,CAAC,kBAAkB,EAAEoC,iBAAiB;QAC1CX,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEU,iBAAiB;;QAE7C;QACA,IAAIA,iBAAiB,CAACE,UAAU,EAAE;UAChCb,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;YACxBa,IAAI,EAAEH,iBAAiB,CAACE,UAAU,CAACE,IAAI;YACvCC,IAAI,EAAEL,iBAAiB,CAACE,UAAU,CAACI,UAAU;YAC7CC,IAAI,EAAEP,iBAAiB,CAACE,UAAU,CAACM,UAAU;YAC7CC,IAAI,EAAET,iBAAiB,CAACE,UAAU,CAACQ;UACrC,CAAC;QACH;MAEF,EAAE,OAAOb,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI;UACF,MAAMc,MAAK,GAAI,MAAM5D,cAAc,CAAC4C,aAAa,CAACH,QAAQ,EAAEJ,MAAM;UAClEpB,eAAe,CAACe,KAAI,GAAI4B,MAAK;UAC7B/C,IAAI,CAAC,kBAAkB,EAAE+C,MAAM;UAC/BtB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqB,MAAM;QAClC,EAAE,OAAOC,aAAa,EAAE;UACtBvB,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEe,aAAa;UACzC;UACA,MAAMC,cAAa,GAAI9D,cAAc,CAAC+D,mBAAmB,CAAC1B,MAAM;UAChEpB,eAAe,CAACe,KAAI,GAAI8B,cAAa;UACrCjD,IAAI,CAAC,kBAAkB,EAAEiD,cAAc;UACvCxB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuB,cAAc;QACzC;MACF;IACF;IAEA,MAAME,oBAAmB,GAAIA,CAAA,KAAM;MACjC5B,iBAAiB,CAAC;IACpB;IAEA,MAAM6B,wBAAuB,GAAI,MAAAA,CAAA,KAAY;MAC3C,IAAI,CAACjD,gBAAgB,CAACgB,KAAI,IAAKhB,gBAAgB,CAACgB,KAAK,CAAC3B,IAAG,KAAM,OAAO,EAAE;QACtEiC,OAAO,CAAC4B,IAAI,CAAC,sBAAsB;QACnC;MACF;MAEApD,SAAS,CAACkB,KAAI,GAAI,IAAG;MACrBd,WAAW,CAACc,KAAI,GAAI,gCAA+B;MAEnD,IAAI;QACFM,OAAO,CAACC,GAAG,CAAC,YAAY;;QAExB;QACA,MAAM4B,aAAY,GAAIxC,YAAY,CAACyC,uBAAuB,CAAC;QAC3D,MAAMzB,MAAK,GAAIf,uBAAuB,CAACyC,cAAc,CAAC;QAEtD,IAAIC,SAAQ,GAAI,IAAG;QACnB,IAAI7B,QAAO,GAAI,IAAG;;QAElB;QACA,IAAI0B,aAAY,IAAKA,aAAa,CAACI,SAAS,EAAE;UAC5CjC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE4B,aAAa,CAACI,SAAS;UACnDD,SAAQ,GAAI,MAAM3C,YAAY,CAAC6C,gBAAgB,CAACL,aAAa,CAACI,SAAS;QACzE;;QAEA;QACA,IAAI,CAACD,SAAQ,IAAKH,aAAY,IAAKA,aAAa,CAAC1B,QAAQ,EAAE;UACzDH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4B,aAAa,CAAC1B,QAAQ;UACtDA,QAAO,GAAI0B,aAAa,CAAC1B,QAAO;QAClC;;QAEA;QACA,IAAI,CAAC6B,SAAQ,IAAK,CAAC7B,QAAO,IAAKzB,gBAAgB,CAACgB,KAAK,CAACU,GAAG,EAAE;UACzDJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvB,gBAAgB,CAACgB,KAAK,CAACU,GAAG;UACvD,IAAI;YACF,MAAM+B,QAAO,GAAI,MAAMC,KAAK,CAAC1D,gBAAgB,CAACgB,KAAK,CAACU,GAAG;YACvD,MAAMiC,IAAG,GAAI,MAAMF,QAAQ,CAACE,IAAI,CAAC;YACjCL,SAAQ,GAAI,IAAIM,IAAI,CAAC,CAACD,IAAI,CAAC,EAAE,eAAe,EAAE;cAAEtE,IAAI,EAAE;YAAY,CAAC;UACrE,EAAE,OAAOyC,KAAK,EAAE;YACdR,OAAO,CAACQ,KAAK,CAAC,aAAa,EAAEA,KAAK;YAClCL,QAAO,GAAIzB,gBAAgB,CAACgB,KAAK,CAACU,GAAE;UACtC;QACF;;QAEA;QACA,MAAMF,MAAK,GAAI,MAAMZ,uBAAuB,CAACqC,wBAAwB,CAAC;UACpEK,SAAS,EAAEA,SAAS;UACpB7B,QAAQ,EAAEA,QAAQ;UAClBE,MAAM,EAAEA,MAAM;UACdkC,UAAU,EAAGC,QAAQ,IAAK;YACxB5D,WAAW,CAACc,KAAI,GAAI8C,QAAQ,CAACC,OAAM;YACnCzC,OAAO,CAACC,GAAG,CAAC,SAASuC,QAAQ,CAACE,UAAU,OAAOF,QAAQ,CAACC,OAAO,EAAE;UACnE;QACF,CAAC;QAED,IAAIvC,MAAK,IAAKA,MAAM,CAACE,GAAG,EAAE;UACxB;UACArB,uBAAuB,CAACW,KAAI,GAAI;YAC9B3B,IAAI,EAAE,OAAO;YACbqC,GAAG,EAAEF,MAAM,CAACE,GAAG;YACfL,MAAM,EAAErB,gBAAgB,CAACgB,KAAK,CAACK,MAAM;YACrCM,MAAM,EAAEH,MAAM,CAACG;UACjB;;UAEA;UACAvB,eAAe,CAACY,KAAI,GAAIQ,MAAM,CAACE,GAAE;UACjCvB,kBAAkB,CAACa,KAAI,GAAI,IAAG;UAE9BM,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,MAAM,CAACE,GAAG;QACrC;MACF,EAAE,OAAOI,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC5B,WAAW,CAACc,KAAI,GAAI,SAASc,KAAK,CAACiC,OAAO,EAAC;;QAE3C;QACAE,UAAU,CAAC,MAAM;UACf/D,WAAW,CAACc,KAAI,GAAI,WAAU;QAChC,CAAC,EAAE,IAAI;MACT,UAAU;QACRlB,SAAS,CAACkB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAMkD,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI7D,uBAAuB,CAACW,KAAK,EAAE;QACjC;QACAhB,gBAAgB,CAACgB,KAAI,GAAI;UAAE,GAAGX,uBAAuB,CAACW;QAAM;QAC5DnB,IAAI,CAAC,mBAAmB,EAAEG,gBAAgB,CAACgB,KAAK;QAChDM,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEvB,gBAAgB,CAACgB,KAAK,CAACU,GAAG;;QAElD;QACAyC,mBAAmB,CAAC;MACtB;IACF;IAEA,MAAMA,mBAAkB,GAAIA,CAAA,KAAM;MAChChE,kBAAkB,CAACa,KAAI,GAAI,KAAI;MAC/BZ,eAAe,CAACY,KAAI,GAAI,EAAC;MACzBX,uBAAuB,CAACW,KAAI,GAAI,IAAG;IACrC;IAEA,MAAMe,cAAa,GAAIA,CAAA,KAAM;MAC3B/B,gBAAgB,CAACgB,KAAI,GAAI,IAAG;MAC5B;MACA,MAAMoD,WAAU,GAAIpF,cAAc,CAAC+D,mBAAmB,CAAC5D,KAAK,CAACC,WAAW;MACxEa,eAAe,CAACe,KAAI,GAAIoD,WAAU;MAClCvE,IAAI,CAAC,kBAAkB,EAAEuE,WAAW;MACpC9C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6C,WAAW;IACvC;IAEA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3B;MACAC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACnE,MAAM,CAAC;;MAE/D;MACA,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;QACnBwB,cAAc,CAAC;MACjB;IACF;IAEA,MAAM2C,UAAS,GAAIA,CAAA,KAAM;MACvB,MAAMC,KAAI,GAAIL,YAAY,CAACM,OAAO,CAAC,kBAAkB;MACrD,IAAID,KAAK,EAAE;QACTE,MAAM,CAACC,MAAM,CAACxE,MAAM,EAAEkE,IAAI,CAACO,KAAK,CAACJ,KAAK,CAAC;MACzC;IACF;;IAEA;IACAhG,SAAS,CAAC,YAAY;MACpB+F,UAAU,CAAC;;MAEX;MACA,IAAIpE,MAAM,CAACC,OAAM,IAAKD,MAAM,CAACd,YAAY,EAAE;QACzC,MAAMX,QAAQ,CAAC;QACfyC,OAAO,CAACC,GAAG,CAAC,eAAe;QAC3BH,iBAAiB,CAAC;MACpB,OAAO;QACLW,cAAc,CAAC;MACjB;IACF,CAAC;;IAED;IACAnD,KAAK,CAAC,MAAMO,KAAK,CAACC,WAAW,EAAG4F,SAAS,IAAK;MAC5C,IAAI1E,MAAM,CAACC,OAAM,IAAKD,MAAM,CAACd,YAAY,EAAE;QACzC4B,iBAAiB,CAAC4D,SAAS;MAC7B;IACF,CAAC;IAED,OAAO;MACLlF,SAAS;MACTC,UAAU;MACVC,gBAAgB;MAChBC,eAAe;MACfK,MAAM;MACNO,eAAe;MACfE,aAAa;MACbb,WAAW;MACXC,kBAAkB;MAClBC,eAAe;MACf4C,oBAAoB;MACpBC,wBAAwB;MACxBiB,qBAAqB;MACrBC,mBAAmB;MACnBpC,cAAc;MACdsC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}