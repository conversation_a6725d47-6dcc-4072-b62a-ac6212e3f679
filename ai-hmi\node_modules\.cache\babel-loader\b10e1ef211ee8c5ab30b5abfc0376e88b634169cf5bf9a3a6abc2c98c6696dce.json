{"ast": null, "code": "import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderSlot as _renderSlot, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"card-header\"\n};\nconst _hoisted_2 = {\n  class: \"card-title\"\n};\nconst _hoisted_3 = {\n  class: \"card-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"glass-card\",\n    style: _normalizeStyle($setup.cardStyles)\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"i\", {\n    class: _normalizeClass([$props.icon, \"card-icon\"])\n  }, null, 2 /* CLASS */), _createElementVNode(\"h3\", _hoisted_2, _toDisplayString($props.title), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_3, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)])], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "style", "_normalizeStyle", "$setup", "cardStyles", "_createElementVNode", "_hoisted_1", "_normalizeClass", "$props", "icon", "_hoisted_2", "_toDisplayString", "title", "_hoisted_3", "_renderSlot", "_ctx", "$slots", "undefined"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\GlassCard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"glass-card\" :style=\"cardStyles\">\r\n    <div class=\"card-header\">\r\n      <i :class=\"icon\" class=\"card-icon\"></i>\r\n      <h3 class=\"card-title\">{{ title }}</h3>\r\n    </div>\r\n    <div class=\"card-content\">\r\n      <slot></slot>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { computed } from 'vue'\r\n\r\nexport default {\r\n  name: 'GlassC<PERSON>',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: 'fas fa-cube'\r\n    },\r\n    style: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n\r\n  setup(props) {\r\n    const cardStyles = computed(() => {\r\n      // 使用主题颜色或默认值\r\n      const colors = props.themeColors || {\r\n        glassBackground: 'rgba(255, 255, 255, 0.15)',\r\n        glassBorder: 'rgba(255, 255, 255, 0.2)',\r\n        text: '#ffffff',\r\n        cardTitleColor: '#ffffff',\r\n        cardContentColor: '#ecf0f1',\r\n        buttonBackground: 'rgba(74, 144, 226, 0.8)',\r\n        buttonColor: '#FFFFFF',\r\n        buttonBorder: 'rgba(255, 255, 255, 0.6)',\r\n        buttonHoverBackground: 'rgba(104, 174, 256, 0.9)',\r\n        buttonTextShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',\r\n        backgroundBrightness: 128\r\n      }\r\n\r\n      // 检查是否有AI增强的智能样式\r\n      const useIntelligentStyles = colors.isAIEnhanced && colors.intelligentCardStyles\r\n\r\n      // 根据背景亮度调整阴影\r\n      const shadowIntensity = colors.backgroundBrightness > 150 ? 0.2 : 0.1\r\n      const textShadowColor = colors.backgroundBrightness > 150 ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'\r\n\r\n      // 如果有AI智能样式，优先使用\r\n      if (useIntelligentStyles) {\r\n        const intelligentStyles = colors.intelligentCardStyles\r\n        const intelligentTextColors = colors.intelligentTextColors || {}\r\n        const intelligentButtonStyles = colors.intelligentButtonStyles || {}\r\n\r\n        return {\r\n          // AI智能样式\r\n          borderRadius: intelligentStyles.borderRadius || props.style.borderRadius || '16px',\r\n          backdropFilter: intelligentStyles.backdropFilter || props.style.backdropFilter || 'blur(12px)',\r\n          background: intelligentStyles.background || props.style.background || colors.glassBackground,\r\n          border: intelligentStyles.border || props.style.border || `1px solid ${colors.glassBorder}`,\r\n          boxShadow: intelligentStyles.boxShadow || props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\r\n          color: intelligentTextColors.content || colors.cardContentColor || colors.text,\r\n          // AI增强的CSS变量\r\n          '--button-bg': intelligentButtonStyles.background || colors.buttonBackground,\r\n          '--button-color': intelligentButtonStyles.color || colors.buttonColor,\r\n          '--button-border': intelligentButtonStyles.border || colors.buttonBorder,\r\n          '--button-hover-bg': intelligentButtonStyles.hoverBackground || colors.buttonHoverBackground,\r\n          '--button-text-shadow': intelligentButtonStyles.textShadow || colors.buttonTextShadow,\r\n          '--card-title-color': intelligentTextColors.title || colors.cardTitleColor || colors.text,\r\n          '--card-content-color': intelligentTextColors.content || colors.cardContentColor || colors.text,\r\n          '--text-shadow': intelligentTextColors.textShadow || `0 1px 2px ${textShadowColor}`,\r\n          // AI分析信息\r\n          '--ai-mood': colors.aiAnalysis?.mood || 'default',\r\n          '--ai-brightness': colors.aiAnalysis?.brightness || 'medium'\r\n        }\r\n      }\r\n\r\n      // 传统样式（降级方案）\r\n      return {\r\n        borderRadius: props.style.borderRadius || '16px',\r\n        backdropFilter: props.style.backdropFilter || 'blur(12px)',\r\n        background: props.style.background || colors.glassBackground,\r\n        border: props.style.border || `1px solid ${colors.glassBorder}`,\r\n        boxShadow: props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\r\n        color: colors.cardContentColor || colors.text,\r\n        // 传统CSS变量\r\n        '--button-bg': colors.buttonBackground,\r\n        '--button-color': colors.buttonColor,\r\n        '--button-border': colors.buttonBorder,\r\n        '--button-hover-bg': colors.buttonHoverBackground,\r\n        '--button-text-shadow': colors.buttonTextShadow,\r\n        '--card-title-color': colors.cardTitleColor || colors.text,\r\n        '--card-content-color': colors.cardContentColor || colors.text,\r\n        '--text-shadow': `0 1px 2px ${textShadowColor}`\r\n      }\r\n    })\r\n\r\n    return {\r\n      cardStyles\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.glass-card {\r\n  padding: 20px;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.glass-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.card-icon {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n  color: var(--card-title-color, #ffffff);\r\n  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n}\r\n\r\n.card-content {\r\n  flex: 1;\r\n  color: var(--card-content-color, #ecf0f1);\r\n  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n}\r\n\r\n/* 玻璃态效果增强 */\r\n.glass-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, \r\n    transparent, \r\n    rgba(255, 255, 255, 0.3), \r\n    transparent\r\n  );\r\n}\r\n\r\n.glass-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 1px;\r\n  background: linear-gradient(180deg, \r\n    transparent, \r\n    rgba(255, 255, 255, 0.3), \r\n    transparent\r\n  );\r\n}\r\n</style>"], "mappings": ";;EAESA,KAAK,EAAC;AAAa;;EAElBA,KAAK,EAAC;AAAY;;EAEnBA,KAAK,EAAC;AAAc;;uBAL3BC,mBAAA,CAQM;IARDD,KAAK,EAAC,YAAY;IAAEE,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,UAAU;MACxCC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAAuC;IAAnCN,KAAK,EAAAQ,eAAA,EAAEC,MAAA,CAAAC,IAAI,EAAQ,WAAW;2BAClCJ,mBAAA,CAAuC,MAAvCK,UAAuC,EAAAC,gBAAA,CAAbH,MAAA,CAAAI,KAAK,iB,GAEjCP,mBAAA,CAEM,OAFNQ,UAEM,GADJC,WAAA,CAAaC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}