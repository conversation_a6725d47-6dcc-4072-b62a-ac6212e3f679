{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderSlot as _renderSlot, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"card-header\"\n};\nconst _hoisted_2 = {\n  class: \"header-left\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"card-title\"\n};\nconst _hoisted_4 = {\n  class: \"header-right\"\n};\nconst _hoisted_5 = {\n  class: \"default-content\"\n};\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  key: 1,\n  class: \"card-footer\"\n};\nconst _hoisted_8 = {\n  class: \"footer-actions\"\n};\nconst _hoisted_9 = {\n  key: 2,\n  class: \"loading-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass(['base-card', `card-${$props.cardType}`, `size-${$props.size}`, $setup.themeClass]),\n    style: _normalizeStyle($setup.cardStyles),\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.handleClick && $setup.handleClick(...args))\n  }, [_createCommentVNode(\" 卡片头部 \"), $props.showHeader ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$props.icon ? (_openBlock(), _createElementBlock(\"i\", {\n    key: 0,\n    class: _normalizeClass([$props.icon, \"card-icon\"])\n  }, null, 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true), $props.title ? (_openBlock(), _createElementBlock(\"h3\", _hoisted_3, _toDisplayString($props.title), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_4, [_renderSlot(_ctx.$slots, \"header-actions\", {}, () => [$props.closable ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = _withModifiers($event => _ctx.$emit('close'), [\"stop\"])),\n    class: \"close-btn\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], true)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 卡片内容 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"card-content\", {\n      'no-header': !$props.showHeader\n    }])\n  }, [_renderSlot(_ctx.$slots, \"default\", {}, () => [_createElementVNode(\"div\", _hoisted_5, [$props.description ? (_openBlock(), _createElementBlock(\"p\", _hoisted_6, _toDisplayString($props.description), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])], true)], 2 /* CLASS */), _createCommentVNode(\" 卡片底部 \"), $props.showFooter ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_renderSlot(_ctx.$slots, \"footer\", {}, () => [_createElementVNode(\"div\", _hoisted_8, [$props.actionable ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[1] || (_cache[1] = _withModifiers((...args) => $setup.handleAction && $setup.handleAction(...args), [\"stop\"])),\n    class: \"action-btn\"\n  }, _toDisplayString($props.actionText || '操作'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])], true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $props.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  })], -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "cardType", "size", "$setup", "themeClass", "style", "_normalizeStyle", "cardStyles", "onClick", "_cache", "args", "handleClick", "_createCommentVNode", "showHeader", "_hoisted_1", "_createElementVNode", "_hoisted_2", "icon", "title", "_hoisted_3", "_toDisplayString", "_hoisted_4", "_renderSlot", "_ctx", "$slots", "closable", "_withModifiers", "$event", "$emit", "_hoisted_5", "description", "_hoisted_6", "showFooter", "_hoisted_7", "_hoisted_8", "actionable", "handleAction", "actionText", "loading", "_hoisted_9"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\BaseCard.vue"], "sourcesContent": ["<template>\n  <div \n    :class=\"['base-card', `card-${cardType}`, `size-${size}`, themeClass]\"\n    :style=\"cardStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 卡片头部 -->\n    <div v-if=\"showHeader\" class=\"card-header\">\n      <div class=\"header-left\">\n        <i v-if=\"icon\" :class=\"icon\" class=\"card-icon\"></i>\n        <h3 v-if=\"title\" class=\"card-title\">{{ title }}</h3>\n      </div>\n      <div class=\"header-right\">\n        <slot name=\"header-actions\">\n          <button v-if=\"closable\" @click.stop=\"$emit('close')\" class=\"close-btn\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </slot>\n      </div>\n    </div>\n\n    <!-- 卡片内容 -->\n    <div class=\"card-content\" :class=\"{ 'no-header': !showHeader }\">\n      <slot>\n        <div class=\"default-content\">\n          <p v-if=\"description\">{{ description }}</p>\n        </div>\n      </slot>\n    </div>\n\n    <!-- 卡片底部 -->\n    <div v-if=\"showFooter\" class=\"card-footer\">\n      <slot name=\"footer\">\n        <div class=\"footer-actions\">\n          <button v-if=\"actionable\" @click.stop=\"handleAction\" class=\"action-btn\">\n            {{ actionText || '操作' }}\n          </button>\n        </div>\n      </slot>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <div class=\"loading-spinner\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store'\n\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 卡片类型\n    cardType: {\n      type: String,\n      default: 'default'\n    },\n    \n    // 卡片尺寸\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large', 'custom'].includes(value)\n    },\n    \n    // 自定义尺寸（当size为custom时使用）\n    customSize: {\n      type: Object,\n      default: () => ({ width: 4, height: 2 })\n    },\n    \n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 1 })\n    },\n    \n    // 卡片标题\n    title: {\n      type: String,\n      default: ''\n    },\n    \n    // 卡片描述\n    description: {\n      type: String,\n      default: ''\n    },\n    \n    // 卡片图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    \n    // 主题\n    theme: {\n      type: String,\n      default: 'glass',\n      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\n    },\n    \n    // 主题颜色\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    \n    // 显示配置\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    \n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    \n    actionable: {\n      type: Boolean,\n      default: false\n    },\n    \n    actionText: {\n      type: String,\n      default: ''\n    },\n    \n    closable: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    \n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  \n  emits: ['click', 'action', 'close'],\n  \n  setup(props, { emit }) {\n    const layoutStore = useLayoutStore()\n    \n    // 计算卡片样式\n    const cardStyles = computed(() => {\n      let gridStyle = {}\n      \n      // 根据尺寸类型获取网格样式\n      if (props.size === 'custom') {\n        gridStyle = layoutStore.getComponentPosition('custom', props.position)\n        // 注册自定义尺寸\n        layoutStore.registerComponentSize('custom', props.customSize)\n      } else {\n        const sizeMap = {\n          small: 'cardSmall',\n          medium: 'cardMedium', \n          large: 'cardLarge'\n        }\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position)\n      }\n      \n      // 主题颜色样式\n      const themeStyle = {\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\n        '--card-text-color': props.themeColors.text || '#ffffff'\n      }\n      \n      return {\n        ...gridStyle,\n        ...themeStyle\n      }\n    })\n    \n    // 主题类名\n    const themeClass = computed(() => {\n      return `theme-${props.theme}`\n    })\n    \n    // 处理点击事件\n    const handleClick = () => {\n      if (props.clickable && !props.disabled) {\n        emit('click')\n      }\n    }\n    \n    // 处理操作事件\n    const handleAction = () => {\n      if (props.actionable && !props.disabled) {\n        emit('action')\n      }\n    }\n    \n    return {\n      cardStyles,\n      themeClass,\n      handleClick,\n      handleAction\n    }\n  }\n}\n</script>\n\n<style scoped>\n.base-card {\n  position: relative;\n  border-radius: 15px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: default;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 主题样式 */\n.theme-glass {\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.theme-solid {\n  background: var(--card-primary-color, #4a90e2);\n  border: none;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.theme-minimal {\n  background: transparent;\n  border: 1px solid var(--card-primary-color, #4a90e2);\n  box-shadow: none;\n}\n\n.theme-gradient {\n  background: linear-gradient(135deg, \n    var(--card-primary-color, #4a90e2) 0%, \n    var(--card-secondary-color, #7ed321) 100%);\n  border: none;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n}\n\n/* 可点击状态 */\n.base-card.clickable {\n  cursor: pointer;\n}\n\n.base-card.clickable:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);\n}\n\n/* 禁用状态 */\n.base-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 卡片头部 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.card-icon {\n  font-size: 18px;\n  color: var(--card-primary-color, #4a90e2);\n}\n\n.card-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--card-text-color, #ffffff);\n}\n\n.close-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n/* 卡片内容 */\n.card-content {\n  flex: 1;\n  padding: 20px;\n  color: var(--card-text-color, #ffffff);\n  overflow: hidden;\n}\n\n.card-content.no-header {\n  padding-top: 20px;\n}\n\n.default-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n/* 卡片底部 */\n.card-footer {\n  padding: 15px 20px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.footer-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: 1px solid var(--card-primary-color, #4a90e2);\n  background: var(--card-primary-color, #4a90e2);\n  color: white;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  background: var(--card-secondary-color, #7ed321);\n  border-color: var(--card-secondary-color, #7ed321);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(5px);\n}\n\n.loading-spinner {\n  color: var(--card-primary-color, #4a90e2);\n  font-size: 24px;\n}\n</style>\n"], "mappings": ";;;EAO2BA,KAAK,EAAC;;;EACtBA,KAAK,EAAC;AAAa;;;EAELA,KAAK,EAAC;;;EAEpBA,KAAK,EAAC;AAAc;;EAYlBA,KAAK,EAAC;AAAiB;;;;;;EAOTA,KAAK,EAAC;;;EAEpBA,KAAK,EAAC;AAAgB;;;EASXA,KAAK,EAAC;;;uBAzC5BC,mBAAA,CA8CM;IA7CHD,KAAK,EAAAE,eAAA,uBAAwBC,MAAA,CAAAC,QAAQ,YAAYD,MAAA,CAAAE,IAAI,IAAIC,MAAA,CAAAC,UAAU;IACnEC,KAAK,EAAAC,eAAA,CAAEH,MAAA,CAAAI,UAAU;IACjBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEP,MAAA,CAAAQ,WAAA,IAAAR,MAAA,CAAAQ,WAAA,IAAAD,IAAA,CAAW;MAEnBE,mBAAA,UAAa,EACFZ,MAAA,CAAAa,UAAU,I,cAArBf,mBAAA,CAYM,OAZNgB,UAYM,GAXJC,mBAAA,CAGM,OAHNC,UAGM,GAFKhB,MAAA,CAAAiB,IAAI,I,cAAbnB,mBAAA,CAAmD;;IAAnCD,KAAK,EAAAE,eAAA,EAAEC,MAAA,CAAAiB,IAAI,EAAQ,WAAW;gEACpCjB,MAAA,CAAAkB,KAAK,I,cAAfpB,mBAAA,CAAoD,MAApDqB,UAAoD,EAAAC,gBAAA,CAAbpB,MAAA,CAAAkB,KAAK,oB,qCAE9CH,mBAAA,CAMM,OANNM,UAMM,GALJC,WAAA,CAIOC,IAAA,CAAAC,MAAA,wBAJP,MAIO,CAHSxB,MAAA,CAAAyB,QAAQ,I,cAAtB3B,mBAAA,CAES;;IAFgBU,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiB,cAAA,CAAAC,MAAA,IAAOJ,IAAA,CAAAK,KAAK;IAAW/B,KAAK,EAAC;gCACzDkB,mBAAA,CAA4B;IAAzBlB,KAAK,EAAC;EAAc,0B,0FAM/Be,mBAAA,UAAa,EACbG,mBAAA,CAMM;IANDlB,KAAK,EAAAE,eAAA,EAAC,cAAc;MAAA,cAAyBC,MAAA,CAAAa;IAAU;MAC1DS,WAAA,CAIOC,IAAA,CAAAC,MAAA,iBAJP,MAIO,CAHLT,mBAAA,CAEM,OAFNc,UAEM,GADK7B,MAAA,CAAA8B,WAAW,I,cAApBhC,mBAAA,CAA2C,KAAAiC,UAAA,EAAAX,gBAAA,CAAlBpB,MAAA,CAAA8B,WAAW,oB,8DAK1ClB,mBAAA,UAAa,EACFZ,MAAA,CAAAgC,UAAU,I,cAArBlC,mBAAA,CAQM,OARNmC,UAQM,GAPJX,WAAA,CAMOC,IAAA,CAAAC,MAAA,gBANP,MAMO,CALLT,mBAAA,CAIM,OAJNmB,UAIM,GAHUlC,MAAA,CAAAmC,UAAU,I,cAAxBrC,mBAAA,CAES;;IAFkBU,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiB,cAAA,KAAAhB,IAAA,KAAOP,MAAA,CAAAiC,YAAA,IAAAjC,MAAA,CAAAiC,YAAA,IAAA1B,IAAA,CAAY;IAAEb,KAAK,EAAC;sBACtDG,MAAA,CAAAqC,UAAU,4B,oFAMrBzB,mBAAA,UAAa,EACFZ,MAAA,CAAAsC,OAAO,I,cAAlBxC,mBAAA,CAIM,OAJNyC,UAIM,EAAA9B,MAAA,QAAAA,MAAA,OAHJM,mBAAA,CAEM;IAFDlB,KAAK,EAAC;EAAiB,IAC1BkB,mBAAA,CAAsC;IAAnClB,KAAK,EAAC;EAAwB,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}