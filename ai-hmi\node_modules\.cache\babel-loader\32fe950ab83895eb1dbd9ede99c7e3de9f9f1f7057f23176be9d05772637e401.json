{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, renderSlot as _renderSlot, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"grid-lines\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"grid-system-16x9\", [`grid-mode-${$props.mode}`, {\n      'show-grid-lines': $props.showGridLines\n    }]]),\n    style: _normalizeStyle($setup.gridStyles)\n  }, [_createCommentVNode(\" 网格线显示 (调试用) \"), $props.showGridLines ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.gridColumns, i => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: `col-${i}`,\n      class: \"grid-line-vertical\",\n      style: _normalizeStyle({\n        gridColumn: i\n      })\n    }, null, 4 /* STYLE */);\n  }), 128 /* KEYED_FRAGMENT */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.gridRows, i => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: `row-${i}`,\n      class: \"grid-line-horizontal\",\n      style: _normalizeStyle({\n        gridRow: i\n      })\n    }, null, 4 /* STYLE */);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 网格内容 \"), _renderSlot(_ctx.$slots, \"default\", {\n    grid: $setup.gridInfo\n  }, undefined, true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "mode", "showGridLines", "style", "_normalizeStyle", "$setup", "gridStyles", "_createCommentVNode", "_hoisted_1", "_Fragment", "_renderList", "gridColumns", "i", "key", "gridColumn", "gridRows", "gridRow", "_renderSlot", "_ctx", "$slots", "grid", "gridInfo", "undefined"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\layout\\GridSystem16x9.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"grid-system-16x9\"\n    :style=\"gridStyles\"\n    :class=\"[\n      `grid-mode-${mode}`,\n      { 'show-grid-lines': showGridLines }\n    ]\"\n  >\n    <!-- 网格线显示 (调试用) -->\n    <div v-if=\"showGridLines\" class=\"grid-lines\">\n      <div \n        v-for=\"i in gridColumns\" \n        :key=\"`col-${i}`\" \n        class=\"grid-line-vertical\"\n        :style=\"{ gridColumn: i }\"\n      ></div>\n      <div \n        v-for=\"i in gridRows\" \n        :key=\"`row-${i}`\" \n        class=\"grid-line-horizontal\"\n        :style=\"{ gridRow: i }\"\n      ></div>\n    </div>\n\n    <!-- 网格内容 -->\n    <slot :grid=\"gridInfo\"></slot>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store/modules/layout'\n\nexport default {\n  name: 'GridSystem16x9',\n  props: {\n    mode: {\n      type: String,\n      default: 'standard', // standard, compact, expanded\n      validator: value => ['standard', 'compact', 'expanded'].includes(value)\n    },\n    showGridLines: {\n      type: Boolean,\n      default: false\n    },\n    customGap: {\n      type: [Number, String],\n      default: null\n    }\n  },\n  setup(props) {\n    const layoutStore = useLayoutStore()\n\n    const gridColumns = computed(() => layoutStore.gridColumns)\n    const gridRows = computed(() => layoutStore.gridRows)\n    \n    const gridGap = computed(() => {\n      if (props.customGap) {\n        return typeof props.customGap === 'number' ? `${props.customGap}px` : props.customGap\n      }\n      return `${layoutStore.gridGap}px`\n    })\n\n    const gridStyles = computed(() => {\n      const baseStyles = {\n        '--grid-columns': gridColumns.value,\n        '--grid-rows': gridRows.value,\n        '--grid-gap': gridGap.value,\n        display: 'grid',\n        gridTemplateColumns: `repeat(${gridColumns.value}, 1fr)`,\n        gridTemplateRows: `repeat(${gridRows.value}, 1fr)`,\n        gap: gridGap.value,\n        width: '100%',\n        height: '100%'\n      }\n\n      // 根据模式调整样式\n      switch (props.mode) {\n        case 'compact':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 0.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 0.5)`\n          }\n        case 'expanded':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 1.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 1.5)`\n          }\n        default:\n          return baseStyles\n      }\n    })\n\n    const gridInfo = computed(() => ({\n      columns: gridColumns.value,\n      rows: gridRows.value,\n      gap: gridGap.value,\n      mode: props.mode,\n      cellSize: layoutStore.gridCellSize\n    }))\n\n    // 网格位置计算工具\n    const getGridPosition = (column, row, colspan = 1, rowspan = 1) => {\n      return {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`\n      }\n    }\n\n    // 检查位置是否被占用\n    const isPositionOccupied = (column, row, colspan = 1, rowspan = 1, excludeId = null) => {\n      const components = layoutStore.visibleComponents\n      \n      for (const component of components) {\n        if (excludeId && component.id === excludeId) continue\n        \n        const { column: cCol, row: cRow, colspan: cColspan, rowspan: cRowspan } = component.position\n        \n        // 检查是否有重叠\n        const horizontalOverlap = column < cCol + cColspan && column + colspan > cCol\n        const verticalOverlap = row < cRow + cRowspan && row + rowspan > cRow\n        \n        if (horizontalOverlap && verticalOverlap) {\n          return true\n        }\n      }\n      \n      return false\n    }\n\n    // 查找可用位置\n    const findAvailablePosition = (colspan = 1, rowspan = 1) => {\n      for (let row = 1; row <= gridRows.value - rowspan + 1; row++) {\n        for (let column = 1; column <= gridColumns.value - colspan + 1; column++) {\n          if (!isPositionOccupied(column, row, colspan, rowspan)) {\n            return { column, row }\n          }\n        }\n      }\n      return null\n    }\n\n    return {\n      gridColumns,\n      gridRows,\n      gridStyles,\n      gridInfo,\n      getGridPosition,\n      isPositionOccupied,\n      findAvailablePosition\n    }\n  }\n}\n</script>\n\n<style scoped>\n.grid-system-16x9 {\n  position: relative;\n  box-sizing: border-box;\n}\n\n/* 网格线显示 */\n.grid-lines {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: grid;\n  grid-template-columns: repeat(var(--grid-columns), 1fr);\n  grid-template-rows: repeat(var(--grid-rows), 1fr);\n  gap: 0;\n  pointer-events: none;\n  z-index: 99999;\n}\n\n.grid-line-vertical {\n  border-left: 2px solid rgba(255, 255, 255, 0.8);\n  grid-row: 1 / -1;\n  box-shadow:\n    -1px 0 0 rgba(0, 0, 0, 0.3),\n    1px 0 0 rgba(0, 0, 0, 0.3);\n  position: relative;\n}\n\n.grid-line-vertical::before {\n  content: attr(data-col);\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  background: rgba(255, 255, 255, 0.9);\n  color: #333;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.grid-line-horizontal {\n  border-top: 2px solid rgba(255, 255, 255, 0.8);\n  grid-column: 1 / -1;\n  box-shadow:\n    0 -1px 0 rgba(0, 0, 0, 0.3),\n    0 1px 0 rgba(0, 0, 0, 0.3);\n  position: relative;\n}\n\n.grid-line-horizontal::before {\n  content: attr(data-row);\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  background: rgba(255, 255, 255, 0.9);\n  color: #333;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-size: 10px;\n  font-weight: bold;\n}\n\n/* 网格模式样式 */\n.grid-mode-standard {\n  /* 标准模式样式 */\n}\n\n.grid-mode-compact {\n  /* 紧凑模式样式 */\n}\n\n.grid-mode-expanded {\n  /* 扩展模式样式 */\n}\n\n/* 调试模式 */\n.show-grid-lines {\n  background-image:\n    linear-gradient(rgba(74, 144, 226, 0.3) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(74, 144, 226, 0.3) 1px, transparent 1px);\n  background-size:\n    calc(100% / var(--grid-columns))\n    calc(100% / var(--grid-rows));\n  position: relative;\n}\n\n.show-grid-lines::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    repeating-linear-gradient(\n      to right,\n      transparent 0,\n      transparent calc(100% / var(--grid-columns) - 1px),\n      rgba(74, 144, 226, 0.4) calc(100% / var(--grid-columns) - 1px),\n      rgba(74, 144, 226, 0.4) calc(100% / var(--grid-columns))\n    ),\n    repeating-linear-gradient(\n      to bottom,\n      transparent 0,\n      transparent calc(100% / var(--grid-rows) - 1px),\n      rgba(74, 144, 226, 0.4) calc(100% / var(--grid-rows) - 1px),\n      rgba(74, 144, 226, 0.4) calc(100% / var(--grid-rows))\n    );\n  pointer-events: none;\n  z-index: 10000;\n}\n\n/* 响应式调整 */\n@media (max-width: 1024px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.75);\n  }\n}\n\n@media (max-width: 768px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.5);\n  }\n}\n</style>\n"], "mappings": ";;;EAU8BA,KAAK,EAAC;;;uBATlCC,mBAAA,CA0BM;IAzBJD,KAAK,EAAAE,eAAA,EAAC,kBAAkB,G,aAEKC,MAAA,CAAAC,IAAI,I;yBAA+BD,MAAA,CAAAE;IAAa,E;IAD5EC,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,UAAU;MAMlBC,mBAAA,iBAAoB,EACTP,MAAA,CAAAE,aAAa,I,cAAxBJ,mBAAA,CAaM,OAbNU,UAaM,I,kBAZJV,mBAAA,CAKOW,SAAA,QAAAC,WAAA,CAJOL,MAAA,CAAAM,WAAW,EAAhBC,CAAC;yBADVd,mBAAA,CAKO;MAHJe,GAAG,SAASD,CAAC;MACdf,KAAK,EAAC,oBAAoB;MACzBM,KAAK,EAAAC,eAAA;QAAAU,UAAA,EAAgBF;MAAC;;qDAEzBd,mBAAA,CAKOW,SAAA,QAAAC,WAAA,CAJOL,MAAA,CAAAU,QAAQ,EAAbH,CAAC;yBADVd,mBAAA,CAKO;MAHJe,GAAG,SAASD,CAAC;MACdf,KAAK,EAAC,sBAAsB;MAC3BM,KAAK,EAAAC,eAAA;QAAAY,OAAA,EAAaJ;MAAC;;yEAIxBL,mBAAA,UAAa,EACbU,WAAA,CAA8BC,IAAA,CAAAC,MAAA;IAAvBC,IAAI,EAAEf,MAAA,CAAAgB;EAAQ,GAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}