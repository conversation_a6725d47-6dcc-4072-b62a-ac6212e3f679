{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"vpa-avatar-container\"\n};\nconst _hoisted_2 = {\n  class: \"vpa-avatar\"\n};\nconst _hoisted_3 = [\"src\", \"alt\"];\nconst _hoisted_4 = {\n  class: \"expression-overlay\"\n};\nconst _hoisted_5 = {\n  class: \"expression-emoji\"\n};\nconst _hoisted_6 = {\n  class: \"status-indicators\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"status-indicator listening\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"status-indicator speaking\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"vpa-info-panel\"\n};\nconst _hoisted_10 = {\n  class: \"vpa-greeting\"\n};\nconst _hoisted_11 = {\n  class: \"vpa-status\"\n};\nconst _hoisted_12 = {\n  class: \"mode-badge\"\n};\nconst _hoisted_13 = {\n  class: \"scene-badge\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"vpa-quick-actions\"\n};\nconst _hoisted_15 = {\n  class: \"btn-icon\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"vpa-digital-human\", [`vpa-mode-${$setup.vpaConfig.mode}`, `vpa-expression-${$setup.currentExpression}`, `vpa-action-${$setup.currentAction}`, {\n      'vpa-active': $setup.vpaConfig.isActive\n    }]]),\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.handleVPAClick && $setup.handleVPAClick(...args))\n  }, [_createCommentVNode(\" VPA头像容器 \"), _createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 动态背景光环 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"vpa-aura\", {\n      'pulsing': $setup.isListening || $setup.isSpeaking\n    }])\n  }, null, 2 /* CLASS */), _createCommentVNode(\" 头像图片 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"img\", {\n    src: `/docs/${$setup.vpaConfig.avatar}`,\n    alt: `VPA ${$setup.currentExpression}`,\n    class: \"avatar-image\",\n    onError: _cache[0] || (_cache[0] = (...args) => $setup.handleImageError && $setup.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_3), _createCommentVNode(\" 表情覆盖层 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.expressionEmoji), 1 /* TEXT */)])]), _createCommentVNode(\" 状态指示器 \"), _createElementVNode(\"div\", _hoisted_6, [$setup.isListening ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"pulse-ring\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    class: \"status-icon\"\n  }, \"🎤\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), $setup.isSpeaking ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n    class: \"wave-animation\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    class: \"status-icon\"\n  }, \"🔊\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" VPA信息面板 \"), $props.showInfoPanel ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.contextualGreeting), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"span\", _hoisted_12, _toDisplayString($setup.modeText), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_13, _toDisplayString($setup.sceneText), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 快速操作按钮 \"), $setup.vpaConfig.isActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"quick-action-btn\", {\n      active: $setup.isListening\n    }]),\n    onClick: _cache[1] || (_cache[1] = _withModifiers((...args) => $setup.toggleListening && $setup.toggleListening(...args), [\"stop\"]))\n  }, [_createElementVNode(\"span\", _hoisted_15, _toDisplayString($setup.isListening ? '⏹️' : '🎤'), 1 /* TEXT */)], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: \"quick-action-btn\",\n    onClick: _cache[2] || (_cache[2] = _withModifiers((...args) => $setup.switchVPAMode && $setup.switchVPAMode(...args), [\"stop\"]))\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"span\", {\n    class: \"btn-icon\"\n  }, \"🔄\", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$setup", "vpaConfig", "mode", "currentExpression", "currentAction", "isActive", "onClick", "_cache", "args", "handleVPAClick", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "isListening", "isSpeaking", "_hoisted_2", "src", "avatar", "alt", "onError", "handleImageError", "_hoisted_4", "_hoisted_5", "_toDisplayString", "expressionEmoji", "_hoisted_6", "_hoisted_7", "_hoisted_8", "$props", "showInfoPanel", "_hoisted_9", "_hoisted_10", "contextualGreeting", "_hoisted_11", "_hoisted_12", "modeText", "_hoisted_13", "sceneText", "_hoisted_14", "active", "_withModifiers", "toggleListening", "_hoisted_15", "switchVPAMode"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPADigitalHuman.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-digital-human\"\n    :class=\"[\n      `vpa-mode-${vpaConfig.mode}`,\n      `vpa-expression-${currentExpression}`,\n      `vpa-action-${currentAction}`,\n      { 'vpa-active': vpaConfig.isActive }\n    ]\"\n    @click=\"handleVPAClick\"\n  >\n    <!-- VPA头像容器 -->\n    <div class=\"vpa-avatar-container\">\n      <!-- 动态背景光环 -->\n      <div class=\"vpa-aura\" :class=\"{ 'pulsing': isListening || isSpeaking }\"></div>\n      \n      <!-- 头像图片 -->\n      <div class=\"vpa-avatar\">\n        <img \n          :src=\"`/docs/${vpaConfig.avatar}`\" \n          :alt=\"`VPA ${currentExpression}`\"\n          class=\"avatar-image\"\n          @error=\"handleImageError\"\n        />\n        \n        <!-- 表情覆盖层 -->\n        <div class=\"expression-overlay\">\n          <span class=\"expression-emoji\">{{ expressionEmoji }}</span>\n        </div>\n      </div>\n\n      <!-- 状态指示器 -->\n      <div class=\"status-indicators\">\n        <div v-if=\"isListening\" class=\"status-indicator listening\">\n          <div class=\"pulse-ring\"></div>\n          <span class=\"status-icon\">🎤</span>\n        </div>\n        <div v-if=\"isSpeaking\" class=\"status-indicator speaking\">\n          <div class=\"wave-animation\"></div>\n          <span class=\"status-icon\">🔊</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- VPA信息面板 -->\n    <div class=\"vpa-info-panel\" v-if=\"showInfoPanel\">\n      <div class=\"vpa-greeting\">\n        {{ contextualGreeting }}\n      </div>\n      \n      <div class=\"vpa-status\">\n        <span class=\"mode-badge\">{{ modeText }}</span>\n        <span class=\"scene-badge\">{{ sceneText }}</span>\n      </div>\n    </div>\n\n    <!-- 快速操作按钮 -->\n    <div class=\"vpa-quick-actions\" v-if=\"vpaConfig.isActive\">\n      <button \n        class=\"quick-action-btn\"\n        @click.stop=\"toggleListening\"\n        :class=\"{ active: isListening }\"\n      >\n        <span class=\"btn-icon\">{{ isListening ? '⏹️' : '🎤' }}</span>\n      </button>\n      \n      <button \n        class=\"quick-action-btn\"\n        @click.stop=\"switchVPAMode\"\n      >\n        <span class=\"btn-icon\">🔄</span>\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPADigitalHuman',\n  props: {\n    size: {\n      type: String,\n      default: 'medium', // small, medium, large\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    showInfoPanel: {\n      type: Boolean,\n      default: true\n    },\n    interactive: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(props) {\n    const vpaStore = useVPAStore()\n    const imageError = ref(false)\n\n    // 计算属性\n    const vpaConfig = computed(() => vpaStore.vpaDisplayConfig)\n    const currentExpression = computed(() => vpaStore.currentExpression)\n    const currentAction = computed(() => vpaStore.currentAction)\n    const isListening = computed(() => vpaStore.isListening)\n    const isSpeaking = computed(() => vpaStore.isSpeaking)\n    const contextualGreeting = computed(() => vpaStore.contextualGreeting)\n\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[currentExpression.value] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      }\n      return modes[vpaStore.currentMode] || '未知模式'\n    })\n\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭场景',\n        'focus': '专注场景',\n        'minimal': '简约场景',\n        'entertainment': '娱乐场景'\n      }\n      return scenes[vpaStore.currentScene] || '通用场景'\n    })\n\n    // 方法\n    const handleVPAClick = () => {\n      if (!props.interactive) return\n      \n      if (vpaStore.isActive) {\n        vpaStore.startConversation()\n      } else {\n        vpaStore.switchMode('companion')\n      }\n    }\n\n    const toggleListening = () => {\n      if (isListening.value) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n    }\n\n    const switchVPAMode = () => {\n      const modes = ['companion', 'interactive', 'restricted']\n      const currentIndex = modes.indexOf(vpaStore.currentMode)\n      const nextMode = modes[(currentIndex + 1) % modes.length]\n      vpaStore.switchMode(nextMode)\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    return {\n      vpaConfig,\n      currentExpression,\n      currentAction,\n      isListening,\n      isSpeaking,\n      contextualGreeting,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      imageError,\n      handleVPAClick,\n      toggleListening,\n      switchVPAMode,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-digital-human {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* VPA头像容器 */\n.vpa-avatar-container {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  margin-bottom: 10px;\n}\n\n/* 动态光环 */\n.vpa-aura {\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  border-radius: 50%;\n  background: radial-gradient(\n    circle,\n    rgba(74, 144, 226, 0.3) 0%,\n    rgba(74, 144, 226, 0.1) 50%,\n    transparent 100%\n  );\n  transition: all 0.3s ease;\n}\n\n.vpa-aura.pulsing {\n  animation: auraPulse 2s ease-in-out infinite;\n}\n\n@keyframes auraPulse {\n  0%, 100% { transform: scale(1); opacity: 0.3; }\n  50% { transform: scale(1.2); opacity: 0.6; }\n}\n\n/* 头像 */\n.vpa-avatar {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: all 0.3s ease;\n}\n\n.expression-overlay {\n  position: absolute;\n  bottom: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n/* 状态指示器 */\n.status-indicators {\n  position: absolute;\n  top: -15px;\n  right: -15px;\n}\n\n.status-indicator {\n  position: relative;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 2px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.3); }\n}\n\n/* 信息面板 */\n.vpa-info-panel {\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 8px 12px;\n  margin-bottom: 10px;\n  max-width: 200px;\n  text-align: center;\n}\n\n.vpa-greeting {\n  color: white;\n  font-size: 12px;\n  margin-bottom: 5px;\n  line-height: 1.3;\n}\n\n.vpa-status {\n  display: flex;\n  gap: 5px;\n  justify-content: center;\n}\n\n.mode-badge,\n.scene-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.scene-badge {\n  background: rgba(156, 39, 176, 0.8);\n}\n\n/* 快速操作按钮 */\n.vpa-quick-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.quick-action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.quick-action-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n.quick-action-btn.active {\n  background: rgba(76, 175, 80, 0.8);\n}\n\n.btn-icon {\n  font-size: 14px;\n}\n\n/* VPA模式样式 */\n.vpa-mode-companion .vpa-avatar {\n  border-color: rgba(76, 175, 80, 0.5);\n}\n\n.vpa-mode-interactive .vpa-avatar {\n  border-color: rgba(33, 150, 243, 0.5);\n}\n\n.vpa-mode-restricted .vpa-avatar {\n  border-color: rgba(158, 158, 158, 0.5);\n  opacity: 0.6;\n}\n\n/* 表情样式 */\n.vpa-expression-happy .avatar-image {\n  filter: brightness(1.1) saturate(1.2);\n}\n\n.vpa-expression-thinking .avatar-image {\n  filter: grayscale(0.3);\n}\n\n.vpa-expression-excited .avatar-image {\n  filter: brightness(1.2) saturate(1.3) hue-rotate(10deg);\n}\n\n/* 动作样式 */\n.vpa-action-listening .vpa-avatar {\n  animation: listeningBounce 2s ease-in-out infinite;\n}\n\n.vpa-action-speaking .vpa-avatar {\n  animation: speakingPulse 1s ease-in-out infinite;\n}\n\n@keyframes listeningBounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-5px); }\n}\n\n@keyframes speakingPulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .vpa-avatar-container {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .vpa-info-panel {\n    max-width: 150px;\n    padding: 6px 10px;\n  }\n  \n  .vpa-greeting {\n    font-size: 11px;\n  }\n}\n</style>\n"], "mappings": ";;EAYSA,KAAK,EAAC;AAAsB;;EAK1BA,KAAK,EAAC;AAAY;;;EAShBA,KAAK,EAAC;AAAoB;;EACvBA,KAAK,EAAC;AAAkB;;EAK7BA,KAAK,EAAC;AAAmB;;;EACJA,KAAK,EAAC;;;;EAIPA,KAAK,EAAC;;;;EAQ5BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;;EAKxBA,KAAK,EAAC;;;EAMDA,KAAK,EAAC;AAAU;;uBA9D5BC,mBAAA,CAwEM;IAvEJD,KAAK,EAAAE,eAAA,EAAC,mBAAmB,G,YACGC,MAAA,CAAAC,SAAS,CAACC,IAAI,I,kBAA4BF,MAAA,CAAAG,iBAAiB,I,cAAwBH,MAAA,CAAAI,aAAa,I;oBAA0BJ,MAAA,CAAAC,SAAS,CAACI;IAAQ,E;IAMvKC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAER,MAAA,CAAAS,cAAA,IAAAT,MAAA,CAAAS,cAAA,IAAAD,IAAA,CAAc;MAEtBE,mBAAA,aAAgB,EAChBC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJF,mBAAA,YAAe,EACfC,mBAAA,CAA8E;IAAzEd,KAAK,EAAAE,eAAA,EAAC,UAAU;MAAA,WAAsBC,MAAA,CAAAa,WAAW,IAAIb,MAAA,CAAAc;IAAU;2BAEpEJ,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNI,UAYM,GAXJJ,mBAAA,CAKE;IAJCK,GAAG,WAAWhB,MAAA,CAAAC,SAAS,CAACgB,MAAM;IAC9BC,GAAG,SAASlB,MAAA,CAAAG,iBAAiB;IAC9BN,KAAK,EAAC,cAAc;IACnBsB,OAAK,EAAAZ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAER,MAAA,CAAAoB,gBAAA,IAAApB,MAAA,CAAAoB,gBAAA,IAAAZ,IAAA,CAAgB;wDAG1BE,mBAAA,WAAc,EACdC,mBAAA,CAEM,OAFNU,UAEM,GADJV,mBAAA,CAA2D,QAA3DW,UAA2D,EAAAC,gBAAA,CAAzBvB,MAAA,CAAAwB,eAAe,iB,KAIrDd,mBAAA,WAAc,EACdC,mBAAA,CASM,OATNc,UASM,GAROzB,MAAA,CAAAa,WAAW,I,cAAtBf,mBAAA,CAGM,OAHN4B,UAGM,EAAAnB,MAAA,QAAAA,MAAA,OAFJI,mBAAA,CAA8B;IAAzBd,KAAK,EAAC;EAAY,2BACvBc,mBAAA,CAAmC;IAA7Bd,KAAK,EAAC;EAAa,GAAC,IAAE,mB,yCAEnBG,MAAA,CAAAc,UAAU,I,cAArBhB,mBAAA,CAGM,OAHN6B,UAGM,EAAApB,MAAA,QAAAA,MAAA,OAFJI,mBAAA,CAAkC;IAA7Bd,KAAK,EAAC;EAAgB,2BAC3Bc,mBAAA,CAAmC;IAA7Bd,KAAK,EAAC;EAAa,GAAC,IAAE,mB,6CAKlCa,mBAAA,aAAgB,EACkBkB,MAAA,CAAAC,aAAa,I,cAA/C/B,mBAAA,CASM,OATNgC,UASM,GARJnB,mBAAA,CAEM,OAFNoB,WAEM,EAAAR,gBAAA,CADDvB,MAAA,CAAAgC,kBAAkB,kBAGvBrB,mBAAA,CAGM,OAHNsB,WAGM,GAFJtB,mBAAA,CAA8C,QAA9CuB,WAA8C,EAAAX,gBAAA,CAAlBvB,MAAA,CAAAmC,QAAQ,kBACpCxB,mBAAA,CAAgD,QAAhDyB,WAAgD,EAAAb,gBAAA,CAAnBvB,MAAA,CAAAqC,SAAS,iB,0CAI1C3B,mBAAA,YAAe,EACsBV,MAAA,CAAAC,SAAS,CAACI,QAAQ,I,cAAvDP,mBAAA,CAeM,OAfNwC,WAeM,GAdJ3B,mBAAA,CAMS;IALPd,KAAK,EAAAE,eAAA,EAAC,kBAAkB;MAAAwC,MAAA,EAENvC,MAAA,CAAAa;IAAW;IAD5BP,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiC,cAAA,KAAAhC,IAAA,KAAOR,MAAA,CAAAyC,eAAA,IAAAzC,MAAA,CAAAyC,eAAA,IAAAjC,IAAA,CAAe;MAG5BG,mBAAA,CAA6D,QAA7D+B,WAA6D,EAAAnB,gBAAA,CAAnCvB,MAAA,CAAAa,WAAW,+B,kBAGvCF,mBAAA,CAKS;IAJPd,KAAK,EAAC,kBAAkB;IACvBS,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiC,cAAA,KAAAhC,IAAA,KAAOR,MAAA,CAAA2C,aAAA,IAAA3C,MAAA,CAAA2C,aAAA,IAAAnC,IAAA,CAAa;gCAE1BG,mBAAA,CAAgC;IAA1Bd,KAAK,EAAC;EAAU,GAAC,IAAE,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}