{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\n// 场景配置管理器 - 管理所有AI-HMI场景的配置\nexport default class SceneManager {\n  constructor() {\n    this.currentScene = 'default';\n    this.scenes = this.initializeScenes();\n    this.sceneHistory = [];\n    this.maxHistorySize = 10;\n  }\n  initializeScenes() {\n    return {\n      // 默认场景\n      default: {\n        id: 'default',\n        name: '默认模式',\n        description: '沉浸式桌面壁纸生成界面',\n        wallpaper: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        layout: 'immersive',\n        cards: ['vpaWidget'],\n        theme: 'immersive',\n        priority: 0,\n        autoSwitch: false\n      },\n      // 场景一：早高峰通勤（家庭出行）\n      morningCommuteFamily: {\n        id: 'morningCommuteFamily',\n        name: '家庭出行模式',\n        description: '送孩子上学的家庭通勤',\n        wallpaper: 'modern glass building, transparent texture, blurred background, warm morning light, suitable for car display',\n        layout: 'family',\n        cards: ['navigation', 'kidEducation', 'pedia', 'vpaWidget'],\n        theme: 'warm',\n        priority: 8,\n        autoSwitch: true,\n        triggers: ['time:7-9', 'passengers:child', 'destination:school']\n      },\n      // 场景二：早高峰通勤（专注模式）\n      morningCommuteFocus: {\n        id: 'morningCommuteFocus',\n        name: '专注通勤模式',\n        description: '独自前往公司的专注通勤',\n        wallpaper: 'relaxing music dynamic wallpaper, glass texture, minimalist style',\n        layout: 'focus',\n        cards: ['navigation', 'music', 'todo', 'orderStatus', 'vpaWidget'],\n        theme: 'calm',\n        priority: 7,\n        autoSwitch: true,\n        triggers: ['time:7-9', 'passengers:single', 'destination:work']\n      },\n      // 场景三：下班通勤\n      eveningCommute: {\n        id: 'eveningCommute',\n        name: '下班通勤模式',\n        description: '工作日下班回家',\n        wallpaper: 'evening city street view, glass reflection, warm lighting',\n        layout: 'relax',\n        cards: ['navigation', 'music', 'smartHome', 'todo', 'vpaWidget'],\n        theme: 'evening',\n        priority: 6,\n        autoSwitch: true,\n        triggers: ['time:17-19', 'destination:home']\n      },\n      // 场景四：车内等待/摸鱼\n      waitingMode: {\n        id: 'waitingMode',\n        name: '等待休息模式',\n        description: '驻车等待时的娱乐模式',\n        wallpaper: 'peaceful lake view, glass texture, relaxing atmosphere',\n        layout: 'entertainment',\n        cards: ['videoPlayer', 'news', 'ambientSound', 'vpaWidget'],\n        theme: 'relax',\n        priority: 5,\n        autoSwitch: true,\n        triggers: ['gear:P', 'duration:1min']\n      },\n      // 场景五：雨夜归途\n      rainyNight: {\n        id: 'rainyNight',\n        name: '雨夜模式',\n        description: '雨夜驾驶的安全模式',\n        wallpaper: 'blurry city night view with raindrops, glass texture, warm lighting',\n        layout: 'minimal',\n        cards: ['navigation', 'music', 'vpaWidget'],\n        theme: 'dark',\n        priority: 9,\n        autoSwitch: true,\n        triggers: ['weather:rain', 'time:night']\n      },\n      // 场景六：周末家庭出游\n      familyTrip: {\n        id: 'familyTrip',\n        name: '家庭出游模式',\n        description: '周末家庭旅行',\n        wallpaper: 'forest park, sunny day, glass texture, family atmosphere',\n        layout: 'family',\n        cards: ['navigation', 'rearSeatControl', 'facilityFinder', 'tripReminder', 'vpaWidget'],\n        theme: 'bright',\n        priority: 6,\n        autoSwitch: true,\n        triggers: ['time:weekend', 'destination:park', 'passengers:family']\n      },\n      // 场景七：长途高速驾驶\n      longDistance: {\n        id: 'longDistance',\n        name: '长途驾驶模式',\n        description: '高速公路长途驾驶',\n        wallpaper: 'highway landscape, glass texture, minimalist style',\n        layout: 'driving',\n        cards: ['navigation', 'serviceArea', 'driverStatus', 'vehicleStatus', 'vpaWidget'],\n        theme: 'functional',\n        priority: 8,\n        autoSwitch: true,\n        triggers: ['road:highway', 'duration:1hour']\n      },\n      // 场景八：访客/代驾模式\n      guestMode: {\n        id: 'guestMode',\n        name: '访客模式',\n        description: '保护隐私的访客模式',\n        wallpaper: 'neutral default wallpaper, simple glass texture',\n        layout: 'basic',\n        cards: ['tempNavigation', 'basicMusic', 'basicControl', 'vpaWidget'],\n        theme: 'neutral',\n        priority: 4,\n        autoSwitch: false,\n        triggers: ['manual:guest']\n      },\n      // 场景九：宠物模式\n      petMode: {\n        id: 'petMode',\n        name: '宠物模式',\n        description: '宠物留在车内的安全模式',\n        wallpaper: 'cute pet animation background, comfortable temperature display',\n        layout: 'pet',\n        cards: ['petInfo', 'climateControl', 'vpaWidget'],\n        theme: 'cute',\n        priority: 7,\n        autoSwitch: false,\n        triggers: ['manual:pet']\n      },\n      // 场景十：洗车模式\n      carWashMode: {\n        id: 'carWashMode',\n        name: '洗车模式',\n        description: '自动洗车准备模式',\n        wallpaper: 'car wash station background, glass texture',\n        layout: 'checklist',\n        cards: ['carWashChecklist', 'vpaWidget'],\n        theme: 'functional',\n        priority: 3,\n        autoSwitch: false,\n        triggers: ['manual:carWash']\n      },\n      // 场景十一：浪漫二人世界\n      romanticMode: {\n        id: 'romanticMode',\n        name: '浪漫模式',\n        description: '私密浪漫氛围',\n        wallpaper: 'dynamic starry sky or fireplace video, glass texture',\n        layout: 'romantic',\n        cards: ['romanticMusic', 'ambientLight', 'vpaWidget'],\n        theme: 'romantic',\n        priority: 5,\n        autoSwitch: false,\n        triggers: ['manual:romantic', 'calendar:anniversary']\n      },\n      // 场景十二：智能充电场景\n      chargingMode: {\n        id: 'chargingMode',\n        name: '充电模式',\n        description: '电动车充电管理',\n        wallpaper: 'charging station environment, glass texture',\n        layout: 'charging',\n        cards: ['chargingStatus', 'entertainment', 'nearbyShops', 'vpaWidget'],\n        theme: 'tech',\n        priority: 8,\n        autoSwitch: true,\n        triggers: ['battery:<30%', 'location:chargingStation']\n      },\n      // 场景十三：疲劳驾驶检测与干预\n      fatigueDetection: {\n        id: 'fatigueDetection',\n        name: '疲劳驾驶预警',\n        description: '疲劳驾驶安全干预',\n        wallpaper: 'warning color gradient background, glass texture',\n        layout: 'warning',\n        cards: ['fatigueWarning', 'restArea', 'refreshment', 'emergencyContact', 'vpaWidget'],\n        theme: 'warning',\n        priority: 10,\n        autoSwitch: true,\n        triggers: ['fatigue:detected', 'driving:2hours']\n      },\n      // 场景十四：多用户识别与切换\n      userSwitch: {\n        id: 'userSwitch',\n        name: '用户切换模式',\n        description: '多用户配置选择',\n        wallpaper: 'neutral user selection background, glass texture',\n        layout: 'userSelection',\n        cards: ['userSelector', 'userPreferences', 'privacySettings', 'vpaWidget'],\n        theme: 'neutral',\n        priority: 6,\n        autoSwitch: true,\n        triggers: ['newUser:detected', 'manual:userSwitch']\n      },\n      // 场景十五：智能泊车辅助\n      parkingMode: {\n        id: 'parkingMode',\n        name: '智能泊车模式',\n        description: '全方位泊车辅助',\n        wallpaper: 'parking lot environment, glass texture',\n        layout: 'parking',\n        cards: ['parkingSearch', 'parkingAssist', 'costInfo', 'vpaWidget'],\n        theme: 'functional',\n        priority: 7,\n        autoSwitch: true,\n        triggers: ['destination:near', 'speed:slow', 'manual:parking']\n      },\n      // 场景十六：紧急情况处理\n      emergencyMode: {\n        id: 'emergencyMode',\n        name: '紧急情况模式',\n        description: '紧急救援处理',\n        wallpaper: 'emergency red background, flashing warning',\n        layout: 'emergency',\n        cards: ['emergencyInfo', 'firstAid', 'emergencyContact', 'vpaWidget'],\n        theme: 'emergency',\n        priority: 11,\n        autoSwitch: true,\n        triggers: ['accident:detected', 'airbag:deployed', 'manual:emergency']\n      }\n    };\n  }\n\n  // 获取当前场景\n  getCurrentScene() {\n    return this.scenes[this.currentScene] || this.scenes.default;\n  }\n\n  // 切换场景\n  switchScene(sceneId, reason = 'manual') {\n    if (!this.scenes[sceneId]) {\n      console.warn(`Scene ${sceneId} not found`);\n      return false;\n    }\n\n    // 记录历史\n    this.sceneHistory.unshift({\n      from: this.currentScene,\n      to: sceneId,\n      reason,\n      timestamp: new Date().toISOString()\n    });\n\n    // 限制历史记录大小\n    if (this.sceneHistory.length > this.maxHistorySize) {\n      this.sceneHistory = this.sceneHistory.slice(0, this.maxHistorySize);\n    }\n    this.currentScene = sceneId;\n    console.log(`Scene switched to ${sceneId} (${reason})`);\n    return true;\n  }\n\n  // 获取所有场景\n  getAllScenes() {\n    return Object.values(this.scenes);\n  }\n\n  // 获取可自动切换的场景\n  getAutoSwitchScenes() {\n    return Object.values(this.scenes).filter(scene => scene.autoSwitch);\n  }\n\n  // 根据触发条件获取推荐场景\n  getRecommendedScenes(context = {}) {\n    const recommendations = [];\n    for (const [sceneId, scene] of Object.entries(this.scenes)) {\n      if (!scene.autoSwitch) continue;\n      let matchScore = 0;\n      for (const trigger of scene.triggers || []) {\n        if (this.evaluateTrigger(trigger, context)) {\n          matchScore += scene.priority;\n        }\n      }\n      if (matchScore > 0) {\n        recommendations.push({\n          sceneId,\n          scene,\n          score: matchScore\n        });\n      }\n    }\n    return recommendations.sort((a, b) => b.score - a.score);\n  }\n\n  // 评估触发条件\n  evaluateTrigger(trigger, context) {\n    const [type, value] = trigger.split(':');\n    switch (type) {\n      case 'time':\n        return this.evaluateTimeTrigger(value);\n      case 'passengers':\n        return this.evaluatePassengerTrigger(value, context);\n      case 'destination':\n        return this.evaluateDestinationTrigger(value, context);\n      case 'gear':\n        return context.gear === value;\n      case 'duration':\n        return this.evaluateDurationTrigger(value, context);\n      case 'weather':\n        return context.weather === value;\n      case 'road':\n        return context.roadType === value;\n      case 'battery':\n        return this.evaluateBatteryTrigger(value, context);\n      case 'location':\n        return context.locationType === value;\n      case 'fatigue':\n        return context.fatigueDetected;\n      case 'driving':\n        return this.evaluateDrivingTrigger(value, context);\n      case 'manual':\n        return context.manualTrigger === value;\n      case 'calendar':\n        return context.calendarEvents?.includes(value);\n      case 'newUser':\n        return context.newUserDetected;\n      case 'accident':\n        return context.accidentDetected;\n      case 'airbag':\n        return context.airbagDeployed;\n      default:\n        return false;\n    }\n  }\n\n  // 时间触发条件评估\n  evaluateTimeTrigger(value) {\n    // eslint-disable-next-line no-unused-vars\n    const now = new Date();\n    // eslint-disable-next-line no-unused-vars\n    const hour = now.getHours();\n    // eslint-disable-next-line no-unused-vars\n    const day = now.getDay();\n    if (value === '7-9') return hour >= 7 && hour <= 9;\n    if (value === '17-19') return hour >= 17 && hour <= 19;\n    if (value === 'night') return hour >= 20 || hour <= 6;\n    if (value === 'weekend') return day === 0 || day === 6;\n    return false;\n  }\n\n  // 乘客触发条件评估\n  evaluatePassengerTrigger(value, context) {\n    if (value === 'child') return context.passengers?.includes('child');\n    if (value === 'single') return context.passengerCount === 1;\n    if (value === 'family') return context.passengerCount > 2;\n    return false;\n  }\n\n  // 目的地触发条件评估\n  evaluateDestinationTrigger(value, context) {\n    if (!context.destination) return false;\n    const dest = context.destination.toLowerCase();\n    if (value === 'school') return dest.includes('school') || dest.includes('学校');\n    if (value === 'work') return dest.includes('work') || dest.includes('公司');\n    if (value === 'home') return dest.includes('home') || dest.includes('家');\n    if (value === 'park') return dest.includes('park') || dest.includes('公园');\n    return false;\n  }\n\n  // 持续时间触发条件评估\n  evaluateDurationTrigger(value, context) {\n    if (!context.drivingDuration) return false;\n    if (value === '1min') return context.drivingDuration >= 60000;\n    if (value === '1hour') return context.drivingDuration >= 3600000;\n    if (value === '2hours') return context.drivingDuration >= 7200000;\n    return false;\n  }\n\n  // 电量触发条件评估\n  evaluateBatteryTrigger(value, context) {\n    if (!context.batteryLevel) return false;\n    if (value === '<30%') return context.batteryLevel < 30;\n    if (value === '<20%') return context.batteryLevel < 20;\n    if (value === '<10%') return context.batteryLevel < 10;\n    return false;\n  }\n\n  // 驾驶时间触发条件评估\n  evaluateDrivingTrigger(value, context) {\n    if (!context.drivingDuration) return false;\n    if (value === '2hours') return context.drivingDuration >= 7200000;\n    return false;\n  }\n\n  // 获取场景历史\n  getSceneHistory() {\n    return this.sceneHistory;\n  }\n\n  // 清除历史\n  clearHistory() {\n    this.sceneHistory = [];\n  }\n\n  // 获取场景统计\n  getSceneStatistics() {\n    const stats = {};\n    for (const history of this.sceneHistory) {\n      stats[history.to] = (stats[history.to] || 0) + 1;\n    }\n    return stats;\n  }\n}", "map": {"version": 3, "names": ["SceneManager", "constructor", "currentScene", "scenes", "initializeScenes", "sceneHistory", "maxHistorySize", "default", "id", "name", "description", "wallpaper", "layout", "cards", "theme", "priority", "autoSwitch", "morningCommuteFamily", "triggers", "morningCommuteFocus", "eveningCommute", "waitingMode", "rainyNight", "familyTrip", "longDistance", "<PERSON><PERSON><PERSON>", "petMode", "carWashMode", "<PERSON><PERSON><PERSON>", "chargingMode", "fatigueDetection", "userSwitch", "parkingMode", "emergencyMode", "getCurrentScene", "switchScene", "sceneId", "reason", "console", "warn", "unshift", "from", "to", "timestamp", "Date", "toISOString", "length", "slice", "log", "getAllScenes", "Object", "values", "getAutoSwitchScenes", "filter", "scene", "getRecommendedScenes", "context", "recommendations", "entries", "matchScore", "trigger", "evaluateTrigger", "push", "score", "sort", "a", "b", "type", "value", "split", "evaluateTimeTrigger", "evaluatePassengerTrigger", "evaluateDestinationTrigger", "gear", "evaluateDurationTrigger", "weather", "roadType", "evaluateBatteryTrigger", "locationType", "fatigueDetected", "evaluateDrivingTrigger", "manualTrigger", "calendarEvents", "includes", "newUserDetected", "accidentDetected", "airbagDeployed", "now", "hour", "getHours", "day", "getDay", "passengers", "passengerCount", "destination", "dest", "toLowerCase", "drivingDuration", "batteryLevel", "getSceneHistory", "clearHistory", "getSceneStatistics", "stats", "history"], "sources": ["F:/工作/theme/ai-hmi/src/utils/SceneManager.js"], "sourcesContent": ["// 场景配置管理器 - 管理所有AI-HMI场景的配置\r\nexport default class SceneManager {\r\n  constructor() {\r\n    this.currentScene = 'default'\r\n    this.scenes = this.initializeScenes()\r\n    this.sceneHistory = []\r\n    this.maxHistorySize = 10\r\n  }\r\n\r\n  initializeScenes() {\r\n    return {\r\n      // 默认场景\r\n      default: {\r\n        id: 'default',\r\n        name: '默认模式',\r\n        description: '沉浸式桌面壁纸生成界面',\r\n        wallpaper: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        layout: 'immersive',\r\n        cards: ['vpaWidget'],\r\n        theme: 'immersive',\r\n        priority: 0,\r\n        autoSwitch: false\r\n      },\r\n\r\n      // 场景一：早高峰通勤（家庭出行）\r\n      morningCommuteFamily: {\r\n        id: 'morningCommuteFamily',\r\n        name: '家庭出行模式',\r\n        description: '送孩子上学的家庭通勤',\r\n        wallpaper: 'modern glass building, transparent texture, blurred background, warm morning light, suitable for car display',\r\n        layout: 'family',\r\n        cards: ['navigation', 'kidEducation', 'pedia', 'vpaWidget'],\r\n        theme: 'warm',\r\n        priority: 8,\r\n        autoSwitch: true,\r\n        triggers: ['time:7-9', 'passengers:child', 'destination:school']\r\n      },\r\n\r\n      // 场景二：早高峰通勤（专注模式）\r\n      morningCommuteFocus: {\r\n        id: 'morningCommuteFocus',\r\n        name: '专注通勤模式',\r\n        description: '独自前往公司的专注通勤',\r\n        wallpaper: 'relaxing music dynamic wallpaper, glass texture, minimalist style',\r\n        layout: 'focus',\r\n        cards: ['navigation', 'music', 'todo', 'orderStatus', 'vpaWidget'],\r\n        theme: 'calm',\r\n        priority: 7,\r\n        autoSwitch: true,\r\n        triggers: ['time:7-9', 'passengers:single', 'destination:work']\r\n      },\r\n\r\n      // 场景三：下班通勤\r\n      eveningCommute: {\r\n        id: 'eveningCommute',\r\n        name: '下班通勤模式',\r\n        description: '工作日下班回家',\r\n        wallpaper: 'evening city street view, glass reflection, warm lighting',\r\n        layout: 'relax',\r\n        cards: ['navigation', 'music', 'smartHome', 'todo', 'vpaWidget'],\r\n        theme: 'evening',\r\n        priority: 6,\r\n        autoSwitch: true,\r\n        triggers: ['time:17-19', 'destination:home']\r\n      },\r\n\r\n      // 场景四：车内等待/摸鱼\r\n      waitingMode: {\r\n        id: 'waitingMode',\r\n        name: '等待休息模式',\r\n        description: '驻车等待时的娱乐模式',\r\n        wallpaper: 'peaceful lake view, glass texture, relaxing atmosphere',\r\n        layout: 'entertainment',\r\n        cards: ['videoPlayer', 'news', 'ambientSound', 'vpaWidget'],\r\n        theme: 'relax',\r\n        priority: 5,\r\n        autoSwitch: true,\r\n        triggers: ['gear:P', 'duration:1min']\r\n      },\r\n\r\n      // 场景五：雨夜归途\r\n      rainyNight: {\r\n        id: 'rainyNight',\r\n        name: '雨夜模式',\r\n        description: '雨夜驾驶的安全模式',\r\n        wallpaper: 'blurry city night view with raindrops, glass texture, warm lighting',\r\n        layout: 'minimal',\r\n        cards: ['navigation', 'music', 'vpaWidget'],\r\n        theme: 'dark',\r\n        priority: 9,\r\n        autoSwitch: true,\r\n        triggers: ['weather:rain', 'time:night']\r\n      },\r\n\r\n      // 场景六：周末家庭出游\r\n      familyTrip: {\r\n        id: 'familyTrip',\r\n        name: '家庭出游模式',\r\n        description: '周末家庭旅行',\r\n        wallpaper: 'forest park, sunny day, glass texture, family atmosphere',\r\n        layout: 'family',\r\n        cards: ['navigation', 'rearSeatControl', 'facilityFinder', 'tripReminder', 'vpaWidget'],\r\n        theme: 'bright',\r\n        priority: 6,\r\n        autoSwitch: true,\r\n        triggers: ['time:weekend', 'destination:park', 'passengers:family']\r\n      },\r\n\r\n      // 场景七：长途高速驾驶\r\n      longDistance: {\r\n        id: 'longDistance',\r\n        name: '长途驾驶模式',\r\n        description: '高速公路长途驾驶',\r\n        wallpaper: 'highway landscape, glass texture, minimalist style',\r\n        layout: 'driving',\r\n        cards: ['navigation', 'serviceArea', 'driverStatus', 'vehicleStatus', 'vpaWidget'],\r\n        theme: 'functional',\r\n        priority: 8,\r\n        autoSwitch: true,\r\n        triggers: ['road:highway', 'duration:1hour']\r\n      },\r\n\r\n      // 场景八：访客/代驾模式\r\n      guestMode: {\r\n        id: 'guestMode',\r\n        name: '访客模式',\r\n        description: '保护隐私的访客模式',\r\n        wallpaper: 'neutral default wallpaper, simple glass texture',\r\n        layout: 'basic',\r\n        cards: ['tempNavigation', 'basicMusic', 'basicControl', 'vpaWidget'],\r\n        theme: 'neutral',\r\n        priority: 4,\r\n        autoSwitch: false,\r\n        triggers: ['manual:guest']\r\n      },\r\n\r\n      // 场景九：宠物模式\r\n      petMode: {\r\n        id: 'petMode',\r\n        name: '宠物模式',\r\n        description: '宠物留在车内的安全模式',\r\n        wallpaper: 'cute pet animation background, comfortable temperature display',\r\n        layout: 'pet',\r\n        cards: ['petInfo', 'climateControl', 'vpaWidget'],\r\n        theme: 'cute',\r\n        priority: 7,\r\n        autoSwitch: false,\r\n        triggers: ['manual:pet']\r\n      },\r\n\r\n      // 场景十：洗车模式\r\n      carWashMode: {\r\n        id: 'carWashMode',\r\n        name: '洗车模式',\r\n        description: '自动洗车准备模式',\r\n        wallpaper: 'car wash station background, glass texture',\r\n        layout: 'checklist',\r\n        cards: ['carWashChecklist', 'vpaWidget'],\r\n        theme: 'functional',\r\n        priority: 3,\r\n        autoSwitch: false,\r\n        triggers: ['manual:carWash']\r\n      },\r\n\r\n      // 场景十一：浪漫二人世界\r\n      romanticMode: {\r\n        id: 'romanticMode',\r\n        name: '浪漫模式',\r\n        description: '私密浪漫氛围',\r\n        wallpaper: 'dynamic starry sky or fireplace video, glass texture',\r\n        layout: 'romantic',\r\n        cards: ['romanticMusic', 'ambientLight', 'vpaWidget'],\r\n        theme: 'romantic',\r\n        priority: 5,\r\n        autoSwitch: false,\r\n        triggers: ['manual:romantic', 'calendar:anniversary']\r\n      },\r\n\r\n      // 场景十二：智能充电场景\r\n      chargingMode: {\r\n        id: 'chargingMode',\r\n        name: '充电模式',\r\n        description: '电动车充电管理',\r\n        wallpaper: 'charging station environment, glass texture',\r\n        layout: 'charging',\r\n        cards: ['chargingStatus', 'entertainment', 'nearbyShops', 'vpaWidget'],\r\n        theme: 'tech',\r\n        priority: 8,\r\n        autoSwitch: true,\r\n        triggers: ['battery:<30%', 'location:chargingStation']\r\n      },\r\n\r\n      // 场景十三：疲劳驾驶检测与干预\r\n      fatigueDetection: {\r\n        id: 'fatigueDetection',\r\n        name: '疲劳驾驶预警',\r\n        description: '疲劳驾驶安全干预',\r\n        wallpaper: 'warning color gradient background, glass texture',\r\n        layout: 'warning',\r\n        cards: ['fatigueWarning', 'restArea', 'refreshment', 'emergencyContact', 'vpaWidget'],\r\n        theme: 'warning',\r\n        priority: 10,\r\n        autoSwitch: true,\r\n        triggers: ['fatigue:detected', 'driving:2hours']\r\n      },\r\n\r\n      // 场景十四：多用户识别与切换\r\n      userSwitch: {\r\n        id: 'userSwitch',\r\n        name: '用户切换模式',\r\n        description: '多用户配置选择',\r\n        wallpaper: 'neutral user selection background, glass texture',\r\n        layout: 'userSelection',\r\n        cards: ['userSelector', 'userPreferences', 'privacySettings', 'vpaWidget'],\r\n        theme: 'neutral',\r\n        priority: 6,\r\n        autoSwitch: true,\r\n        triggers: ['newUser:detected', 'manual:userSwitch']\r\n      },\r\n\r\n      // 场景十五：智能泊车辅助\r\n      parkingMode: {\r\n        id: 'parkingMode',\r\n        name: '智能泊车模式',\r\n        description: '全方位泊车辅助',\r\n        wallpaper: 'parking lot environment, glass texture',\r\n        layout: 'parking',\r\n        cards: ['parkingSearch', 'parkingAssist', 'costInfo', 'vpaWidget'],\r\n        theme: 'functional',\r\n        priority: 7,\r\n        autoSwitch: true,\r\n        triggers: ['destination:near', 'speed:slow', 'manual:parking']\r\n      },\r\n\r\n      // 场景十六：紧急情况处理\r\n      emergencyMode: {\r\n        id: 'emergencyMode',\r\n        name: '紧急情况模式',\r\n        description: '紧急救援处理',\r\n        wallpaper: 'emergency red background, flashing warning',\r\n        layout: 'emergency',\r\n        cards: ['emergencyInfo', 'firstAid', 'emergencyContact', 'vpaWidget'],\r\n        theme: 'emergency',\r\n        priority: 11,\r\n        autoSwitch: true,\r\n        triggers: ['accident:detected', 'airbag:deployed', 'manual:emergency']\r\n      }\r\n    }\r\n  }\r\n\r\n  // 获取当前场景\r\n  getCurrentScene() {\r\n    return this.scenes[this.currentScene] || this.scenes.default\r\n  }\r\n\r\n  // 切换场景\r\n  switchScene(sceneId, reason = 'manual') {\r\n    if (!this.scenes[sceneId]) {\r\n      console.warn(`Scene ${sceneId} not found`)\r\n      return false\r\n    }\r\n\r\n    // 记录历史\r\n    this.sceneHistory.unshift({\r\n      from: this.currentScene,\r\n      to: sceneId,\r\n      reason,\r\n      timestamp: new Date().toISOString()\r\n    })\r\n\r\n    // 限制历史记录大小\r\n    if (this.sceneHistory.length > this.maxHistorySize) {\r\n      this.sceneHistory = this.sceneHistory.slice(0, this.maxHistorySize)\r\n    }\r\n\r\n    this.currentScene = sceneId\r\n    console.log(`Scene switched to ${sceneId} (${reason})`)\r\n    return true\r\n  }\r\n\r\n  // 获取所有场景\r\n  getAllScenes() {\r\n    return Object.values(this.scenes)\r\n  }\r\n\r\n  // 获取可自动切换的场景\r\n  getAutoSwitchScenes() {\r\n    return Object.values(this.scenes).filter(scene => scene.autoSwitch)\r\n  }\r\n\r\n  // 根据触发条件获取推荐场景\r\n  getRecommendedScenes(context = {}) {\r\n    const recommendations = []\r\n    \r\n    for (const [sceneId, scene] of Object.entries(this.scenes)) {\r\n      if (!scene.autoSwitch) continue\r\n      \r\n      let matchScore = 0\r\n      for (const trigger of scene.triggers || []) {\r\n        if (this.evaluateTrigger(trigger, context)) {\r\n          matchScore += scene.priority\r\n        }\r\n      }\r\n      \r\n      if (matchScore > 0) {\r\n        recommendations.push({\r\n          sceneId,\r\n          scene,\r\n          score: matchScore\r\n        })\r\n      }\r\n    }\r\n    \r\n    return recommendations.sort((a, b) => b.score - a.score)\r\n  }\r\n\r\n  // 评估触发条件\r\n  evaluateTrigger(trigger, context) {\r\n    const [type, value] = trigger.split(':')\r\n    \r\n    switch (type) {\r\n      case 'time':\r\n        return this.evaluateTimeTrigger(value)\r\n      case 'passengers':\r\n        return this.evaluatePassengerTrigger(value, context)\r\n      case 'destination':\r\n        return this.evaluateDestinationTrigger(value, context)\r\n      case 'gear':\r\n        return context.gear === value\r\n      case 'duration':\r\n        return this.evaluateDurationTrigger(value, context)\r\n      case 'weather':\r\n        return context.weather === value\r\n      case 'road':\r\n        return context.roadType === value\r\n      case 'battery':\r\n        return this.evaluateBatteryTrigger(value, context)\r\n      case 'location':\r\n        return context.locationType === value\r\n      case 'fatigue':\r\n        return context.fatigueDetected\r\n      case 'driving':\r\n        return this.evaluateDrivingTrigger(value, context)\r\n      case 'manual':\r\n        return context.manualTrigger === value\r\n      case 'calendar':\r\n        return context.calendarEvents?.includes(value)\r\n      case 'newUser':\r\n        return context.newUserDetected\r\n      case 'accident':\r\n        return context.accidentDetected\r\n      case 'airbag':\r\n        return context.airbagDeployed\r\n      default:\r\n        return false\r\n    }\r\n  }\r\n\r\n  // 时间触发条件评估\r\n  evaluateTimeTrigger(value) {\r\n    // eslint-disable-next-line no-unused-vars\r\n    const now = new Date()\r\n    // eslint-disable-next-line no-unused-vars\r\n    const hour = now.getHours()\r\n    // eslint-disable-next-line no-unused-vars\r\n    const day = now.getDay()\r\n    \r\n    if (value === '7-9') return hour >= 7 && hour <= 9\r\n    if (value === '17-19') return hour >= 17 && hour <= 19\r\n    if (value === 'night') return hour >= 20 || hour <= 6\r\n    if (value === 'weekend') return day === 0 || day === 6\r\n    \r\n    return false\r\n  }\r\n\r\n  // 乘客触发条件评估\r\n  evaluatePassengerTrigger(value, context) {\r\n    if (value === 'child') return context.passengers?.includes('child')\r\n    if (value === 'single') return context.passengerCount === 1\r\n    if (value === 'family') return context.passengerCount > 2\r\n    \r\n    return false\r\n  }\r\n\r\n  // 目的地触发条件评估\r\n  evaluateDestinationTrigger(value, context) {\r\n    if (!context.destination) return false\r\n    \r\n    const dest = context.destination.toLowerCase()\r\n    if (value === 'school') return dest.includes('school') || dest.includes('学校')\r\n    if (value === 'work') return dest.includes('work') || dest.includes('公司')\r\n    if (value === 'home') return dest.includes('home') || dest.includes('家')\r\n    if (value === 'park') return dest.includes('park') || dest.includes('公园')\r\n    \r\n    return false\r\n  }\r\n\r\n  // 持续时间触发条件评估\r\n  evaluateDurationTrigger(value, context) {\r\n    if (!context.drivingDuration) return false\r\n    \r\n    if (value === '1min') return context.drivingDuration >= 60000\r\n    if (value === '1hour') return context.drivingDuration >= 3600000\r\n    if (value === '2hours') return context.drivingDuration >= 7200000\r\n    \r\n    return false\r\n  }\r\n\r\n  // 电量触发条件评估\r\n  evaluateBatteryTrigger(value, context) {\r\n    if (!context.batteryLevel) return false\r\n    \r\n    if (value === '<30%') return context.batteryLevel < 30\r\n    if (value === '<20%') return context.batteryLevel < 20\r\n    if (value === '<10%') return context.batteryLevel < 10\r\n    \r\n    return false\r\n  }\r\n\r\n  // 驾驶时间触发条件评估\r\n  evaluateDrivingTrigger(value, context) {\r\n    if (!context.drivingDuration) return false\r\n    \r\n    if (value === '2hours') return context.drivingDuration >= 7200000\r\n    \r\n    return false\r\n  }\r\n\r\n  // 获取场景历史\r\n  getSceneHistory() {\r\n    return this.sceneHistory\r\n  }\r\n\r\n  // 清除历史\r\n  clearHistory() {\r\n    this.sceneHistory = []\r\n  }\r\n\r\n  // 获取场景统计\r\n  getSceneStatistics() {\r\n    const stats = {}\r\n    for (const history of this.sceneHistory) {\r\n      stats[history.to] = (stats[history.to] || 0) + 1\r\n    }\r\n    return stats\r\n  }\r\n}"], "mappings": ";;;AAAA;AACA,eAAe,MAAMA,YAAY,CAAC;EAChCC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,YAAY,GAAG,SAAS;IAC7B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACrC,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEAF,gBAAgBA,CAAA,EAAG;IACjB,OAAO;MACL;MACAG,OAAO,EAAE;QACPC,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,mDAAmD;QAC9DC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,CAAC,WAAW,CAAC;QACpBC,KAAK,EAAE,WAAW;QAClBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE;MACd,CAAC;MAED;MACAC,oBAAoB,EAAE;QACpBT,EAAE,EAAE,sBAAsB;QAC1BC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,8GAA8G;QACzHC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,CAAC;QAC3DC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,UAAU,EAAE,kBAAkB,EAAE,oBAAoB;MACjE,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBX,EAAE,EAAE,qBAAqB;QACzBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,mEAAmE;QAC9EC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;QAClEC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,UAAU,EAAE,mBAAmB,EAAE,kBAAkB;MAChE,CAAC;MAED;MACAE,cAAc,EAAE;QACdZ,EAAE,EAAE,gBAAgB;QACpBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,2DAA2D;QACtEC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,CAAC;QAChEC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,YAAY,EAAE,kBAAkB;MAC7C,CAAC;MAED;MACAG,WAAW,EAAE;QACXb,EAAE,EAAE,aAAa;QACjBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,wDAAwD;QACnEC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,CAAC;QAC3DC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,QAAQ,EAAE,eAAe;MACtC,CAAC;MAED;MACAI,UAAU,EAAE;QACVd,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,SAAS,EAAE,qEAAqE;QAChFC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC;QAC3CC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,cAAc,EAAE,YAAY;MACzC,CAAC;MAED;MACAK,UAAU,EAAE;QACVf,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,QAAQ;QACrBC,SAAS,EAAE,0DAA0D;QACrEC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,EAAE,WAAW,CAAC;QACvFC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,mBAAmB;MACpE,CAAC;MAED;MACAM,YAAY,EAAE;QACZhB,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,UAAU;QACvBC,SAAS,EAAE,oDAAoD;QAC/DC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,CAAC;QAClFC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,cAAc,EAAE,gBAAgB;MAC7C,CAAC;MAED;MACAO,SAAS,EAAE;QACTjB,EAAE,EAAE,WAAW;QACfC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,SAAS,EAAE,iDAAiD;QAC5DC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,CAAC,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;QACpEC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBE,QAAQ,EAAE,CAAC,cAAc;MAC3B,CAAC;MAED;MACAQ,OAAO,EAAE;QACPlB,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,gEAAgE;QAC3EC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;QACjDC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBE,QAAQ,EAAE,CAAC,YAAY;MACzB,CAAC;MAED;MACAS,WAAW,EAAE;QACXnB,EAAE,EAAE,aAAa;QACjBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,UAAU;QACvBC,SAAS,EAAE,4CAA4C;QACvDC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,CAAC,kBAAkB,EAAE,WAAW,CAAC;QACxCC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBE,QAAQ,EAAE,CAAC,gBAAgB;MAC7B,CAAC;MAED;MACAU,YAAY,EAAE;QACZpB,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,QAAQ;QACrBC,SAAS,EAAE,sDAAsD;QACjEC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,WAAW,CAAC;QACrDC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBE,QAAQ,EAAE,CAAC,iBAAiB,EAAE,sBAAsB;MACtD,CAAC;MAED;MACAW,YAAY,EAAE;QACZrB,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,6CAA6C;QACxDC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,CAAC;QACtEC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,cAAc,EAAE,0BAA0B;MACvD,CAAC;MAED;MACAY,gBAAgB,EAAE;QAChBtB,EAAE,EAAE,kBAAkB;QACtBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,UAAU;QACvBC,SAAS,EAAE,kDAAkD;QAC7DC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,CAAC,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACrFC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB;MACjD,CAAC;MAED;MACAa,UAAU,EAAE;QACVvB,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,kDAAkD;QAC7DC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,WAAW,CAAC;QAC1EC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,kBAAkB,EAAE,mBAAmB;MACpD,CAAC;MAED;MACAc,WAAW,EAAE;QACXxB,EAAE,EAAE,aAAa;QACjBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,wCAAwC;QACnDC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC;QAClEC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,gBAAgB;MAC/D,CAAC;MAED;MACAe,aAAa,EAAE;QACbzB,EAAE,EAAE,eAAe;QACnBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,QAAQ;QACrBC,SAAS,EAAE,4CAA4C;QACvDC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACrEC,KAAK,EAAE,WAAW;QAClBC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,IAAI;QAChBE,QAAQ,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,kBAAkB;MACvE;IACF,CAAC;EACH;;EAEA;EACAgB,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/B,MAAM,CAAC,IAAI,CAACD,YAAY,CAAC,IAAI,IAAI,CAACC,MAAM,CAACI,OAAO;EAC9D;;EAEA;EACA4B,WAAWA,CAACC,OAAO,EAAEC,MAAM,GAAG,QAAQ,EAAE;IACtC,IAAI,CAAC,IAAI,CAAClC,MAAM,CAACiC,OAAO,CAAC,EAAE;MACzBE,OAAO,CAACC,IAAI,CAAC,SAASH,OAAO,YAAY,CAAC;MAC1C,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC/B,YAAY,CAACmC,OAAO,CAAC;MACxBC,IAAI,EAAE,IAAI,CAACvC,YAAY;MACvBwC,EAAE,EAAEN,OAAO;MACXC,MAAM;MACNM,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;;IAEF;IACA,IAAI,IAAI,CAACxC,YAAY,CAACyC,MAAM,GAAG,IAAI,CAACxC,cAAc,EAAE;MAClD,IAAI,CAACD,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC0C,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzC,cAAc,CAAC;IACrE;IAEA,IAAI,CAACJ,YAAY,GAAGkC,OAAO;IAC3BE,OAAO,CAACU,GAAG,CAAC,qBAAqBZ,OAAO,KAAKC,MAAM,GAAG,CAAC;IACvD,OAAO,IAAI;EACb;;EAEA;EACAY,YAAYA,CAAA,EAAG;IACb,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChD,MAAM,CAAC;EACnC;;EAEA;EACAiD,mBAAmBA,CAAA,EAAG;IACpB,OAAOF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChD,MAAM,CAAC,CAACkD,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtC,UAAU,CAAC;EACrE;;EAEA;EACAuC,oBAAoBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,MAAMC,eAAe,GAAG,EAAE;IAE1B,KAAK,MAAM,CAACrB,OAAO,EAAEkB,KAAK,CAAC,IAAIJ,MAAM,CAACQ,OAAO,CAAC,IAAI,CAACvD,MAAM,CAAC,EAAE;MAC1D,IAAI,CAACmD,KAAK,CAACtC,UAAU,EAAE;MAEvB,IAAI2C,UAAU,GAAG,CAAC;MAClB,KAAK,MAAMC,OAAO,IAAIN,KAAK,CAACpC,QAAQ,IAAI,EAAE,EAAE;QAC1C,IAAI,IAAI,CAAC2C,eAAe,CAACD,OAAO,EAAEJ,OAAO,CAAC,EAAE;UAC1CG,UAAU,IAAIL,KAAK,CAACvC,QAAQ;QAC9B;MACF;MAEA,IAAI4C,UAAU,GAAG,CAAC,EAAE;QAClBF,eAAe,CAACK,IAAI,CAAC;UACnB1B,OAAO;UACPkB,KAAK;UACLS,KAAK,EAAEJ;QACT,CAAC,CAAC;MACJ;IACF;IAEA,OAAOF,eAAe,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACH,KAAK,GAAGE,CAAC,CAACF,KAAK,CAAC;EAC1D;;EAEA;EACAF,eAAeA,CAACD,OAAO,EAAEJ,OAAO,EAAE;IAChC,MAAM,CAACW,IAAI,EAAEC,KAAK,CAAC,GAAGR,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC;IAExC,QAAQF,IAAI;MACV,KAAK,MAAM;QACT,OAAO,IAAI,CAACG,mBAAmB,CAACF,KAAK,CAAC;MACxC,KAAK,YAAY;QACf,OAAO,IAAI,CAACG,wBAAwB,CAACH,KAAK,EAAEZ,OAAO,CAAC;MACtD,KAAK,aAAa;QAChB,OAAO,IAAI,CAACgB,0BAA0B,CAACJ,KAAK,EAAEZ,OAAO,CAAC;MACxD,KAAK,MAAM;QACT,OAAOA,OAAO,CAACiB,IAAI,KAAKL,KAAK;MAC/B,KAAK,UAAU;QACb,OAAO,IAAI,CAACM,uBAAuB,CAACN,KAAK,EAAEZ,OAAO,CAAC;MACrD,KAAK,SAAS;QACZ,OAAOA,OAAO,CAACmB,OAAO,KAAKP,KAAK;MAClC,KAAK,MAAM;QACT,OAAOZ,OAAO,CAACoB,QAAQ,KAAKR,KAAK;MACnC,KAAK,SAAS;QACZ,OAAO,IAAI,CAACS,sBAAsB,CAACT,KAAK,EAAEZ,OAAO,CAAC;MACpD,KAAK,UAAU;QACb,OAAOA,OAAO,CAACsB,YAAY,KAAKV,KAAK;MACvC,KAAK,SAAS;QACZ,OAAOZ,OAAO,CAACuB,eAAe;MAChC,KAAK,SAAS;QACZ,OAAO,IAAI,CAACC,sBAAsB,CAACZ,KAAK,EAAEZ,OAAO,CAAC;MACpD,KAAK,QAAQ;QACX,OAAOA,OAAO,CAACyB,aAAa,KAAKb,KAAK;MACxC,KAAK,UAAU;QACb,OAAOZ,OAAO,CAAC0B,cAAc,EAAEC,QAAQ,CAACf,KAAK,CAAC;MAChD,KAAK,SAAS;QACZ,OAAOZ,OAAO,CAAC4B,eAAe;MAChC,KAAK,UAAU;QACb,OAAO5B,OAAO,CAAC6B,gBAAgB;MACjC,KAAK,QAAQ;QACX,OAAO7B,OAAO,CAAC8B,cAAc;MAC/B;QACE,OAAO,KAAK;IAChB;EACF;;EAEA;EACAhB,mBAAmBA,CAACF,KAAK,EAAE;IACzB;IACA,MAAMmB,GAAG,GAAG,IAAI3C,IAAI,CAAC,CAAC;IACtB;IACA,MAAM4C,IAAI,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;IAC3B;IACA,MAAMC,GAAG,GAAGH,GAAG,CAACI,MAAM,CAAC,CAAC;IAExB,IAAIvB,KAAK,KAAK,KAAK,EAAE,OAAOoB,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC;IAClD,IAAIpB,KAAK,KAAK,OAAO,EAAE,OAAOoB,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE;IACtD,IAAIpB,KAAK,KAAK,OAAO,EAAE,OAAOoB,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,CAAC;IACrD,IAAIpB,KAAK,KAAK,SAAS,EAAE,OAAOsB,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;IAEtD,OAAO,KAAK;EACd;;EAEA;EACAnB,wBAAwBA,CAACH,KAAK,EAAEZ,OAAO,EAAE;IACvC,IAAIY,KAAK,KAAK,OAAO,EAAE,OAAOZ,OAAO,CAACoC,UAAU,EAAET,QAAQ,CAAC,OAAO,CAAC;IACnE,IAAIf,KAAK,KAAK,QAAQ,EAAE,OAAOZ,OAAO,CAACqC,cAAc,KAAK,CAAC;IAC3D,IAAIzB,KAAK,KAAK,QAAQ,EAAE,OAAOZ,OAAO,CAACqC,cAAc,GAAG,CAAC;IAEzD,OAAO,KAAK;EACd;;EAEA;EACArB,0BAA0BA,CAACJ,KAAK,EAAEZ,OAAO,EAAE;IACzC,IAAI,CAACA,OAAO,CAACsC,WAAW,EAAE,OAAO,KAAK;IAEtC,MAAMC,IAAI,GAAGvC,OAAO,CAACsC,WAAW,CAACE,WAAW,CAAC,CAAC;IAC9C,IAAI5B,KAAK,KAAK,QAAQ,EAAE,OAAO2B,IAAI,CAACZ,QAAQ,CAAC,QAAQ,CAAC,IAAIY,IAAI,CAACZ,QAAQ,CAAC,IAAI,CAAC;IAC7E,IAAIf,KAAK,KAAK,MAAM,EAAE,OAAO2B,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC,IAAIY,IAAI,CAACZ,QAAQ,CAAC,IAAI,CAAC;IACzE,IAAIf,KAAK,KAAK,MAAM,EAAE,OAAO2B,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC,IAAIY,IAAI,CAACZ,QAAQ,CAAC,GAAG,CAAC;IACxE,IAAIf,KAAK,KAAK,MAAM,EAAE,OAAO2B,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC,IAAIY,IAAI,CAACZ,QAAQ,CAAC,IAAI,CAAC;IAEzE,OAAO,KAAK;EACd;;EAEA;EACAT,uBAAuBA,CAACN,KAAK,EAAEZ,OAAO,EAAE;IACtC,IAAI,CAACA,OAAO,CAACyC,eAAe,EAAE,OAAO,KAAK;IAE1C,IAAI7B,KAAK,KAAK,MAAM,EAAE,OAAOZ,OAAO,CAACyC,eAAe,IAAI,KAAK;IAC7D,IAAI7B,KAAK,KAAK,OAAO,EAAE,OAAOZ,OAAO,CAACyC,eAAe,IAAI,OAAO;IAChE,IAAI7B,KAAK,KAAK,QAAQ,EAAE,OAAOZ,OAAO,CAACyC,eAAe,IAAI,OAAO;IAEjE,OAAO,KAAK;EACd;;EAEA;EACApB,sBAAsBA,CAACT,KAAK,EAAEZ,OAAO,EAAE;IACrC,IAAI,CAACA,OAAO,CAAC0C,YAAY,EAAE,OAAO,KAAK;IAEvC,IAAI9B,KAAK,KAAK,MAAM,EAAE,OAAOZ,OAAO,CAAC0C,YAAY,GAAG,EAAE;IACtD,IAAI9B,KAAK,KAAK,MAAM,EAAE,OAAOZ,OAAO,CAAC0C,YAAY,GAAG,EAAE;IACtD,IAAI9B,KAAK,KAAK,MAAM,EAAE,OAAOZ,OAAO,CAAC0C,YAAY,GAAG,EAAE;IAEtD,OAAO,KAAK;EACd;;EAEA;EACAlB,sBAAsBA,CAACZ,KAAK,EAAEZ,OAAO,EAAE;IACrC,IAAI,CAACA,OAAO,CAACyC,eAAe,EAAE,OAAO,KAAK;IAE1C,IAAI7B,KAAK,KAAK,QAAQ,EAAE,OAAOZ,OAAO,CAACyC,eAAe,IAAI,OAAO;IAEjE,OAAO,KAAK;EACd;;EAEA;EACAE,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC9F,YAAY;EAC1B;;EAEA;EACA+F,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC/F,YAAY,GAAG,EAAE;EACxB;;EAEA;EACAgG,kBAAkBA,CAAA,EAAG;IACnB,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChB,KAAK,MAAMC,OAAO,IAAI,IAAI,CAAClG,YAAY,EAAE;MACvCiG,KAAK,CAACC,OAAO,CAAC7D,EAAE,CAAC,GAAG,CAAC4D,KAAK,CAACC,OAAO,CAAC7D,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;IAClD;IACA,OAAO4D,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}