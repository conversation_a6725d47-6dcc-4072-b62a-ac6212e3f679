{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, renderSlot as _renderSlot, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"grid-lines-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"grid-system-16x9\", [`grid-mode-${$props.mode}`, {\n      'show-grid-lines': $props.showGridLines\n    }]]),\n    style: _normalizeStyle($setup.gridStyles)\n  }, [_createCommentVNode(\" 网格线显示 (调试用) - 重新实现 \"), $props.showGridLines ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 垂直网格线 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.gridColumns - 1, i => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: `v-line-${i}`,\n      class: \"grid-line grid-line-vertical\",\n      style: _normalizeStyle({\n        left: `${100 / $setup.gridColumns * i}%`,\n        top: '0',\n        height: '100%'\n      })\n    }, null, 4 /* STYLE */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 水平网格线 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.gridRows - 1, i => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: `h-line-${i}`,\n      class: \"grid-line grid-line-horizontal\",\n      style: _normalizeStyle({\n        top: `${100 / $setup.gridRows * i}%`,\n        left: '0',\n        width: '100%'\n      })\n    }, null, 4 /* STYLE */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 外边框 \"), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"grid-border\"\n  }, null, -1 /* CACHED */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 网格内容 \"), _renderSlot(_ctx.$slots, \"default\", {\n    grid: $setup.gridInfo\n  }, undefined, true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "mode", "showGridLines", "style", "_normalizeStyle", "$setup", "gridStyles", "_createCommentVNode", "_hoisted_1", "_Fragment", "_renderList", "gridColumns", "i", "key", "gridRows", "_createElementVNode", "_renderSlot", "_ctx", "$slots", "grid", "gridInfo", "undefined"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\layout\\GridSystem16x9.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"grid-system-16x9\"\n    :style=\"gridStyles\"\n    :class=\"[\n      `grid-mode-${mode}`,\n      { 'show-grid-lines': showGridLines }\n    ]\"\n  >\n    <!-- 网格线显示 (调试用) - 重新实现 -->\n    <div v-if=\"showGridLines\" class=\"grid-lines-overlay\">\n      <!-- 垂直网格线 -->\n      <div\n        v-for=\"i in (gridColumns - 1)\"\n        :key=\"`v-line-${i}`\"\n        class=\"grid-line grid-line-vertical\"\n        :style=\"{\n          left: `${(100 / gridColumns) * i}%`,\n          top: '0',\n          height: '100%'\n        }\"\n      ></div>\n      <!-- 水平网格线 -->\n      <div\n        v-for=\"i in (gridRows - 1)\"\n        :key=\"`h-line-${i}`\"\n        class=\"grid-line grid-line-horizontal\"\n        :style=\"{\n          top: `${(100 / gridRows) * i}%`,\n          left: '0',\n          width: '100%'\n        }\"\n      ></div>\n      <!-- 外边框 -->\n      <div class=\"grid-border\"></div>\n    </div>\n\n    <!-- 网格内容 -->\n    <slot :grid=\"gridInfo\"></slot>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store/modules/layout'\n\nexport default {\n  name: 'GridSystem16x9',\n  props: {\n    mode: {\n      type: String,\n      default: 'standard', // standard, compact, expanded\n      validator: value => ['standard', 'compact', 'expanded'].includes(value)\n    },\n    showGridLines: {\n      type: Boolean,\n      default: false\n    },\n    customGap: {\n      type: [Number, String],\n      default: null\n    }\n  },\n  setup(props) {\n    const layoutStore = useLayoutStore()\n\n    const gridColumns = computed(() => layoutStore.gridColumns)\n    const gridRows = computed(() => layoutStore.gridRows)\n    \n    const gridGap = computed(() => {\n      if (props.customGap) {\n        return typeof props.customGap === 'number' ? `${props.customGap}px` : props.customGap\n      }\n      return `${layoutStore.gridGap}px`\n    })\n\n    const gridStyles = computed(() => {\n      const baseStyles = {\n        '--grid-columns': gridColumns.value,\n        '--grid-rows': gridRows.value,\n        '--grid-gap': gridGap.value,\n        display: 'grid',\n        gridTemplateColumns: `repeat(${gridColumns.value}, 1fr)`,\n        gridTemplateRows: `repeat(${gridRows.value}, 1fr)`,\n        gap: gridGap.value,\n        width: '100%',\n        height: '100%'\n      }\n\n      // 根据模式调整样式\n      switch (props.mode) {\n        case 'compact':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 0.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 0.5)`\n          }\n        case 'expanded':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 1.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 1.5)`\n          }\n        default:\n          return baseStyles\n      }\n    })\n\n    const gridInfo = computed(() => ({\n      columns: gridColumns.value,\n      rows: gridRows.value,\n      gap: gridGap.value,\n      mode: props.mode,\n      cellSize: layoutStore.gridCellSize\n    }))\n\n    // 网格位置计算工具\n    const getGridPosition = (column, row, colspan = 1, rowspan = 1) => {\n      return {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`\n      }\n    }\n\n    // 检查位置是否被占用\n    const isPositionOccupied = (column, row, colspan = 1, rowspan = 1, excludeId = null) => {\n      const components = layoutStore.visibleComponents\n      \n      for (const component of components) {\n        if (excludeId && component.id === excludeId) continue\n        \n        const { column: cCol, row: cRow, colspan: cColspan, rowspan: cRowspan } = component.position\n        \n        // 检查是否有重叠\n        const horizontalOverlap = column < cCol + cColspan && column + colspan > cCol\n        const verticalOverlap = row < cRow + cRowspan && row + rowspan > cRow\n        \n        if (horizontalOverlap && verticalOverlap) {\n          return true\n        }\n      }\n      \n      return false\n    }\n\n    // 查找可用位置\n    const findAvailablePosition = (colspan = 1, rowspan = 1) => {\n      for (let row = 1; row <= gridRows.value - rowspan + 1; row++) {\n        for (let column = 1; column <= gridColumns.value - colspan + 1; column++) {\n          if (!isPositionOccupied(column, row, colspan, rowspan)) {\n            return { column, row }\n          }\n        }\n      }\n      return null\n    }\n\n    return {\n      gridColumns,\n      gridRows,\n      gridStyles,\n      gridInfo,\n      getGridPosition,\n      isPositionOccupied,\n      findAvailablePosition\n    }\n  }\n}\n</script>\n\n<style scoped>\n.grid-system-16x9 {\n  position: relative;\n  box-sizing: border-box;\n}\n\n/* 网格线覆盖层 - 全新实现 */\n.grid-lines-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 10000;\n}\n\n/* 网格线基础样式 - 超高可见性 */\n.grid-line {\n  position: absolute;\n  background-color: #FF0000;\n  box-shadow: 0 0 4px #FF0000, 0 0 8px rgba(255, 0, 0, 0.5);\n}\n\n/* 垂直网格线 */\n.grid-line-vertical {\n  width: 3px;\n  border-left: 2px solid #FF0000;\n}\n\n/* 水平网格线 */\n.grid-line-horizontal {\n  height: 3px;\n  border-top: 2px solid #FF0000;\n}\n\n/* 网格边框 */\n.grid-border {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: 4px solid #FF0000;\n  box-shadow:\n    inset 0 0 0 2px rgba(255, 0, 0, 0.5),\n    0 0 15px #FF0000,\n    0 0 30px rgba(255, 0, 0, 0.3);\n}\n\n/* 网格模式样式 */\n.grid-mode-standard {\n  /* 标准模式样式 */\n}\n\n.grid-mode-compact {\n  /* 紧凑模式样式 */\n}\n\n.grid-mode-expanded {\n  /* 扩展模式样式 */\n}\n\n/* 调试模式 */\n.show-grid-lines {\n  position: relative;\n}\n\n/* 响应式调整 */\n@media (max-width: 1024px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.75);\n  }\n}\n\n@media (max-width: 768px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.5);\n  }\n}\n</style>\n"], "mappings": ";;;EAU8BA,KAAK,EAAC;;;uBATlCC,mBAAA,CAsCM;IArCJD,KAAK,EAAAE,eAAA,EAAC,kBAAkB,G,aAEKC,MAAA,CAAAC,IAAI,I;yBAA+BD,MAAA,CAAAE;IAAa,E;IAD5EC,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,UAAU;MAMlBC,mBAAA,wBAA2B,EAChBP,MAAA,CAAAE,aAAa,I,cAAxBJ,mBAAA,CAyBM,OAzBNU,UAyBM,GAxBJD,mBAAA,WAAc,G,kBACdT,mBAAA,CASOW,SAAA,QAAAC,WAAA,CARQL,MAAA,CAAAM,WAAW,MAAjBC,CAAC;yBADVd,mBAAA,CASO;MAPJe,GAAG,YAAYD,CAAC;MACjBf,KAAK,EAAC,8BAA8B;MACnCM,KAAK,EAAAC,eAAA;uBAA8BC,MAAA,CAAAM,WAAW,GAAIC,CAAC;;;;;kCAMtDL,mBAAA,WAAc,G,kBACdT,mBAAA,CASOW,SAAA,QAAAC,WAAA,CARQL,MAAA,CAAAS,QAAQ,MAAdF,CAAC;yBADVd,mBAAA,CASO;MAPJe,GAAG,YAAYD,CAAC;MACjBf,KAAK,EAAC,gCAAgC;MACrCM,KAAK,EAAAC,eAAA;sBAA6BC,MAAA,CAAAS,QAAQ,GAAIF,CAAC;;;;;kCAMlDL,mBAAA,SAAY,E,0BACZQ,mBAAA,CAA+B;IAA1BlB,KAAK,EAAC;EAAa,2B,wCAG1BU,mBAAA,UAAa,EACbS,WAAA,CAA8BC,IAAA,CAAAC,MAAA;IAAvBC,IAAI,EAAEd,MAAA,CAAAe;EAAQ,GAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}