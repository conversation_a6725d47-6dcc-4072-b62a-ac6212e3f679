<template>
  <BaseCard
    card-type="music"
    size="large"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :title="'音乐控制'"
    :icon="'fas fa-music'"
    :clickable="true"
    :show-header="false"
    @click="handleCardClick"
    class="music-control-card"
  >
    <div class="music-content">
      <!-- 专辑封面和歌曲信息 -->
      <div class="music-info">
        <div class="album-cover">
          <img 
            v-if="currentSong.cover"
            :src="currentSong.cover"
            :alt="currentSong.title"
            class="cover-image"
          />
          <div v-else class="cover-placeholder">
            <i class="fas fa-music"></i>
          </div>
          
          <!-- 播放状态覆盖层 -->
          <div class="play-overlay" :class="{ active: isPlaying }">
            <div class="sound-waves">
              <div class="wave" v-for="i in 4" :key="i" :style="{ animationDelay: `${i * 0.1}s` }"></div>
            </div>
          </div>
        </div>
        
        <div class="song-details">
          <h3 class="song-title">{{ currentSong.title }}</h3>
          <p class="song-artist">{{ currentSong.artist }}</p>
          <p class="song-album">{{ currentSong.album }}</p>
        </div>
      </div>

      <!-- 播放进度 -->
      <div class="progress-section">
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="total-time">{{ formatTime(currentSong.duration) }}</span>
        </div>
        <div class="progress-bar" @click="seekTo">
          <div class="progress-track"></div>
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
          <div 
            class="progress-thumb" 
            :style="{ left: `${progressPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- 播放控制 -->
      <div class="control-section">
        <div class="main-controls">
          <button @click="previousSong" class="control-btn">
            <i class="fas fa-step-backward"></i>
          </button>
          
          <button @click="togglePlay" class="control-btn play-btn">
            <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
          </button>
          
          <button @click="nextSong" class="control-btn">
            <i class="fas fa-step-forward"></i>
          </button>
        </div>
        
        <div class="secondary-controls">
          <button @click="toggleShuffle" :class="['control-btn', { active: isShuffled }]">
            <i class="fas fa-random"></i>
          </button>
          
          <button @click="toggleRepeat" :class="['control-btn', { active: isRepeating }]">
            <i class="fas fa-redo"></i>
          </button>
          
          <div class="volume-control">
            <button @click="toggleMute" class="control-btn volume-btn">
              <i :class="volumeIcon"></i>
            </button>
            <div class="volume-slider">
              <input 
                type="range" 
                min="0" 
                max="100" 
                v-model="volume"
                class="volume-input"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 播放列表预览 -->
      <div class="playlist-preview">
        <div class="playlist-header">
          <span>播放列表</span>
          <button @click="openFullPlaylist" class="playlist-btn">
            <i class="fas fa-list"></i>
          </button>
        </div>
        <div class="playlist-items">
          <div 
            v-for="(song, index) in playlist.slice(0, 3)" 
            :key="index"
            :class="['playlist-item', { active: currentSongIndex === index }]"
            @click="playSong(index)"
          >
            <div class="item-info">
              <span class="item-title">{{ song.title }}</span>
              <span class="item-artist">{{ song.artist }}</span>
            </div>
            <div class="item-duration">{{ formatTime(song.duration) }}</div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '../BaseCard.vue'

export default {
  name: 'MusicControlCard',
  components: {
    BaseCard
  },
  props: {
    position: {
      type: Object,
      default: () => ({ x: 1, y: 2 })
    },
    theme: {
      type: String,
      default: 'glass'
    },
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#9b59b6',
        secondary: '#e74c3c',
        background: 'rgba(155, 89, 182, 0.1)',
        text: '#ffffff'
      })
    }
  },
  
  emits: ['card-click', 'song-changed', 'play-state-changed'],
  
  setup(props, { emit }) {
    // 响应式状态
    const isPlaying = ref(false)
    const isShuffled = ref(false)
    const isRepeating = ref(false)
    const isMuted = ref(false)
    const volume = ref(75)
    const currentTime = ref(0)
    const currentSongIndex = ref(0)
    
    // 播放列表数据
    const playlist = ref([
      {
        title: '夜空中最亮的星',
        artist: '逃跑计划',
        album: '世界',
        duration: 245,
        cover: null
      },
      {
        title: '成都',
        artist: '赵雷',
        album: '无法长大',
        duration: 327,
        cover: null
      },
      {
        title: '南山南',
        artist: '马頔',
        album: '孤岛',
        duration: 290,
        cover: null
      },
      {
        title: '理想',
        artist: '赵雷',
        album: '赵小雷',
        duration: 268,
        cover: null
      }
    ])
    
    // 计算属性
    const currentSong = computed(() => playlist.value[currentSongIndex.value])
    
    const progressPercentage = computed(() => {
      if (currentSong.value.duration === 0) return 0
      return (currentTime.value / currentSong.value.duration) * 100
    })
    
    const volumeIcon = computed(() => {
      if (isMuted.value || volume.value === 0) {
        return 'fas fa-volume-mute'
      } else if (volume.value < 50) {
        return 'fas fa-volume-down'
      } else {
        return 'fas fa-volume-up'
      }
    })
    
    // 播放控制
    let playTimer = null
    
    const togglePlay = () => {
      isPlaying.value = !isPlaying.value
      
      if (isPlaying.value) {
        startPlayTimer()
      } else {
        stopPlayTimer()
      }
      
      emit('play-state-changed', {
        isPlaying: isPlaying.value,
        song: currentSong.value
      })
    }
    
    const startPlayTimer = () => {
      playTimer = setInterval(() => {
        currentTime.value += 1
        
        // 歌曲播放完毕
        if (currentTime.value >= currentSong.value.duration) {
          if (isRepeating.value) {
            currentTime.value = 0
          } else {
            nextSong()
          }
        }
      }, 1000)
    }
    
    const stopPlayTimer = () => {
      if (playTimer) {
        clearInterval(playTimer)
        playTimer = null
      }
    }
    
    const previousSong = () => {
      if (isShuffled.value) {
        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)
      } else {
        currentSongIndex.value = currentSongIndex.value > 0 
          ? currentSongIndex.value - 1 
          : playlist.value.length - 1
      }
      currentTime.value = 0
      emit('song-changed', currentSong.value)
    }
    
    const nextSong = () => {
      if (isShuffled.value) {
        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)
      } else {
        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 
          ? currentSongIndex.value + 1 
          : 0
      }
      currentTime.value = 0
      emit('song-changed', currentSong.value)
    }
    
    const playSong = (index) => {
      currentSongIndex.value = index
      currentTime.value = 0
      if (!isPlaying.value) {
        togglePlay()
      }
      emit('song-changed', currentSong.value)
    }
    
    const seekTo = (event) => {
      const progressBar = event.currentTarget
      const rect = progressBar.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const percentage = clickX / rect.width
      currentTime.value = Math.floor(percentage * currentSong.value.duration)
    }
    
    const toggleShuffle = () => {
      isShuffled.value = !isShuffled.value
    }
    
    const toggleRepeat = () => {
      isRepeating.value = !isRepeating.value
    }
    
    const toggleMute = () => {
      isMuted.value = !isMuted.value
    }
    
    const openFullPlaylist = () => {
      console.log('打开完整播放列表')
    }
    
    const handleCardClick = () => {
      emit('card-click', 'music')
    }
    
    // 工具函数
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }
    
    // 生命周期
    onMounted(() => {
      console.log('音乐控制卡片已加载')
    })
    
    onUnmounted(() => {
      stopPlayTimer()
    })
    
    return {
      isPlaying,
      isShuffled,
      isRepeating,
      isMuted,
      volume,
      currentTime,
      currentSongIndex,
      playlist,
      currentSong,
      progressPercentage,
      volumeIcon,
      togglePlay,
      previousSong,
      nextSong,
      playSong,
      seekTo,
      toggleShuffle,
      toggleRepeat,
      toggleMute,
      openFullPlaylist,
      handleCardClick,
      formatTime
    }
  }
}
</script>

<style scoped>
.music-control-card {
  background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);
}

.music-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
}

/* 音乐信息区域 */
.music-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.album-cover {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--card-primary-color, #9b59b6);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-overlay.active {
  opacity: 1;
}

.sound-waves {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.wave {
  width: 3px;
  height: 10px;
  background: white;
  border-radius: 2px;
  animation: wave 1s infinite ease-in-out;
}

.song-details {
  flex: 1;
  min-width: 0;
}

.song-title {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--card-text-color, #ffffff);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.song-artist, .song-album {
  margin: 0;
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 进度区域 */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
}

.progress-bar {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  cursor: pointer;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: var(--card-primary-color, #9b59b6);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: -4px;
  width: 12px;
  height: 12px;
  background: var(--card-primary-color, #9b59b6);
  border-radius: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-bar:hover .progress-thumb {
  opacity: 1;
}

/* 控制区域 */
.control-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--card-text-color, #ffffff);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.active {
  background: var(--card-primary-color, #9b59b6);
}

.play-btn {
  width: 50px;
  height: 50px;
  background: var(--card-primary-color, #9b59b6);
  font-size: 18px;
}

.play-btn:hover {
  background: var(--card-secondary-color, #e74c3c);
}

.secondary-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-btn {
  width: 32px;
  height: 32px;
}

.volume-slider {
  width: 60px;
}

.volume-input {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.volume-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: var(--card-primary-color, #9b59b6);
  border-radius: 50%;
  cursor: pointer;
}

/* 播放列表预览 */
.playlist-preview {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 10px;
  min-height: 0;
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
}

.playlist-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--card-text-color, #ffffff);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.playlist-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.playlist-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.playlist-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.playlist-item.active {
  background: var(--card-primary-color, #9b59b6);
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-title {
  display: block;
  font-size: 11px;
  color: var(--card-text-color, #ffffff);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-artist {
  display: block;
  font-size: 10px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-duration {
  font-size: 10px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.7;
}

/* 动画 */
@keyframes wave {
  0%, 100% { height: 10px; }
  50% { height: 20px; }
}
</style>
