{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport ImageGenerationService from '@/services/ImageGenerationService';\nimport LlmService from '@/services/LlmService';\nimport AsrService from '@/services/AsrService';\n// import ColorExtractor from '@/utils/ColorExtractor' // 已被AIColorAnalyzer替代\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer';\nexport default {\n  name: 'TestSceneGeneration',\n  emits: ['colors-extracted'],\n  setup(props, {\n    emit\n  }) {\n    const testInput = ref('温暖的日落场景，现代玻璃建筑');\n    const isGenerating = ref(false);\n    const isListening = ref(false);\n    const voiceResult = ref('');\n    const currentResult = ref(null);\n    const testLogs = ref([]);\n    const testScenes = ref([{\n      id: 'morning',\n      name: '早晨通勤',\n      icon: 'fas fa-sun',\n      prompt: '清晨阳光，现代玻璃建筑，商务氛围'\n    }, {\n      id: 'evening',\n      name: '晚间放松',\n      icon: 'fas fa-moon',\n      prompt: '温暖夜景，柔和灯光，舒适氛围'\n    }, {\n      id: 'rainy',\n      name: '雨夜模式',\n      icon: 'fas fa-cloud-rain',\n      prompt: '雨夜街景，霓虹灯光，玻璃反射'\n    }, {\n      id: 'family',\n      name: '家庭出行',\n      icon: 'fas fa-car',\n      prompt: '温馨家庭，阳光明媚，愉快旅程'\n    }]);\n    const addLog = (message, type = 'info') => {\n      testLogs.value.unshift({\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      });\n      if (testLogs.value.length > 50) {\n        testLogs.value.pop();\n      }\n    };\n    const testTextToImage = async () => {\n      if (!testInput.value.trim()) {\n        addLog('请输入测试文本', 'error');\n        return;\n      }\n      isGenerating.value = true;\n      addLog(`开始测试: ${testInput.value}`, 'info');\n      try {\n        currentResult.value = {\n          input: testInput.value,\n          optimizedPrompt: '',\n          status: 'processing',\n          statusText: '处理中...',\n          imageUrl: null,\n          colors: null\n        };\n\n        // 1. LLM优化提示词\n        addLog('步骤1: LLM优化提示词...', 'info');\n        const llmService = new LlmService();\n        const optimizedPrompt = await llmService.generateResponse(testInput.value);\n        currentResult.value.optimizedPrompt = optimizedPrompt;\n        addLog(`LLM优化完成: ${optimizedPrompt}`, 'success');\n\n        // 2. 调用Kolors生图\n        addLog('步骤2: 调用Kolors生图API...', 'info');\n        const imageService = new ImageGenerationService();\n        const imageResult = await imageService.generateWallpaper(optimizedPrompt);\n        currentResult.value.imageUrl = imageResult.imageUrl;\n        addLog(`图片生成完成: ${imageResult.imageUrl}`, 'success');\n\n        // 3. AI智能配色分析\n        addLog('步骤3: AI智能配色分析...', 'info');\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(imageResult.imageUrl, testInput.value, optimizedPrompt);\n        currentResult.value.colors = intelligentColors;\n        addLog('AI智能配色完成', 'success');\n\n        // 显示AI分析结果\n        if (intelligentColors.aiAnalysis) {\n          addLog(`AI分析: ${intelligentColors.aiAnalysis.mood}氛围, ${intelligentColors.aiAnalysis.brightness}亮度`, 'info');\n        }\n\n        // 发送颜色到父组件\n        emit('colors-extracted', intelligentColors);\n        currentResult.value.status = 'success';\n        currentResult.value.statusText = '✅ 生成成功';\n        addLog('🎉 完整流程测试成功！', 'success');\n      } catch (error) {\n        console.error('测试失败:', error);\n        currentResult.value.status = 'error';\n        currentResult.value.statusText = '❌ 生成失败';\n        addLog(`测试失败: ${error.message}`, 'error');\n      } finally {\n        isGenerating.value = false;\n      }\n    };\n    const testVoiceInput = async () => {\n      if (!AsrService.isSupported()) {\n        addLog('浏览器不支持语音识别', 'error');\n        return;\n      }\n      isListening.value = true;\n      addLog('开始语音识别...', 'info');\n      try {\n        const result = await AsrService.startListening();\n        voiceResult.value = result;\n        testInput.value = result;\n        addLog(`语音识别结果: ${result}`, 'success');\n\n        // 自动进行文生图测试\n        await testTextToImage();\n      } catch (error) {\n        addLog(`语音识别失败: ${error.message}`, 'error');\n      } finally {\n        isListening.value = false;\n      }\n    };\n    const testScene = async scene => {\n      testInput.value = scene.prompt;\n      addLog(`测试场景: ${scene.name}`, 'info');\n      await testTextToImage();\n    };\n    onMounted(() => {\n      addLog('AI-HMI 场景生成测试组件已加载', 'info');\n      addLog('theme_backend 服务应运行在 http://localhost:8000', 'info');\n    });\n    return {\n      testInput,\n      isGenerating,\n      isListening,\n      voiceResult,\n      currentResult,\n      testLogs,\n      testScenes,\n      testTextToImage,\n      testVoiceInput,\n      testScene\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "ImageGenerationService", "LlmService", "AsrService", "AIColorAnalyzer", "name", "emits", "setup", "props", "emit", "testInput", "isGenerating", "isListening", "voiceResult", "currentResult", "testLogs", "testScenes", "id", "icon", "prompt", "addLog", "message", "type", "value", "unshift", "time", "Date", "toLocaleTimeString", "length", "pop", "testTextToImage", "trim", "input", "optimizedPrompt", "status", "statusText", "imageUrl", "colors", "llmService", "generateResponse", "imageService", "imageResult", "generateWallpaper", "intelligentColors", "analyzeWallpaperAndGenerateColors", "aiAnalysis", "mood", "brightness", "error", "console", "testVoiceInput", "isSupported", "result", "startListening", "testScene", "scene"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue"], "sourcesContent": ["<template>\r\n  <div class=\"test-scene-generation\">\r\n    <div class=\"test-header\">\r\n      <h2>🧪 AI-HMI 场景生成测试</h2>\r\n      <p>测试语音输入 → LLM优化 → Kolors生图 → 玻璃态主题应用的完整流程</p>\r\n    </div>\r\n\r\n    <div class=\"test-controls\">\r\n      <div class=\"input-section\">\r\n        <h3>📝 输入测试</h3>\r\n        <div class=\"input-group\">\r\n          <label>文本输入:</label>\r\n          <input \r\n            v-model=\"testInput\" \r\n            type=\"text\" \r\n            placeholder=\"例如：温暖的日落场景，现代玻璃建筑\"\r\n            @keyup.enter=\"testTextToImage\"\r\n          />\r\n          <button @click=\"testTextToImage\" :disabled=\"isGenerating\">\r\n            {{ isGenerating ? '生成中...' : '生成壁纸' }}\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"voice-group\">\r\n          <label>语音输入:</label>\r\n          <button @click=\"testVoiceInput\" :disabled=\"isListening\">\r\n            {{ isListening ? '🎤 听取中...' : '🎤 开始语音输入' }}\r\n          </button>\r\n          <span v-if=\"voiceResult\" class=\"voice-result\">识别结果: {{ voiceResult }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"scene-section\">\r\n        <h3>🎭 场景测试</h3>\r\n        <div class=\"scene-buttons\">\r\n          <button \r\n            v-for=\"scene in testScenes\" \r\n            :key=\"scene.id\"\r\n            @click=\"testScene(scene)\"\r\n            class=\"scene-btn\"\r\n          >\r\n            <i :class=\"scene.icon\"></i>\r\n            {{ scene.name }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"test-results\">\r\n      <div class=\"result-section\">\r\n        <h3>📊 测试结果</h3>\r\n        <div v-if=\"currentResult\" class=\"result-display\">\r\n          <div class=\"result-item\">\r\n            <strong>原始输入:</strong> {{ currentResult.input }}\r\n          </div>\r\n          <div class=\"result-item\">\r\n            <strong>LLM优化后:</strong> {{ currentResult.optimizedPrompt }}\r\n          </div>\r\n          <div class=\"result-item\">\r\n            <strong>生成状态:</strong> \r\n            <span :class=\"currentResult.status\">{{ currentResult.statusText }}</span>\r\n          </div>\r\n          <div v-if=\"currentResult.imageUrl\" class=\"result-item\">\r\n            <strong>生成图片:</strong>\r\n            <img :src=\"currentResult.imageUrl\" alt=\"生成的壁纸\" class=\"generated-image\" />\r\n          </div>\r\n          <div v-if=\"currentResult.colors\" class=\"result-item\">\r\n            <strong>提取颜色:</strong>\r\n            <div class=\"color-palette\">\r\n              <div \r\n                v-for=\"(color, key) in currentResult.colors\" \r\n                :key=\"key\"\r\n                class=\"color-item\"\r\n                :style=\"{ backgroundColor: color }\"\r\n                :title=\"`${key}: ${color}`\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"log-section\">\r\n        <h3>📝 测试日志</h3>\r\n        <div class=\"log-display\">\r\n          <div \r\n            v-for=\"(log, index) in testLogs\" \r\n            :key=\"index\"\r\n            class=\"log-item\"\r\n            :class=\"log.type\"\r\n          >\r\n            <span class=\"log-time\">{{ log.time }}</span>\r\n            <span class=\"log-message\">{{ log.message }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted } from 'vue'\r\nimport ImageGenerationService from '@/services/ImageGenerationService'\r\nimport LlmService from '@/services/LlmService'\r\nimport AsrService from '@/services/AsrService'\r\n// import ColorExtractor from '@/utils/ColorExtractor' // 已被AIColorAnalyzer替代\r\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer'\r\n\r\nexport default {\r\n  name: 'TestSceneGeneration',\r\n  emits: ['colors-extracted'],\r\n  setup(props, { emit }) {\r\n    const testInput = ref('温暖的日落场景，现代玻璃建筑')\r\n    const isGenerating = ref(false)\r\n    const isListening = ref(false)\r\n    const voiceResult = ref('')\r\n    const currentResult = ref(null)\r\n    const testLogs = ref([])\r\n\r\n    const testScenes = ref([\r\n      { id: 'morning', name: '早晨通勤', icon: 'fas fa-sun', prompt: '清晨阳光，现代玻璃建筑，商务氛围' },\r\n      { id: 'evening', name: '晚间放松', icon: 'fas fa-moon', prompt: '温暖夜景，柔和灯光，舒适氛围' },\r\n      { id: 'rainy', name: '雨夜模式', icon: 'fas fa-cloud-rain', prompt: '雨夜街景，霓虹灯光，玻璃反射' },\r\n      { id: 'family', name: '家庭出行', icon: 'fas fa-car', prompt: '温馨家庭，阳光明媚，愉快旅程' }\r\n    ])\r\n\r\n    const addLog = (message, type = 'info') => {\r\n      testLogs.value.unshift({\r\n        time: new Date().toLocaleTimeString(),\r\n        message,\r\n        type\r\n      })\r\n      if (testLogs.value.length > 50) {\r\n        testLogs.value.pop()\r\n      }\r\n    }\r\n\r\n    const testTextToImage = async () => {\r\n      if (!testInput.value.trim()) {\r\n        addLog('请输入测试文本', 'error')\r\n        return\r\n      }\r\n\r\n      isGenerating.value = true\r\n      addLog(`开始测试: ${testInput.value}`, 'info')\r\n\r\n      try {\r\n        currentResult.value = {\r\n          input: testInput.value,\r\n          optimizedPrompt: '',\r\n          status: 'processing',\r\n          statusText: '处理中...',\r\n          imageUrl: null,\r\n          colors: null\r\n        }\r\n\r\n        // 1. LLM优化提示词\r\n        addLog('步骤1: LLM优化提示词...', 'info')\r\n        const llmService = new LlmService()\r\n        const optimizedPrompt = await llmService.generateResponse(testInput.value)\r\n        currentResult.value.optimizedPrompt = optimizedPrompt\r\n        addLog(`LLM优化完成: ${optimizedPrompt}`, 'success')\r\n\r\n        // 2. 调用Kolors生图\r\n        addLog('步骤2: 调用Kolors生图API...', 'info')\r\n        const imageService = new ImageGenerationService()\r\n        const imageResult = await imageService.generateWallpaper(optimizedPrompt)\r\n        currentResult.value.imageUrl = imageResult.imageUrl\r\n        addLog(`图片生成完成: ${imageResult.imageUrl}`, 'success')\r\n\r\n        // 3. AI智能配色分析\r\n        addLog('步骤3: AI智能配色分析...', 'info')\r\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(\r\n          imageResult.imageUrl,\r\n          testInput.value,\r\n          optimizedPrompt\r\n        )\r\n        currentResult.value.colors = intelligentColors\r\n        addLog('AI智能配色完成', 'success')\r\n\r\n        // 显示AI分析结果\r\n        if (intelligentColors.aiAnalysis) {\r\n          addLog(`AI分析: ${intelligentColors.aiAnalysis.mood}氛围, ${intelligentColors.aiAnalysis.brightness}亮度`, 'info')\r\n        }\r\n\r\n        // 发送颜色到父组件\r\n        emit('colors-extracted', intelligentColors)\r\n\r\n        currentResult.value.status = 'success'\r\n        currentResult.value.statusText = '✅ 生成成功'\r\n        addLog('🎉 完整流程测试成功！', 'success')\r\n\r\n      } catch (error) {\r\n        console.error('测试失败:', error)\r\n        currentResult.value.status = 'error'\r\n        currentResult.value.statusText = '❌ 生成失败'\r\n        addLog(`测试失败: ${error.message}`, 'error')\r\n      } finally {\r\n        isGenerating.value = false\r\n      }\r\n    }\r\n\r\n    const testVoiceInput = async () => {\r\n      if (!AsrService.isSupported()) {\r\n        addLog('浏览器不支持语音识别', 'error')\r\n        return\r\n      }\r\n\r\n      isListening.value = true\r\n      addLog('开始语音识别...', 'info')\r\n\r\n      try {\r\n        const result = await AsrService.startListening()\r\n        voiceResult.value = result\r\n        testInput.value = result\r\n        addLog(`语音识别结果: ${result}`, 'success')\r\n        \r\n        // 自动进行文生图测试\r\n        await testTextToImage()\r\n      } catch (error) {\r\n        addLog(`语音识别失败: ${error.message}`, 'error')\r\n      } finally {\r\n        isListening.value = false\r\n      }\r\n    }\r\n\r\n    const testScene = async (scene) => {\r\n      testInput.value = scene.prompt\r\n      addLog(`测试场景: ${scene.name}`, 'info')\r\n      await testTextToImage()\r\n    }\r\n\r\n    onMounted(() => {\r\n      addLog('AI-HMI 场景生成测试组件已加载', 'info')\r\n      addLog('theme_backend 服务应运行在 http://localhost:8000', 'info')\r\n    })\r\n\r\n    return {\r\n      testInput,\r\n      isGenerating,\r\n      isListening,\r\n      voiceResult,\r\n      currentResult,\r\n      testLogs,\r\n      testScenes,\r\n      testTextToImage,\r\n      testVoiceInput,\r\n      testScene\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.test-scene-generation {\r\n  padding: 20px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n}\r\n\r\n.test-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 15px;\r\n}\r\n\r\n.test-controls {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.input-section, .scene-section {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.input-group, .voice-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 10px;\r\n  margin: 5px 0;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.scene-buttons {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 10px;\r\n}\r\n\r\n.scene-btn {\r\n  padding: 12px;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 500;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\n.scene-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.test-results {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.result-section, .log-section {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.result-display {\r\n  background: white;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.generated-image {\r\n  max-width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.color-palette {\r\n  display: flex;\r\n  gap: 5px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.color-item {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.log-display {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  background: #1a1a1a;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 12px;\r\n}\r\n\r\n.log-item {\r\n  margin-bottom: 5px;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.log-time {\r\n  color: #888;\r\n  min-width: 80px;\r\n}\r\n\r\n.log-item.info .log-message { color: #4fc3f7; }\r\n.log-item.success .log-message { color: #66bb6a; }\r\n.log-item.error .log-message { color: #ef5350; }\r\n\r\n.status.success { color: #4caf50; }\r\n.status.error { color: #f44336; }\r\n.status.processing { color: #ff9800; }\r\n\r\nbutton {\r\n  padding: 10px 15px;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  margin: 5px;\r\n  font-weight: 500;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\nbutton:hover:not(:disabled) {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\nbutton:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  background: rgba(128, 128, 128, 0.5);\r\n}\r\n\r\n.voice-result {\r\n  color: #4caf50;\r\n  font-style: italic;\r\n  margin-left: 10px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .test-controls,\r\n  .test-results {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .scene-buttons {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoGA,SAASA,GAAG,EAAEC,SAAQ,QAAS,KAAI;AACnC,OAAOC,sBAAqB,MAAO,mCAAkC;AACrE,OAAOC,UAAS,MAAO,uBAAsB;AAC7C,OAAOC,UAAS,MAAO,uBAAsB;AAC7C;AACA,OAAOC,eAAc,MAAO,yBAAwB;AAEpD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE,CAAC,kBAAkB,CAAC;EAC3BC,KAAKA,CAACC,KAAK,EAAE;IAAEC;EAAK,CAAC,EAAE;IACrB,MAAMC,SAAQ,GAAIX,GAAG,CAAC,gBAAgB;IACtC,MAAMY,YAAW,GAAIZ,GAAG,CAAC,KAAK;IAC9B,MAAMa,WAAU,GAAIb,GAAG,CAAC,KAAK;IAC7B,MAAMc,WAAU,GAAId,GAAG,CAAC,EAAE;IAC1B,MAAMe,aAAY,GAAIf,GAAG,CAAC,IAAI;IAC9B,MAAMgB,QAAO,GAAIhB,GAAG,CAAC,EAAE;IAEvB,MAAMiB,UAAS,GAAIjB,GAAG,CAAC,CACrB;MAAEkB,EAAE,EAAE,SAAS;MAAEZ,IAAI,EAAE,MAAM;MAAEa,IAAI,EAAE,YAAY;MAAEC,MAAM,EAAE;IAAmB,CAAC,EAC/E;MAAEF,EAAE,EAAE,SAAS;MAAEZ,IAAI,EAAE,MAAM;MAAEa,IAAI,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAiB,CAAC,EAC9E;MAAEF,EAAE,EAAE,OAAO;MAAEZ,IAAI,EAAE,MAAM;MAAEa,IAAI,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAiB,CAAC,EAClF;MAAEF,EAAE,EAAE,QAAQ;MAAEZ,IAAI,EAAE,MAAM;MAAEa,IAAI,EAAE,YAAY;MAAEC,MAAM,EAAE;IAAiB,EAC5E;IAED,MAAMC,MAAK,GAAIA,CAACC,OAAO,EAAEC,IAAG,GAAI,MAAM,KAAK;MACzCP,QAAQ,CAACQ,KAAK,CAACC,OAAO,CAAC;QACrBC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACrCN,OAAO;QACPC;MACF,CAAC;MACD,IAAIP,QAAQ,CAACQ,KAAK,CAACK,MAAK,GAAI,EAAE,EAAE;QAC9Bb,QAAQ,CAACQ,KAAK,CAACM,GAAG,CAAC;MACrB;IACF;IAEA,MAAMC,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI,CAACpB,SAAS,CAACa,KAAK,CAACQ,IAAI,CAAC,CAAC,EAAE;QAC3BX,MAAM,CAAC,SAAS,EAAE,OAAO;QACzB;MACF;MAEAT,YAAY,CAACY,KAAI,GAAI,IAAG;MACxBH,MAAM,CAAC,SAASV,SAAS,CAACa,KAAK,EAAE,EAAE,MAAM;MAEzC,IAAI;QACFT,aAAa,CAACS,KAAI,GAAI;UACpBS,KAAK,EAAEtB,SAAS,CAACa,KAAK;UACtBU,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE,YAAY;UACpBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,IAAI;UACdC,MAAM,EAAE;QACV;;QAEA;QACAjB,MAAM,CAAC,kBAAkB,EAAE,MAAM;QACjC,MAAMkB,UAAS,GAAI,IAAIpC,UAAU,CAAC;QAClC,MAAM+B,eAAc,GAAI,MAAMK,UAAU,CAACC,gBAAgB,CAAC7B,SAAS,CAACa,KAAK;QACzET,aAAa,CAACS,KAAK,CAACU,eAAc,GAAIA,eAAc;QACpDb,MAAM,CAAC,YAAYa,eAAe,EAAE,EAAE,SAAS;;QAE/C;QACAb,MAAM,CAAC,uBAAuB,EAAE,MAAM;QACtC,MAAMoB,YAAW,GAAI,IAAIvC,sBAAsB,CAAC;QAChD,MAAMwC,WAAU,GAAI,MAAMD,YAAY,CAACE,iBAAiB,CAACT,eAAe;QACxEnB,aAAa,CAACS,KAAK,CAACa,QAAO,GAAIK,WAAW,CAACL,QAAO;QAClDhB,MAAM,CAAC,WAAWqB,WAAW,CAACL,QAAQ,EAAE,EAAE,SAAS;;QAEnD;QACAhB,MAAM,CAAC,kBAAkB,EAAE,MAAM;QACjC,MAAMuB,iBAAgB,GAAI,MAAMvC,eAAe,CAACwC,iCAAiC,CAC/EH,WAAW,CAACL,QAAQ,EACpB1B,SAAS,CAACa,KAAK,EACfU,eACF;QACAnB,aAAa,CAACS,KAAK,CAACc,MAAK,GAAIM,iBAAgB;QAC7CvB,MAAM,CAAC,UAAU,EAAE,SAAS;;QAE5B;QACA,IAAIuB,iBAAiB,CAACE,UAAU,EAAE;UAChCzB,MAAM,CAAC,SAASuB,iBAAiB,CAACE,UAAU,CAACC,IAAI,OAAOH,iBAAiB,CAACE,UAAU,CAACE,UAAU,IAAI,EAAE,MAAM;QAC7G;;QAEA;QACAtC,IAAI,CAAC,kBAAkB,EAAEkC,iBAAiB;QAE1C7B,aAAa,CAACS,KAAK,CAACW,MAAK,GAAI,SAAQ;QACrCpB,aAAa,CAACS,KAAK,CAACY,UAAS,GAAI,QAAO;QACxCf,MAAM,CAAC,cAAc,EAAE,SAAS;MAElC,EAAE,OAAO4B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5BlC,aAAa,CAACS,KAAK,CAACW,MAAK,GAAI,OAAM;QACnCpB,aAAa,CAACS,KAAK,CAACY,UAAS,GAAI,QAAO;QACxCf,MAAM,CAAC,SAAS4B,KAAK,CAAC3B,OAAO,EAAE,EAAE,OAAO;MAC1C,UAAU;QACRV,YAAY,CAACY,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAM2B,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI,CAAC/C,UAAU,CAACgD,WAAW,CAAC,CAAC,EAAE;QAC7B/B,MAAM,CAAC,YAAY,EAAE,OAAO;QAC5B;MACF;MAEAR,WAAW,CAACW,KAAI,GAAI,IAAG;MACvBH,MAAM,CAAC,WAAW,EAAE,MAAM;MAE1B,IAAI;QACF,MAAMgC,MAAK,GAAI,MAAMjD,UAAU,CAACkD,cAAc,CAAC;QAC/CxC,WAAW,CAACU,KAAI,GAAI6B,MAAK;QACzB1C,SAAS,CAACa,KAAI,GAAI6B,MAAK;QACvBhC,MAAM,CAAC,WAAWgC,MAAM,EAAE,EAAE,SAAS;;QAErC;QACA,MAAMtB,eAAe,CAAC;MACxB,EAAE,OAAOkB,KAAK,EAAE;QACd5B,MAAM,CAAC,WAAW4B,KAAK,CAAC3B,OAAO,EAAE,EAAE,OAAO;MAC5C,UAAU;QACRT,WAAW,CAACW,KAAI,GAAI,KAAI;MAC1B;IACF;IAEA,MAAM+B,SAAQ,GAAI,MAAOC,KAAK,IAAK;MACjC7C,SAAS,CAACa,KAAI,GAAIgC,KAAK,CAACpC,MAAK;MAC7BC,MAAM,CAAC,SAASmC,KAAK,CAAClD,IAAI,EAAE,EAAE,MAAM;MACpC,MAAMyB,eAAe,CAAC;IACxB;IAEA9B,SAAS,CAAC,MAAM;MACdoB,MAAM,CAAC,oBAAoB,EAAE,MAAM;MACnCA,MAAM,CAAC,4CAA4C,EAAE,MAAM;IAC7D,CAAC;IAED,OAAO;MACLV,SAAS;MACTC,YAAY;MACZC,WAAW;MACXC,WAAW;MACXC,aAAa;MACbC,QAAQ;MACRC,UAAU;MACVc,eAAe;MACfoB,cAAc;MACdI;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}