{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"immersive-interface\"\n};\nconst _hoisted_2 = {\n  class: \"conversation-panel\"\n};\nconst _hoisted_3 = {\n  class: \"preset-buttons\"\n};\nconst _hoisted_4 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 左侧对话区域 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"welcome-message\"\n  }, [_createElementVNode(\"p\", null, \"你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.presetScenes, preset => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: preset.id,\n      onClick: $event => $setup.generateWallpaper(preset.prompt),\n      class: \"preset-btn\"\n    }, _toDisplayString(preset.name), 9 /* TEXT, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"description\"\n  }, [_createElementVNode(\"p\", null, \"根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍\")], -1 /* CACHED */))]), _createCommentVNode(\" 右侧VPA区域 \"), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"vpa-panel\"\n  }, [_createElementVNode(\"div\", {\n    class: \"vpa-container\"\n  }, [_createElementVNode(\"img\", {\n    src: \"/images/vpa2.gif\",\n    alt: \"VPA智能助手\",\n    class: \"vpa-avatar\"\n  })])], -1 /* CACHED */))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$setup", "presetScenes", "preset", "key", "id", "onClick", "$event", "generateWallpaper", "prompt", "name", "_hoisted_4", "src", "alt"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue"], "sourcesContent": ["<template>\r\n  <div class=\"immersive-interface\">\r\n    <!-- 左侧对话区域 -->\r\n    <div class=\"conversation-panel\">\r\n      <div class=\"welcome-message\">\r\n        <p>你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>\r\n      </div>\r\n      \r\n      <div class=\"preset-buttons\">\r\n        <button \r\n          v-for=\"preset in presetScenes\" \r\n          :key=\"preset.id\"\r\n          @click=\"generateWallpaper(preset.prompt)\"\r\n          class=\"preset-btn\"\r\n        >\r\n          {{ preset.name }}\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"description\">\r\n        <p>根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧VPA区域 -->\r\n    <div class=\"vpa-panel\">\r\n      <div class=\"vpa-container\">\r\n        <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed } from 'vue'\r\nimport EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'\r\nimport SceneContextManager from '@/services/SceneContextManager'\r\n\r\nexport default {\r\n  name: 'ImmersiveWallpaperInterface',\r\n  emits: ['wallpaper-prompt-ready'],\r\n  \r\n  setup(props, { emit }) {\r\n    const emotionalPromptGenerator = new EmotionalPromptGenerator()\r\n    const contextManager = new SceneContextManager()\r\n    \r\n    const presetScenes = ref([\r\n      {\r\n        id: 'commute',\r\n        name: '生成通勤桌面',\r\n        prompt: '动漫卡通风格的通勤场景，可爱的卡通车厢，柔和的晨光，温馨的通勤氛围'\r\n      },\r\n      {\r\n        id: 'navigation',\r\n        name: '导航3D效果',\r\n        prompt: '动漫卡通风格的导航界面，可爱的地图元素，卡通化的道路标识，温馨的导航体验'\r\n      },\r\n      {\r\n        id: 'solitude',\r\n        name: '帮我规划一个独处的桌面',\r\n        prompt: '动漫卡通风格的独处空间，舒适的小屋，柔和的灯光，温馨治愈的独处氛围'\r\n      },\r\n      {\r\n        id: 'spring',\r\n        name: '生成春游桌面',\r\n        prompt: '动漫卡通风格的春游场景，可爱的花草树木，明媚的春光，温馨的春日氛围'\r\n      },\r\n      {\r\n        id: 'weekend',\r\n        name: '帮我规划一个周末一日游',\r\n        prompt: '动漫卡通风格的周末出游，可爱的风景元素，轻松的氛围，温馨的周末时光'\r\n      }\r\n    ])\r\n\r\n    const generateWallpaper = async (prompt) => {\r\n      console.log('生成壁纸:', prompt)\r\n      \r\n      try {\r\n        // 获取当前上下文\r\n        const currentContext = contextManager.getPromptGenerationContext()\r\n        console.log('📋 当前上下文:', currentContext)\r\n        \r\n        // 创建场景信息对象\r\n        const sceneInfo = {\r\n          id: 'immersive',\r\n          name: '沉浸式模式',\r\n          description: prompt,\r\n          theme: 'immersive'\r\n        }\r\n        \r\n        // 生成情感化提示词\r\n        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(\r\n          sceneInfo,\r\n          currentContext\r\n        )\r\n        \r\n        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)\r\n        \r\n        // 发送完整的事件数据\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt: emotionalPrompt,\r\n          originalPrompt: prompt,\r\n          scene: sceneInfo,\r\n          context: currentContext\r\n        })\r\n        \r\n      } catch (error) {\r\n        console.error('情感化提示词生成失败，使用原始提示词:', error)\r\n        \r\n        // 降级到原始提示词\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt,\r\n          originalPrompt: prompt,\r\n          scene: {\r\n            id: 'immersive',\r\n            name: '沉浸式模式',\r\n            description: prompt,\r\n            theme: 'immersive'\r\n          },\r\n          context: contextManager.getPromptGenerationContext()\r\n        })\r\n      }\r\n    }\r\n\r\n    return {\r\n      presetScenes,\r\n      generateWallpaper,\r\n      // 暴露上下文信息用于调试\r\n      contextInfo: computed(() => contextManager.getStatistics())\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.immersive-interface {\r\n  width: 100%;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 40px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.conversation-panel {\r\n  flex: 1;\r\n  max-width: 400px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  padding: 30px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-message {\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.welcome-message p {\r\n  color: white;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.preset-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.preset-btn {\r\n  padding: 12px 20px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 25px;\r\n  color: white;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n  text-align: left;\r\n}\r\n\r\n.preset-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.description {\r\n  margin-top: 20px;\r\n}\r\n\r\n.description p {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.vpa-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  max-width: 500px;\r\n}\r\n\r\n.vpa-container {\r\n  width: 300px;\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 50%;\r\n  border: 2px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.vpa-avatar {\r\n  width: 200px;\r\n  height: 200px;\r\n  object-fit: contain;\r\n  border-radius: 50%;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .immersive-interface {\r\n    flex-direction: column;\r\n    padding: 20px;\r\n    gap: 30px;\r\n  }\r\n  \r\n  .conversation-panel {\r\n    max-width: 100%;\r\n  }\r\n  \r\n  .vpa-container {\r\n    width: 200px;\r\n    height: 200px;\r\n  }\r\n  \r\n  .vpa-avatar {\r\n    width: 150px;\r\n    height: 150px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAoB;;EAKxBA,KAAK,EAAC;AAAgB;;;uBAP/BC,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNC,UAmBM,G,0BAlBJD,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAsD,WAAnD,iDAA+C,E,qBAGpDA,mBAAA,CASM,OATNE,UASM,I,kBARJL,mBAAA,CAOSM,SAAA,QAAAC,WAAA,CANUC,MAAA,CAAAC,YAAY,EAAtBC,MAAM;yBADfV,mBAAA,CAOS;MALNW,GAAG,EAAED,MAAM,CAACE,EAAE;MACdC,OAAK,EAAAC,MAAA,IAAEN,MAAA,CAAAO,iBAAiB,CAACL,MAAM,CAACM,MAAM;MACvCjB,KAAK,EAAC;wBAEHW,MAAM,CAACO,IAAI,wBAAAC,UAAA;8DAIlBf,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA+C,WAA5C,0CAAwC,E,uBAI/CD,mBAAA,aAAgB,E,0BAChBC,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAA+D;IAA1DgB,GAAG,EAAC,kBAAkB;IAACC,GAAG,EAAC,SAAS;IAACrB,KAAK,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}