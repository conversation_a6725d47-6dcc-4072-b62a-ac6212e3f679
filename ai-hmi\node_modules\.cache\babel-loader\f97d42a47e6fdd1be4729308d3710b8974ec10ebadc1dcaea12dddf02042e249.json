{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, createElementVNode as _createElementVNode, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dynamic-island-zone\"\n};\nconst _hoisted_2 = {\n  class: \"main-content-zone\"\n};\nconst _hoisted_3 = {\n  class: \"contextual-card-zone\"\n};\nconst _hoisted_4 = {\n  class: \"primary-driving-zone\"\n};\nconst _hoisted_5 = {\n  class: \"vpa-zone\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"main-layout-container\",\n    style: _normalizeStyle($setup.layoutStyles)\n  }, [_createCommentVNode(\" 灵动岛区域 \"), _createElementVNode(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"dynamic-island\", {}, () => [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"default-dynamic-island\"\n  }, [_createElementVNode(\"span\", null, \"AI-HMI 智能座舱\")], -1 /* CACHED */))], true)]), _createCommentVNode(\" 主内容区域 - 分区融合式布局 \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 情景卡片区 (左侧) \"), _createElementVNode(\"div\", _hoisted_3, [_renderSlot(_ctx.$slots, \"card-zone\", {\n    layout: $setup.sceneLayout\n  }, () => [_createCommentVNode(\" 默认卡片区域内容 \")], true)]), _createCommentVNode(\" 边界融合效果 \"), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"zone-fusion-border\"\n  }, null, -1 /* CACHED */)), _createCommentVNode(\" 主驾驶信息区 (右侧) \"), _createElementVNode(\"div\", _hoisted_4, [_renderSlot(_ctx.$slots, \"driving-zone\", {\n    layout: $setup.sceneLayout\n  }, () => [_createCommentVNode(\" 默认导航地图区域 \"), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"default-navigation\"\n  }, [_createElementVNode(\"div\", {\n    class: \"nav-placeholder\"\n  }, [_createElementVNode(\"i\", {\n    class: \"nav-icon\"\n  }, \"🗺️\"), _createElementVNode(\"span\", null, \"导航地图区域\")])], -1 /* CACHED */))], true)])]), _createCommentVNode(\" VPA数字人区域 \"), _createElementVNode(\"div\", _hoisted_5, [_renderSlot(_ctx.$slots, \"vpa\", {\n    layout: $setup.sceneLayout\n  }, () => [_createCommentVNode(\" VPA组件将在这里渲染 \")], true)])], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "style", "_normalizeStyle", "$setup", "layoutStyles", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_hoisted_2", "_hoisted_3", "layout", "sceneLayout", "_hoisted_4", "_hoisted_5"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\layout\\MainLayoutContainer.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"main-layout-container\"\n    :style=\"layoutStyles\"\n  >\n    <!-- 灵动岛区域 -->\n    <div class=\"dynamic-island-zone\">\n      <slot name=\"dynamic-island\">\n        <div class=\"default-dynamic-island\">\n          <span>AI-HMI 智能座舱</span>\n        </div>\n      </slot>\n    </div>\n\n    <!-- 主内容区域 - 分区融合式布局 -->\n    <div class=\"main-content-zone\">\n      <!-- 情景卡片区 (左侧) -->\n      <div class=\"contextual-card-zone\">\n        <slot name=\"card-zone\" :layout=\"sceneLayout\">\n          <!-- 默认卡片区域内容 -->\n        </slot>\n      </div>\n\n      <!-- 边界融合效果 -->\n      <div class=\"zone-fusion-border\"></div>\n\n      <!-- 主驾驶信息区 (右侧) -->\n      <div class=\"primary-driving-zone\">\n        <slot name=\"driving-zone\" :layout=\"sceneLayout\">\n          <!-- 默认导航地图区域 -->\n          <div class=\"default-navigation\">\n            <div class=\"nav-placeholder\">\n              <i class=\"nav-icon\">🗺️</i>\n              <span>导航地图区域</span>\n            </div>\n          </div>\n        </slot>\n      </div>\n    </div>\n\n    <!-- VPA数字人区域 -->\n    <div class=\"vpa-zone\">\n      <slot name=\"vpa\" :layout=\"sceneLayout\">\n        <!-- VPA组件将在这里渲染 -->\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store/modules/layout'\n\nexport default {\n  name: 'MainLayoutContainer',\n  props: {\n    scene: {\n      type: String,\n      default: 'family'\n    }\n  },\n  setup(props) {\n    const layoutStore = useLayoutStore()\n\n    const layoutStyles = computed(() => ({\n      ...layoutStore.gridCSSVariables,\n      '--scene-layout': props.scene\n    }))\n\n    const sceneLayout = computed(() => {\n      return layoutStore.getSceneLayout(props.scene)\n    })\n\n    return {\n      layoutStyles,\n      sceneLayout\n    }\n  }\n}\n</script>\n\n<style scoped>\n.main-layout-container {\n  width: 100vw;\n  height: 100vh;\n  display: grid;\n  grid-template-columns: repeat(var(--grid-columns), 1fr);\n  grid-template-rows: auto 1fr;\n  gap: var(--grid-gap);\n  padding: var(--grid-gap);\n  background: transparent;\n  overflow: hidden;\n  position: relative;\n}\n\n/* 灵动岛区域 */\n.dynamic-island-zone {\n  grid-column: 1 / -1;\n  grid-row: 1;\n  height: 44px;\n  z-index: var(--z-navigation, 1000);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.default-dynamic-island {\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(20px);\n  border-radius: 22px;\n  padding: 8px 24px;\n  color: white;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 主内容区域 */\n.main-content-zone {\n  grid-column: 1 / -1;\n  grid-row: 2;\n  display: grid;\n  grid-template-columns: 1fr auto 1fr;\n  gap: 0;\n  position: relative;\n  min-height: 0;\n}\n\n/* 情景卡片区 (左侧) */\n.contextual-card-zone {\n  grid-column: 1;\n  padding: var(--grid-gap);\n  overflow-y: auto;\n  overflow-x: hidden;\n  background: rgba(255, 255, 255, 0.02);\n  backdrop-filter: blur(10px);\n  border-radius: 20px 0 0 20px;\n}\n\n/* 边界融合效果 */\n.zone-fusion-border {\n  grid-column: 2;\n  width: 2px;\n  background: linear-gradient(\n    to bottom,\n    transparent 0%,\n    rgba(255, 255, 255, 0.1) 20%,\n    rgba(255, 255, 255, 0.2) 50%,\n    rgba(255, 255, 255, 0.1) 80%,\n    transparent 100%\n  );\n  position: relative;\n}\n\n.zone-fusion-border::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -10px;\n  right: -10px;\n  height: 100%;\n  background: linear-gradient(\n    to right,\n    rgba(255, 255, 255, 0.05) 0%,\n    transparent 50%,\n    rgba(255, 255, 255, 0.05) 100%\n  );\n  filter: blur(5px);\n}\n\n/* 主驾驶信息区 (右侧) */\n.primary-driving-zone {\n  grid-column: 3;\n  padding: var(--grid-gap);\n  background: rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(5px);\n  border-radius: 0 20px 20px 0;\n  position: relative;\n}\n\n.default-navigation {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 15px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.nav-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n}\n\n.nav-icon {\n  font-size: 48px;\n}\n\n/* VPA数字人区域 */\n.vpa-zone {\n  position: absolute;\n  bottom: calc(var(--grid-gap) * 2);\n  left: calc(var(--grid-gap) * 2);\n  width: calc(2 * (100vw - 2 * var(--grid-gap)) / var(--grid-columns));\n  height: calc(2 * (100vh - 44px - 2 * var(--grid-gap)) / var(--grid-rows));\n  z-index: var(--z-vpa, 100);\n  pointer-events: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .main-content-zone {\n    grid-template-columns: 1fr;\n    grid-template-rows: 1fr auto;\n  }\n  \n  .contextual-card-zone {\n    grid-column: 1;\n    grid-row: 1;\n    border-radius: 20px 20px 0 0;\n  }\n  \n  .zone-fusion-border {\n    grid-column: 1;\n    grid-row: 2;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(\n      to right,\n      transparent 0%,\n      rgba(255, 255, 255, 0.1) 20%,\n      rgba(255, 255, 255, 0.2) 50%,\n      rgba(255, 255, 255, 0.1) 80%,\n      transparent 100%\n    );\n  }\n  \n  .primary-driving-zone {\n    grid-column: 1;\n    grid-row: 3;\n    border-radius: 0 0 20px 20px;\n  }\n}\n\n@media (max-width: 768px) {\n  .main-layout-container {\n    padding: calc(var(--grid-gap) / 2);\n    gap: calc(var(--grid-gap) / 2);\n  }\n  \n  .vpa-zone {\n    width: calc(3 * (100vw - var(--grid-gap)) / var(--grid-columns));\n    height: calc(3 * (100vh - 44px - var(--grid-gap)) / var(--grid-rows));\n  }\n}\n</style>\n"], "mappings": ";;EAMSA,KAAK,EAAC;AAAqB;;EAS3BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAsB;;EAU5BA,KAAK,EAAC;AAAsB;;EAc9BA,KAAK,EAAC;AAAU;;uBAxCvBC,mBAAA,CA6CM;IA5CJD,KAAK,EAAC,uBAAuB;IAC5BE,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,YAAY;MAEpBC,mBAAA,WAAc,EACdC,mBAAA,CAMM,OANNC,UAMM,GALJC,WAAA,CAIOC,IAAA,CAAAC,MAAA,wBAJP,MAIO,C,0BAHLJ,mBAAA,CAEM;IAFDP,KAAK,EAAC;EAAwB,IACjCO,mBAAA,CAAwB,cAAlB,aAAW,E,+BAKvBD,mBAAA,qBAAwB,EACxBC,mBAAA,CAuBM,OAvBNK,UAuBM,GAtBJN,mBAAA,gBAAmB,EACnBC,mBAAA,CAIM,OAJNM,UAIM,GAHJJ,WAAA,CAEOC,IAAA,CAAAC,MAAA;IAFiBG,MAAM,EAAEV,MAAA,CAAAW;EAAW,GAA3C,MAEO,CADLT,mBAAA,cAAiB,C,WAIrBA,mBAAA,YAAe,E,0BACfC,mBAAA,CAAsC;IAAjCP,KAAK,EAAC;EAAoB,4BAE/BM,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVNS,UAUM,GATJP,WAAA,CAQOC,IAAA,CAAAC,MAAA;IARoBG,MAAM,EAAEV,MAAA,CAAAW;EAAW,GAA9C,MAQO,CAPLT,mBAAA,cAAiB,E,0BACjBC,mBAAA,CAKM;IALDP,KAAK,EAAC;EAAoB,IAC7BO,mBAAA,CAGM;IAHDP,KAAK,EAAC;EAAiB,IAC1BO,mBAAA,CAA2B;IAAxBP,KAAK,EAAC;EAAU,GAAC,KAAG,GACvBO,mBAAA,CAAmB,cAAb,QAAM,E,mCAOtBD,mBAAA,cAAiB,EACjBC,mBAAA,CAIM,OAJNU,UAIM,GAHJR,WAAA,CAEOC,IAAA,CAAAC,MAAA;IAFWG,MAAM,EAAEV,MAAA,CAAAW;EAAW,GAArC,MAEO,CADLT,mBAAA,iBAAoB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}