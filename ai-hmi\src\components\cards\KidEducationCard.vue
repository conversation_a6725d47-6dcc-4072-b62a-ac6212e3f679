<template>
  <BaseCard
    card-type="kidEducation"
    size="large"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :title="'儿童教育'"
    :icon="'fas fa-graduation-cap'"
    :clickable="true"
    :show-footer="true"
    @click="handleCardClick"
    class="kid-education-card"
  >
    <div class="education-content">
      <!-- 当前学习内容 -->
      <div class="current-lesson">
        <div class="lesson-header">
          <div class="lesson-icon">
            <i :class="currentLesson.icon"></i>
          </div>
          <div class="lesson-info">
            <h4 class="lesson-title">{{ currentLesson.title }}</h4>
            <p class="lesson-description">{{ currentLesson.description }}</p>
          </div>
        </div>
        
        <!-- 学习进度 -->
        <div class="lesson-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: `${currentLesson.progress}%` }"
            ></div>
          </div>
          <span class="progress-text">{{ currentLesson.progress }}% 完成</span>
        </div>
      </div>

      <!-- 学习模式选择 -->
      <div class="learning-modes">
        <button 
          v-for="mode in learningModes" 
          :key="mode.id"
          @click="selectLearningMode(mode)"
          :class="['mode-btn', { active: selectedMode === mode.id }]"
        >
          <i :class="mode.icon"></i>
          <span>{{ mode.name }}</span>
        </button>
      </div>

      <!-- 互动区域 -->
      <div class="interaction-area">
        <div v-if="selectedMode === 'story'" class="story-mode">
          <div class="story-content">
            <h5>{{ currentStory.title }}</h5>
            <p>{{ currentStory.content }}</p>
          </div>
          <div class="story-controls">
            <button @click="previousStory" class="story-btn">
              <i class="fas fa-step-backward"></i>
            </button>
            <button @click="toggleStoryPlay" class="story-btn play-btn">
              <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
            <button @click="nextStory" class="story-btn">
              <i class="fas fa-step-forward"></i>
            </button>
          </div>
        </div>

        <div v-else-if="selectedMode === 'quiz'" class="quiz-mode">
          <div class="quiz-question">
            <h5>{{ currentQuiz.question }}</h5>
            <div class="quiz-options">
              <button 
                v-for="(option, index) in currentQuiz.options" 
                :key="index"
                @click="selectQuizOption(index)"
                :class="['quiz-option', { 
                  selected: selectedOption === index,
                  correct: showAnswer && index === currentQuiz.correct,
                  wrong: showAnswer && selectedOption === index && index !== currentQuiz.correct
                }]"
              >
                {{ option }}
              </button>
            </div>
          </div>
        </div>

        <div v-else-if="selectedMode === 'game'" class="game-mode">
          <div class="game-content">
            <h5>数字游戏</h5>
            <div class="number-game">
              <div class="game-display">{{ gameNumber }}</div>
              <div class="game-controls">
                <button @click="gameAction('add')" class="game-btn">+1</button>
                <button @click="gameAction('subtract')" class="game-btn">-1</button>
                <button @click="gameAction('reset')" class="game-btn">重置</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="card-footer-content">
        <div class="achievement-info">
          <i class="fas fa-star"></i>
          <span>今日获得 {{ todayStars }} 颗星</span>
        </div>
        <button @click="openFullEducation" class="expand-btn">
          <i class="fas fa-expand"></i>
          <span>展开</span>
        </button>
      </div>
    </template>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import BaseCard from '../BaseCard.vue'

export default {
  name: 'KidEducationCard',
  components: {
    BaseCard
  },
  props: {
    position: {
      type: Object,
      default: () => ({ x: 1, y: 2 })
    },
    theme: {
      type: String,
      default: 'glass'
    },
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#ff6b6b',
        secondary: '#4ecdc4',
        background: 'rgba(255, 107, 107, 0.1)',
        text: '#ffffff'
      })
    }
  },
  
  emits: ['card-click', 'mode-changed', 'lesson-completed'],
  
  setup(props, { emit }) {
    // 响应式状态
    const selectedMode = ref('story')
    const isPlaying = ref(false)
    const selectedOption = ref(null)
    const showAnswer = ref(false)
    const gameNumber = ref(0)
    const todayStars = ref(3)
    
    // 当前课程数据
    const currentLesson = ref({
      title: '认识动物',
      description: '学习各种动物的名称和特征',
      icon: 'fas fa-paw',
      progress: 65
    })
    
    // 学习模式
    const learningModes = ref([
      { id: 'story', name: '故事', icon: 'fas fa-book' },
      { id: 'quiz', name: '问答', icon: 'fas fa-question-circle' },
      { id: 'game', name: '游戏', icon: 'fas fa-gamepad' }
    ])
    
    // 故事内容
    const stories = ref([
      {
        title: '小兔子的冒险',
        content: '从前有一只小兔子，它住在美丽的森林里...'
      },
      {
        title: '勇敢的小狮子',
        content: '在非洲大草原上，有一只勇敢的小狮子...'
      }
    ])
    
    const currentStoryIndex = ref(0)
    const currentStory = computed(() => stories.value[currentStoryIndex.value])
    
    // 问答内容
    const quizzes = ref([
      {
        question: '小兔子最喜欢吃什么？',
        options: ['胡萝卜', '肉', '鱼', '草'],
        correct: 0
      },
      {
        question: '狮子是什么动物？',
        options: ['食草动物', '食肉动物', '杂食动物', '不知道'],
        correct: 1
      }
    ])
    
    const currentQuizIndex = ref(0)
    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value])
    
    // 事件处理
    const handleCardClick = () => {
      emit('card-click', 'kidEducation')
    }
    
    const selectLearningMode = (mode) => {
      selectedMode.value = mode.id
      emit('mode-changed', mode.id)
      
      // 重置相关状态
      if (mode.id === 'quiz') {
        selectedOption.value = null
        showAnswer.value = false
      }
    }
    
    const previousStory = () => {
      if (currentStoryIndex.value > 0) {
        currentStoryIndex.value--
      }
    }
    
    const nextStory = () => {
      if (currentStoryIndex.value < stories.value.length - 1) {
        currentStoryIndex.value++
      }
    }
    
    const toggleStoryPlay = () => {
      isPlaying.value = !isPlaying.value
      // 这里可以集成TTS服务来朗读故事
    }
    
    const selectQuizOption = (index) => {
      if (showAnswer.value) return
      
      selectedOption.value = index
      showAnswer.value = true
      
      // 2秒后自动进入下一题
      setTimeout(() => {
        if (currentQuizIndex.value < quizzes.value.length - 1) {
          currentQuizIndex.value++
          selectedOption.value = null
          showAnswer.value = false
        } else {
          // 完成所有问题
          emit('lesson-completed', 'quiz')
        }
      }, 2000)
    }
    
    const gameAction = (action) => {
      switch (action) {
        case 'add':
          gameNumber.value++
          break
        case 'subtract':
          if (gameNumber.value > 0) {
            gameNumber.value--
          }
          break
        case 'reset':
          gameNumber.value = 0
          break
      }
    }
    
    const openFullEducation = () => {
      // 打开完整的教育界面
      console.log('打开完整教育界面')
    }
    
    // 初始化
    onMounted(() => {
      // 可以在这里加载用户的学习进度
      console.log('儿童教育卡片已加载')
    })
    
    return {
      selectedMode,
      isPlaying,
      selectedOption,
      showAnswer,
      gameNumber,
      todayStars,
      currentLesson,
      learningModes,
      currentStory,
      currentQuiz,
      handleCardClick,
      selectLearningMode,
      previousStory,
      nextStory,
      toggleStoryPlay,
      selectQuizOption,
      gameAction,
      openFullEducation
    }
  }
}
</script>

<style scoped>
.kid-education-card {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
}

.education-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
}

/* 当前课程 */
.current-lesson {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
}

.lesson-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.lesson-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--card-primary-color, #ff6b6b);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.lesson-info h4 {
  margin: 0;
  color: var(--card-text-color, #ffffff);
  font-size: 16px;
}

.lesson-info p {
  margin: 5px 0 0 0;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
  font-size: 12px;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--card-secondary-color, #4ecdc4);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
}

/* 学习模式 */
.learning-modes {
  display: flex;
  gap: 8px;
}

.mode-btn {
  flex: 1;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--card-text-color, #ffffff);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  transition: all 0.3s ease;
}

.mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.mode-btn.active {
  background: var(--card-primary-color, #ff6b6b);
  border-color: var(--card-primary-color, #ff6b6b);
}

.mode-btn i {
  font-size: 16px;
}

/* 互动区域 */
.interaction-area {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  min-height: 120px;
}

/* 故事模式 */
.story-content h5 {
  margin: 0 0 10px 0;
  color: var(--card-text-color, #ffffff);
  font-size: 14px;
}

.story-content p {
  margin: 0;
  color: var(--card-text-color, #ffffff);
  opacity: 0.9;
  font-size: 12px;
  line-height: 1.4;
}

.story-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.story-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: var(--card-primary-color, #ff6b6b);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.story-btn:hover {
  background: var(--card-secondary-color, #4ecdc4);
}

.play-btn {
  width: 40px;
  height: 40px;
}

/* 问答模式 */
.quiz-question h5 {
  margin: 0 0 15px 0;
  color: var(--card-text-color, #ffffff);
  font-size: 14px;
}

.quiz-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quiz-option {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: var(--card-text-color, #ffffff);
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.quiz-option:hover {
  background: rgba(255, 255, 255, 0.2);
}

.quiz-option.selected {
  background: var(--card-primary-color, #ff6b6b);
}

.quiz-option.correct {
  background: #2ecc71;
  border-color: #2ecc71;
}

.quiz-option.wrong {
  background: #e74c3c;
  border-color: #e74c3c;
}

/* 游戏模式 */
.game-content h5 {
  margin: 0 0 15px 0;
  color: var(--card-text-color, #ffffff);
  font-size: 14px;
  text-align: center;
}

.number-game {
  text-align: center;
}

.game-display {
  font-size: 32px;
  font-weight: bold;
  color: var(--card-primary-color, #ff6b6b);
  margin-bottom: 15px;
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.game-btn {
  padding: 6px 12px;
  background: var(--card-primary-color, #ff6b6b);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.game-btn:hover {
  background: var(--card-secondary-color, #4ecdc4);
}

/* 卡片底部 */
.card-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.achievement-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
}

.achievement-info i {
  color: #f1c40f;
}

.expand-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background: var(--card-primary-color, #ff6b6b);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.expand-btn:hover {
  background: var(--card-secondary-color, #4ecdc4);
}
</style>
