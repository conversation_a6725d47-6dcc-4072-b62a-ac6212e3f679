import LlmService from '@/services/LlmService'
import ColorExtractor from './ColorExtractor'

/**
 * AI驱动的智能配色分析器
 * 通过AI模型理解壁纸内容，生成符合主题且有良好对比度的卡片样式
 */
class AIColorAnalyzer {
  /**
   * 分析壁纸并生成智能配色方案
   * @param {string} imageUrl - 壁纸图片URL
   * @param {string} scenePrompt - 场景描述
   * @param {string} optimizedPrompt - LLM优化后的提示词
   * @returns {Promise<Object>} 智能配色方案
   */
  static async analyzeWallpaperAndGenerateColors(imageUrl, scenePrompt = '', optimizedPrompt = '') {
    try {
      console.log('🎨 开始AI智能配色分析...')
      
      // 1. 首先尝试传统颜色提取
      const extractedColors = await ColorExtractor.extractColors(imageUrl, scenePrompt)
      
      // 2. 使用AI分析壁纸内容和情感
      const aiAnalysis = await this.analyzeWallpaperContent(scenePrompt, optimizedPrompt)
      
      // 3. 结合AI分析和颜色提取生成智能配色
      const intelligentColors = await this.generateIntelligentColorScheme(
        extractedColors, 
        aiAnalysis, 
        scenePrompt
      )
      
      console.log('🎨 AI智能配色分析完成:', intelligentColors)
      return intelligentColors
      
    } catch (error) {
      console.error('AI配色分析失败:', error)
      // 降级到基础颜色提取
      return await ColorExtractor.extractColors(imageUrl, scenePrompt)
    }
  }

  /**
   * 使用AI分析壁纸内容和情感色彩
   */
  static async analyzeWallpaperContent(scenePrompt, optimizedPrompt) {
    const analysisPrompt = `
请分析以下壁纸场景的视觉特征和情感色彩，为UI设计提供配色建议：

场景描述: ${scenePrompt}
详细描述: ${optimizedPrompt}

请从以下维度分析并返回JSON格式：
{
  "mood": "情感氛围(如: 温暖、冷静、活力、神秘、优雅等)",
  "dominantColors": ["主要颜色1", "主要颜色2", "主要颜色3"],
  "brightness": "整体亮度(bright/medium/dark)",
  "contrast": "对比度需求(high/medium/low)",
  "atmosphere": "氛围描述",
  "cardStyleSuggestion": {
    "backgroundOpacity": "建议背景透明度(0.1-0.8)",
    "blurIntensity": "建议模糊强度(8-20px)",
    "borderStyle": "边框风格建议",
    "textContrast": "文字对比度建议"
  },
  "colorHarmony": "色彩和谐度分析"
}

请确保返回有效的JSON格式。`

    try {
      const llmService = new LlmService()
      const response = await llmService.generateResponse(analysisPrompt)
      
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
      
      // 如果无法解析JSON，返回默认分析
      return this.getDefaultAnalysis(scenePrompt)
      
    } catch (error) {
      console.error('AI内容分析失败:', error)
      return this.getDefaultAnalysis(scenePrompt)
    }
  }

  /**
   * 获取默认分析结果
   */
  static getDefaultAnalysis(scenePrompt) {
    const prompt = scenePrompt.toLowerCase()
    
    if (prompt.includes('夜') || prompt.includes('night')) {
      return {
        mood: '神秘',
        dominantColors: ['#2c3e50', '#34495e', '#9b59b6'],
        brightness: 'dark',
        contrast: 'high',
        atmosphere: '夜晚神秘氛围',
        cardStyleSuggestion: {
          backgroundOpacity: '0.6',
          blurIntensity: '15px',
          borderStyle: '亮色边框',
          textContrast: '高对比度白色文字'
        }
      }
    } else if (prompt.includes('阳光') || prompt.includes('bright') || prompt.includes('morning')) {
      return {
        mood: '活力',
        dominantColors: ['#f39c12', '#e67e22', '#3498db'],
        brightness: 'bright',
        contrast: 'medium',
        atmosphere: '明亮活力氛围',
        cardStyleSuggestion: {
          backgroundOpacity: '0.3',
          blurIntensity: '12px',
          borderStyle: '柔和边框',
          textContrast: '中等对比度深色文字'
        }
      }
    }
    
    return {
      mood: '平衡',
      dominantColors: ['#3498db', '#2ecc71', '#95a5a6'],
      brightness: 'medium',
      contrast: 'medium',
      atmosphere: '平衡舒适氛围',
      cardStyleSuggestion: {
        backgroundOpacity: '0.4',
        blurIntensity: '12px',
        borderStyle: '标准边框',
        textContrast: '标准对比度'
      }
    }
  }

  /**
   * 结合AI分析生成智能配色方案
   */
  static async generateIntelligentColorScheme(extractedColors, aiAnalysis, scenePrompt) {
    // 基于AI分析调整颜色方案
    const baseColors = extractedColors.isSceneBased ? 
      this.getAIEnhancedSceneColors(scenePrompt, aiAnalysis) : 
      extractedColors

    // 生成智能卡片样式
    const cardStyles = this.generateIntelligentCardStyles(baseColors, aiAnalysis)
    
    // 生成智能文字颜色
    const textColors = this.generateIntelligentTextColors(baseColors, aiAnalysis)
    
    // 生成智能按钮样式
    const buttonStyles = this.generateIntelligentButtonStyles(baseColors, aiAnalysis)

    return {
      ...baseColors,
      // AI增强的样式
      aiAnalysis: aiAnalysis,
      intelligentCardStyles: cardStyles,
      intelligentTextColors: textColors,
      intelligentButtonStyles: buttonStyles,
      // 覆盖原有样式
      glassBackground: cardStyles.background,
      glassBorder: cardStyles.border,
      cardTitleColor: textColors.title,
      cardContentColor: textColors.content,
      buttonBackground: buttonStyles.background,
      buttonColor: buttonStyles.color,
      buttonBorder: buttonStyles.border,
      buttonHoverBackground: buttonStyles.hoverBackground,
      // 标记为AI增强
      isAIEnhanced: true
    }
  }

  /**
   * 基于AI分析获取增强的场景颜色
   */
  static getAIEnhancedSceneColors(scenePrompt, aiAnalysis) {
    const dominantColors = aiAnalysis.dominantColors || ['#3498db', '#2ecc71', '#95a5a6']
    const primaryColorHex = dominantColors[0]
    // eslint-disable-next-line no-unused-vars
    const primaryColor = ColorExtractor.hexToRgb(primaryColorHex) || { r: 52, g: 152, b: 219 } // 保留用于未来扩展，提供默认值

    return {
      primary: primaryColorHex,
      secondary: dominantColors[1] || '#2ecc71',
      accent: dominantColors[2] || '#95a5a6',
      mood: aiAnalysis.mood,
      brightness: aiAnalysis.brightness,
      atmosphere: aiAnalysis.atmosphere,
      isSceneBased: true,
      isAIAnalyzed: true
    }
  }

  /**
   * 生成智能卡片样式
   */
  static generateIntelligentCardStyles(colors, aiAnalysis) {
    const suggestion = aiAnalysis.cardStyleSuggestion || {}
    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || { r: 52, g: 152, b: 219 }
    const opacity = parseFloat(suggestion.backgroundOpacity) || 0.4
    const blurIntensity = suggestion.blurIntensity || '12px'

    // 根据亮度和情感调整卡片背景
    let background, border
    
    if (aiAnalysis.brightness === 'dark') {
      // 暗色背景：使用亮色半透明卡片
      background = `rgba(${Math.min(255, primaryColor.r + 60)}, ${Math.min(255, primaryColor.g + 60)}, ${Math.min(255, primaryColor.b + 60)}, ${opacity + 0.2})`
      border = `1px solid rgba(255, 255, 255, 0.3)`
    } else if (aiAnalysis.brightness === 'bright') {
      // 亮色背景：使用深色半透明卡片
      background = `rgba(${Math.max(0, primaryColor.r - 40)}, ${Math.max(0, primaryColor.g - 40)}, ${Math.max(0, primaryColor.b - 40)}, ${opacity + 0.1})`
      border = `1px solid rgba(0, 0, 0, 0.2)`
    } else {
      // 中等亮度：平衡处理
      background = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, ${opacity})`
      border = `1px solid rgba(255, 255, 255, 0.25)`
    }

    return {
      background,
      border,
      backdropFilter: `blur(${blurIntensity})`,
      borderRadius: this.getIntelligentBorderRadius(aiAnalysis.mood),
      boxShadow: this.getIntelligentShadow(aiAnalysis.brightness, aiAnalysis.mood)
    }
  }

  /**
   * 生成智能文字颜色
   */
  static generateIntelligentTextColors(colors, aiAnalysis) {
    const contrast = aiAnalysis.contrast || 'medium'
    const brightness = aiAnalysis.brightness || 'medium'

    let titleColor, contentColor

    if (brightness === 'dark') {
      titleColor = contrast === 'high' ? '#ffffff' : '#f8f9fa'
      contentColor = contrast === 'high' ? '#e9ecef' : '#dee2e6'
    } else if (brightness === 'bright') {
      titleColor = contrast === 'high' ? '#212529' : '#343a40'
      contentColor = contrast === 'high' ? '#495057' : '#6c757d'
    } else {
      titleColor = '#ffffff'
      contentColor = '#e9ecef'
    }

    return {
      title: titleColor,
      content: contentColor,
      textShadow: brightness === 'dark' ? 
        '0 1px 3px rgba(0, 0, 0, 0.8)' : 
        '0 1px 3px rgba(255, 255, 255, 0.8)'
    }
  }

  /**
   * 生成智能按钮样式
   */
  static generateIntelligentButtonStyles(colors, aiAnalysis) {
    // eslint-disable-next-line no-unused-vars
    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || { r: 52, g: 152, b: 219 } // 用于计算按钮颜色，提供默认值
    const mood = aiAnalysis.mood || '平衡'
    const brightness = aiAnalysis.brightness || 'medium'

    // 根据情感调整按钮样式
    let buttonBg, buttonColor, buttonBorder, hoverBg

    if (mood.includes('活力') || mood.includes('温暖')) {
      // 活力/温暖：使用饱和度高的颜色
      buttonBg = `rgba(${Math.min(255, primaryColor.r + 20)}, ${Math.min(255, primaryColor.g + 20)}, ${Math.min(255, primaryColor.b + 20)}, 0.8)`
      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff'
      buttonBorder = '1px solid rgba(255, 255, 255, 0.6)'
      hoverBg = `rgba(${Math.min(255, primaryColor.r + 40)}, ${Math.min(255, primaryColor.g + 40)}, ${Math.min(255, primaryColor.b + 40)}, 0.9)`
    } else if (mood.includes('神秘') || mood.includes('优雅')) {
      // 神秘/优雅：使用深色调
      buttonBg = `rgba(${Math.max(0, primaryColor.r - 20)}, ${Math.max(0, primaryColor.g - 20)}, ${Math.max(0, primaryColor.b - 20)}, 0.7)`
      buttonColor = '#ffffff'
      buttonBorder = '1px solid rgba(255, 255, 255, 0.4)'
      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`
    } else {
      // 平衡：标准处理
      buttonBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.7)`
      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff'
      buttonBorder = '1px solid rgba(255, 255, 255, 0.5)'
      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`
    }

    return {
      background: buttonBg,
      color: buttonColor,
      border: buttonBorder,
      hoverBackground: hoverBg,
      textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)'
    }
  }

  /**
   * 获取智能圆角
   */
  static getIntelligentBorderRadius(mood) {
    if (mood.includes('现代') || mood.includes('商务')) return '8px'
    if (mood.includes('温暖') || mood.includes('舒适')) return '16px'
    if (mood.includes('优雅') || mood.includes('神秘')) return '12px'
    return '12px'
  }

  /**
   * 获取智能阴影
   */
  static getIntelligentShadow(brightness, mood) {
    let intensity = 0.1
    
    if (brightness === 'dark') intensity = 0.3
    else if (brightness === 'bright') intensity = 0.05
    
    if (mood.includes('神秘')) intensity += 0.1
    if (mood.includes('活力')) intensity -= 0.05
    
    return `0 8px 32px rgba(0, 0, 0, ${Math.max(0.05, Math.min(0.4, intensity))})`
  }
}

export default AIColorAnalyzer
