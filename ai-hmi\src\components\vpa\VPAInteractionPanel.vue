<template>
  <BaseCard
    :card-type="'vpa-interaction'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="vpaName"
    :icon="'fas fa-robot'"
    :closable="true"
    @close="handleClose"
    class="vpa-interaction-panel"
  >
    <div class="interaction-container" :class="[`size-${size}`, `state-${conversationState}`]">
      <!-- VPA头像区域 -->
      <div class="vpa-avatar-section">
        <div class="avatar-large">
          <img 
            v-if="currentAnimationResource"
            :src="currentAnimationResource"
            :alt="vpaName"
            class="avatar-animation"
          />
          <div v-else class="avatar-fallback">
            <i class="fas fa-robot"></i>
          </div>
        </div>
        
        <!-- VPA状态显示 -->
        <div class="vpa-status-display">
          <div class="status-text">{{ statusText }}</div>
          <div class="status-indicator" :class="statusClass"></div>
        </div>
      </div>

      <!-- 对话区域 -->
      <div class="conversation-area">
        <!-- 对话历史 -->
        <div class="conversation-history" ref="historyContainer">
          <div 
            v-for="(message, index) in conversationHistory" 
            :key="index"
            :class="['message', `message-${message.type}`]"
          >
            <div class="message-avatar">
              <i :class="message.type === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.text }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
          
          <!-- 正在输入指示器 -->
          <div v-if="isTyping" class="typing-indicator">
            <div class="typing-avatar">
              <i class="fas fa-robot"></i>
            </div>
            <div class="typing-animation">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <input 
              v-model="currentInput"
              type="text"
              placeholder="输入消息或语音指令..."
              class="text-input"
              @keypress.enter="sendMessage"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
              :disabled="isProcessing"
            />
            
            <div class="input-actions">
              <!-- 语音输入按钮 -->
              <button 
                @click="toggleVoiceInput"
                :class="['voice-btn', { active: isListening }]"
                :disabled="isProcessing"
              >
                <i :class="isListening ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
              </button>
              
              <!-- 发送按钮 -->
              <button 
                @click="sendMessage"
                class="send-btn"
                :disabled="!currentInput.trim() || isProcessing"
              >
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
          
          <!-- 语音输入状态 -->
          <div v-if="isListening" class="voice-input-status">
            <div class="voice-wave-large">
              <div class="wave-bar" v-for="i in 7" :key="i" :style="{ animationDelay: `${i * 0.1}s` }"></div>
            </div>
            <div class="voice-status-text">正在聆听...</div>
          </div>
        </div>
      </div>

      <!-- 快捷操作区域 -->
      <div class="quick-actions">
        <button 
          v-for="action in quickActions" 
          :key="action.id"
          @click="executeQuickAction(action)"
          class="quick-action-btn"
          :disabled="isProcessing"
        >
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { computed, ref, watch, nextTick, onMounted } from 'vue'
import { useVpaStore } from '@/store'
import BaseCard from '../BaseCard.vue'

export default {
  name: 'VPAInteractionPanel',
  components: {
    BaseCard
  },
  props: {
    // 组件尺寸
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['medium', 'large'].includes(value)
    },
    
    // 网格位置
    position: {
      type: Object,
      default: () => ({ x: 9, y: 1 })
    },
    
    // 主题
    theme: {
      type: String,
      default: 'glass'
    },
    
    // 主题颜色
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#4a90e2',
        secondary: '#7ed321',
        background: 'rgba(255, 255, 255, 0.1)',
        text: '#ffffff'
      })
    }
  },
  
  emits: ['close', 'message-sent', 'voice-input', 'action-executed'],
  
  setup(props, { emit }) {
    const vpaStore = useVpaStore()
    
    // 响应式状态
    const currentInput = ref('')
    const conversationHistory = ref([])
    const isListening = ref(false)
    const isTyping = ref(false)
    const isProcessing = ref(false)
    const historyContainer = ref(null)
    
    // 计算属性
    const vpaName = computed(() => vpaStore.personality.name)
    const animationState = computed(() => vpaStore.animationState)
    const conversationState = computed(() => 
      vpaStore.conversationState.isActive ? 'active' : 'idle'
    )
    
    const currentAnimationResource = computed(() => {
      const animationConfig = vpaStore.currentAnimationConfig
      if (animationConfig && animationConfig.resource) {
        return require(`@/assets/${animationConfig.resource}`)
      }
      return null
    })
    
    const statusClass = computed(() => ({
      'status-idle': animationState.value === 'idle',
      'status-active': animationState.value === 'talking' || animationState.value === 'listening',
      'status-thinking': animationState.value === 'thinking'
    }))
    
    const statusText = computed(() => {
      const statusMap = {
        idle: '等待您的指令',
        talking: '正在回复',
        listening: '正在聆听',
        thinking: '思考中...'
      }
      return statusMap[animationState.value] || '准备就绪'
    })
    
    // 快捷操作配置
    const quickActions = ref([
      { id: 'weather', label: '天气', icon: 'fas fa-cloud-sun' },
      { id: 'navigation', label: '导航', icon: 'fas fa-map-marked-alt' },
      { id: 'music', label: '音乐', icon: 'fas fa-music' },
      { id: 'settings', label: '设置', icon: 'fas fa-cog' }
    ])
    
    // 事件处理
    const handleClose = () => {
      // 切换回陪伴模式
      vpaStore.switchMode('companion')
      vpaStore.endConversation()
      emit('close')
    }
    
    const sendMessage = async () => {
      if (!currentInput.value.trim() || isProcessing.value) return
      
      const message = currentInput.value.trim()
      currentInput.value = ''
      
      // 添加用户消息到历史
      addMessage('user', message)
      
      // 开始处理
      isProcessing.value = true
      vpaStore.setAnimation('thinking')
      
      emit('message-sent', { message, timestamp: new Date() })
      
      // 模拟VPA回复
      setTimeout(() => {
        simulateVpaResponse(message)
      }, 1500)
    }
    
    const addMessage = (type, text) => {
      conversationHistory.value.push({
        type,
        text,
        timestamp: new Date()
      })
      
      // 滚动到底部
      nextTick(() => {
        if (historyContainer.value) {
          historyContainer.value.scrollTop = historyContainer.value.scrollHeight
        }
      })
    }
    
    const simulateVpaResponse = (userMessage) => {
      isTyping.value = true
      vpaStore.setAnimation('talking')
      
      setTimeout(() => {
        // 简单的回复逻辑
        let response = '我理解了您的需求，正在为您处理。'
        
        if (userMessage.includes('天气')) {
          response = '今天天气晴朗，温度适宜，适合出行。'
        } else if (userMessage.includes('导航') || userMessage.includes('路线')) {
          response = '已为您规划最优路线，预计行程时间25分钟。'
        } else if (userMessage.includes('音乐')) {
          response = '正在为您播放轻松的驾驶音乐。'
        }
        
        addMessage('vpa', response)
        isTyping.value = false
        isProcessing.value = false
        vpaStore.setAnimation('idle')
      }, 2000)
    }
    
    const toggleVoiceInput = () => {
      isListening.value = !isListening.value
      
      if (isListening.value) {
        vpaStore.setAnimation('listening')
        emit('voice-input', { action: 'start' })
      } else {
        vpaStore.setAnimation('idle')
        emit('voice-input', { action: 'stop' })
      }
    }
    
    const executeQuickAction = (action) => {
      if (isProcessing.value) return
      
      addMessage('user', `执行${action.label}操作`)
      emit('action-executed', action)
      
      // 模拟快捷操作回复
      setTimeout(() => {
        simulateVpaResponse(action.label)
      }, 500)
    }
    
    const handleInputFocus = () => {
      // 输入框获得焦点时的处理
    }
    
    const handleInputBlur = () => {
      // 输入框失去焦点时的处理
    }
    
    const formatTime = (timestamp) => {
      return timestamp.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
    
    // 组件挂载
    onMounted(() => {
      // 初始化欢迎消息
      addMessage('vpa', '您好！我是您的AI助手，有什么可以帮助您的吗？')
      
      // 确保VPA处于交互模式
      if (vpaStore.currentMode !== 'interaction') {
        vpaStore.switchMode('interaction')
      }
    })
    
    return {
      vpaName,
      animationState,
      conversationState,
      currentAnimationResource,
      statusClass,
      statusText,
      currentInput,
      conversationHistory,
      isListening,
      isTyping,
      isProcessing,
      historyContainer,
      quickActions,
      handleClose,
      sendMessage,
      toggleVoiceInput,
      executeQuickAction,
      handleInputFocus,
      handleInputBlur,
      formatTime
    }
  }
}
</script>

<style scoped>
.vpa-interaction-panel {
  height: 100%;
}

.interaction-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 15px;
}

/* VPA头像区域 */
.vpa-avatar-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-animation {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  font-size: 24px;
  color: var(--card-primary-color, #4a90e2);
}

.vpa-status-display {
  flex: 1;
}

.status-text {
  font-weight: 600;
  color: var(--card-text-color, #ffffff);
  margin-bottom: 5px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-idle { background: #95a5a6; }
.status-active { background: #2ecc71; animation: pulse 1.5s infinite; }
.status-thinking { background: #f39c12; animation: blink 1s infinite; }

/* 对话区域 */
.conversation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.conversation-history {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-bottom: 15px;
}

.message {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  align-items: flex-start;
}

.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--card-primary-color, #4a90e2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.message-user .message-avatar {
  background: var(--card-secondary-color, #7ed321);
}

.message-content {
  max-width: 70%;
}

.message-text {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 15px;
  border-radius: 15px;
  color: var(--card-text-color, #ffffff);
  line-height: 1.4;
}

.message-user .message-text {
  background: var(--card-secondary-color, #7ed321);
  color: white;
}

.message-time {
  font-size: 11px;
  opacity: 0.6;
  margin-top: 5px;
  text-align: right;
}

.message-user .message-time {
  text-align: left;
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  gap: 10px;
  align-items: center;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--card-primary-color, #4a90e2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.typing-animation {
  display: flex;
  gap: 3px;
  align-items: center;
}

.typing-animation span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--card-primary-color, #4a90e2);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(2) { animation-delay: 0.2s; }
.typing-animation span:nth-child(3) { animation-delay: 0.4s; }

/* 输入区域 */
.input-area {
  margin-top: auto;
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 5px;
}

.text-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 10px 15px;
  color: var(--card-text-color, #ffffff);
  font-size: 14px;
  outline: none;
}

.text-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input-actions {
  display: flex;
  gap: 5px;
}

.voice-btn, .send-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: var(--card-primary-color, #4a90e2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.voice-btn:hover, .send-btn:hover {
  background: var(--card-secondary-color, #7ed321);
}

.voice-btn.active {
  background: #e74c3c;
  animation: pulse 1s infinite;
}

.voice-btn:disabled, .send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 语音输入状态 */
.voice-input-status {
  margin-top: 10px;
  text-align: center;
}

.voice-wave-large {
  display: flex;
  justify-content: center;
  gap: 3px;
  margin-bottom: 5px;
}

.voice-wave-large .wave-bar {
  width: 4px;
  height: 15px;
  background: var(--card-primary-color, #4a90e2);
  border-radius: 2px;
  animation: wave 1s infinite ease-in-out;
}

.voice-status-text {
  font-size: 12px;
  opacity: 0.7;
  color: var(--card-text-color, #ffffff);
}

/* 快捷操作区域 */
.quick-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.quick-action-btn {
  flex: 1;
  min-width: 80px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--card-text-color, #ffffff);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-action-btn i {
  font-size: 16px;
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

@keyframes wave {
  0%, 100% { height: 15px; }
  50% { height: 25px; }
}
</style>
