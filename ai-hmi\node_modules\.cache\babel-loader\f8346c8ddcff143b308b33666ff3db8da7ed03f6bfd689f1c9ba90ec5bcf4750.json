{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"education-content\"\n};\nconst _hoisted_2 = {\n  class: \"current-lesson\"\n};\nconst _hoisted_3 = {\n  class: \"lesson-header\"\n};\nconst _hoisted_4 = {\n  class: \"lesson-icon\"\n};\nconst _hoisted_5 = {\n  class: \"lesson-info\"\n};\nconst _hoisted_6 = {\n  class: \"lesson-title\"\n};\nconst _hoisted_7 = {\n  class: \"lesson-description\"\n};\nconst _hoisted_8 = {\n  class: \"lesson-progress\"\n};\nconst _hoisted_9 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_10 = {\n  class: \"progress-text\"\n};\nconst _hoisted_11 = {\n  class: \"learning-modes\"\n};\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  class: \"interaction-area\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"story-mode\"\n};\nconst _hoisted_15 = {\n  class: \"story-content\"\n};\nconst _hoisted_16 = {\n  class: \"story-controls\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"quiz-mode\"\n};\nconst _hoisted_18 = {\n  class: \"quiz-question\"\n};\nconst _hoisted_19 = {\n  class: \"quiz-options\"\n};\nconst _hoisted_20 = [\"onClick\"];\nconst _hoisted_21 = {\n  key: 2,\n  class: \"game-mode\"\n};\nconst _hoisted_22 = {\n  class: \"game-content\"\n};\nconst _hoisted_23 = {\n  class: \"number-game\"\n};\nconst _hoisted_24 = {\n  class: \"game-display\"\n};\nconst _hoisted_25 = {\n  class: \"game-controls\"\n};\nconst _hoisted_26 = {\n  class: \"card-footer-content\"\n};\nconst _hoisted_27 = {\n  class: \"achievement-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": \"kidEducation\",\n    size: \"large\",\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    title: '儿童教育',\n    icon: 'fas fa-graduation-cap',\n    clickable: true,\n    \"show-footer\": true,\n    onClick: $setup.handleCardClick,\n    class: \"kid-education-card\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[10] || (_cache[10] = _createElementVNode(\"i\", {\n      class: \"fas fa-star\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, \"今日获得 \" + _toDisplayString($setup.todayStars) + \" 颗星\", 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      onClick: _cache[6] || (_cache[6] = (...args) => $setup.openFullEducation && $setup.openFullEducation(...args)),\n      class: \"expand-btn\"\n    }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n      class: \"fas fa-expand\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"展开\", -1 /* CACHED */)]))])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 当前学习内容 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.currentLesson.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h4\", _hoisted_6, _toDisplayString($setup.currentLesson.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_7, _toDisplayString($setup.currentLesson.description), 1 /* TEXT */)])]), _createCommentVNode(\" 学习进度 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: `${$setup.currentLesson.progress}%`\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.currentLesson.progress) + \"% 完成\", 1 /* TEXT */)])]), _createCommentVNode(\" 学习模式选择 \"), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.learningModes, mode => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: mode.id,\n        onClick: $event => $setup.selectLearningMode(mode),\n        class: _normalizeClass(['mode-btn', {\n          active: $setup.selectedMode === mode.id\n        }])\n      }, [_createElementVNode(\"i\", {\n        class: _normalizeClass(mode.icon)\n      }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(mode.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_12);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 互动区域 \"), _createElementVNode(\"div\", _hoisted_13, [$setup.selectedMode === 'story' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"h5\", null, _toDisplayString($setup.currentStory.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString($setup.currentStory.content), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.previousStory && $setup.previousStory(...args)),\n      class: \"story-btn\"\n    }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-backward\"\n    }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleStoryPlay && $setup.toggleStoryPlay(...args)),\n      class: \"story-btn play-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.isPlaying ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $setup.nextStory && $setup.nextStory(...args)),\n      class: \"story-btn\"\n    }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-forward\"\n    }, null, -1 /* CACHED */)]))])])) : $setup.selectedMode === 'quiz' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"h5\", null, _toDisplayString($setup.currentQuiz.question), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentQuiz.options, (option, index) => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: index,\n        onClick: $event => $setup.selectQuizOption(index),\n        class: _normalizeClass(['quiz-option', {\n          selected: $setup.selectedOption === index,\n          correct: $setup.showAnswer && index === $setup.currentQuiz.correct,\n          wrong: $setup.showAnswer && $setup.selectedOption === index && index !== $setup.currentQuiz.correct\n        }])\n      }, _toDisplayString(option), 11 /* TEXT, CLASS, PROPS */, _hoisted_20);\n    }), 128 /* KEYED_FRAGMENT */))])])])) : $setup.selectedMode === 'game' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[9] || (_cache[9] = _createElementVNode(\"h5\", null, \"数字游戏\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString($setup.gameNumber), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"button\", {\n      onClick: _cache[3] || (_cache[3] = $event => $setup.gameAction('add')),\n      class: \"game-btn\"\n    }, \"+1\"), _createElementVNode(\"button\", {\n      onClick: _cache[4] || (_cache[4] = $event => $setup.gameAction('subtract')),\n      class: \"game-btn\"\n    }, \"-1\"), _createElementVNode(\"button\", {\n      onClick: _cache[5] || (_cache[5] = $event => $setup.gameAction('reset')),\n      class: \"game-btn\"\n    }, \"重置\")])])])])) : _createCommentVNode(\"v-if\", true)])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"position\", \"theme\", \"theme-colors\", \"onClick\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "position", "$props", "theme", "themeColors", "title", "icon", "clickable", "onClick", "$setup", "handleCardClick", "footer", "_withCtx", "_createElementVNode", "_hoisted_26", "_hoisted_27", "_toDisplayString", "todayStars", "_cache", "args", "openFullEducation", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_normalizeClass", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_5", "_hoisted_6", "_hoisted_7", "description", "_hoisted_8", "_hoisted_9", "style", "_normalizeStyle", "width", "progress", "_hoisted_10", "_hoisted_11", "_createElementBlock", "_Fragment", "_renderList", "learningModes", "mode", "key", "id", "$event", "selectLearningMode", "active", "selectedMode", "name", "_hoisted_13", "_hoisted_14", "_hoisted_15", "currentStory", "content", "_hoisted_16", "previousStory", "toggleStoryPlay", "isPlaying", "nextStory", "_hoisted_17", "_hoisted_18", "currentQuiz", "question", "_hoisted_19", "options", "option", "index", "selectQuizOption", "selectedOption", "showAnswer", "correct", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "gameNumber", "_hoisted_25", "gameAction"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    card-type=\"kidEducation\"\n    size=\"large\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :title=\"'儿童教育'\"\n    :icon=\"'fas fa-graduation-cap'\"\n    :clickable=\"true\"\n    :show-footer=\"true\"\n    @click=\"handleCardClick\"\n    class=\"kid-education-card\"\n  >\n    <div class=\"education-content\">\n      <!-- 当前学习内容 -->\n      <div class=\"current-lesson\">\n        <div class=\"lesson-header\">\n          <div class=\"lesson-icon\">\n            <i :class=\"currentLesson.icon\"></i>\n          </div>\n          <div class=\"lesson-info\">\n            <h4 class=\"lesson-title\">{{ currentLesson.title }}</h4>\n            <p class=\"lesson-description\">{{ currentLesson.description }}</p>\n          </div>\n        </div>\n        \n        <!-- 学习进度 -->\n        <div class=\"lesson-progress\">\n          <div class=\"progress-bar\">\n            <div \n              class=\"progress-fill\" \n              :style=\"{ width: `${currentLesson.progress}%` }\"\n            ></div>\n          </div>\n          <span class=\"progress-text\">{{ currentLesson.progress }}% 完成</span>\n        </div>\n      </div>\n\n      <!-- 学习模式选择 -->\n      <div class=\"learning-modes\">\n        <button \n          v-for=\"mode in learningModes\" \n          :key=\"mode.id\"\n          @click=\"selectLearningMode(mode)\"\n          :class=\"['mode-btn', { active: selectedMode === mode.id }]\"\n        >\n          <i :class=\"mode.icon\"></i>\n          <span>{{ mode.name }}</span>\n        </button>\n      </div>\n\n      <!-- 互动区域 -->\n      <div class=\"interaction-area\">\n        <div v-if=\"selectedMode === 'story'\" class=\"story-mode\">\n          <div class=\"story-content\">\n            <h5>{{ currentStory.title }}</h5>\n            <p>{{ currentStory.content }}</p>\n          </div>\n          <div class=\"story-controls\">\n            <button @click=\"previousStory\" class=\"story-btn\">\n              <i class=\"fas fa-step-backward\"></i>\n            </button>\n            <button @click=\"toggleStoryPlay\" class=\"story-btn play-btn\">\n              <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n            </button>\n            <button @click=\"nextStory\" class=\"story-btn\">\n              <i class=\"fas fa-step-forward\"></i>\n            </button>\n          </div>\n        </div>\n\n        <div v-else-if=\"selectedMode === 'quiz'\" class=\"quiz-mode\">\n          <div class=\"quiz-question\">\n            <h5>{{ currentQuiz.question }}</h5>\n            <div class=\"quiz-options\">\n              <button \n                v-for=\"(option, index) in currentQuiz.options\" \n                :key=\"index\"\n                @click=\"selectQuizOption(index)\"\n                :class=\"['quiz-option', { \n                  selected: selectedOption === index,\n                  correct: showAnswer && index === currentQuiz.correct,\n                  wrong: showAnswer && selectedOption === index && index !== currentQuiz.correct\n                }]\"\n              >\n                {{ option }}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div v-else-if=\"selectedMode === 'game'\" class=\"game-mode\">\n          <div class=\"game-content\">\n            <h5>数字游戏</h5>\n            <div class=\"number-game\">\n              <div class=\"game-display\">{{ gameNumber }}</div>\n              <div class=\"game-controls\">\n                <button @click=\"gameAction('add')\" class=\"game-btn\">+1</button>\n                <button @click=\"gameAction('subtract')\" class=\"game-btn\">-1</button>\n                <button @click=\"gameAction('reset')\" class=\"game-btn\">重置</button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"card-footer-content\">\n        <div class=\"achievement-info\">\n          <i class=\"fas fa-star\"></i>\n          <span>今日获得 {{ todayStars }} 颗星</span>\n        </div>\n        <button @click=\"openFullEducation\" class=\"expand-btn\">\n          <i class=\"fas fa-expand\"></i>\n          <span>展开</span>\n        </button>\n      </div>\n    </template>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport BaseCard from '../BaseCard.vue'\n\nexport default {\n  name: 'KidEducationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 2 })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#ff6b6b',\n        secondary: '#4ecdc4',\n        background: 'rgba(255, 107, 107, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  \n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\n  \n  setup(props, { emit }) {\n    // 响应式状态\n    const selectedMode = ref('story')\n    const isPlaying = ref(false)\n    const selectedOption = ref(null)\n    const showAnswer = ref(false)\n    const gameNumber = ref(0)\n    const todayStars = ref(3)\n    \n    // 当前课程数据\n    const currentLesson = ref({\n      title: '认识动物',\n      description: '学习各种动物的名称和特征',\n      icon: 'fas fa-paw',\n      progress: 65\n    })\n    \n    // 学习模式\n    const learningModes = ref([\n      { id: 'story', name: '故事', icon: 'fas fa-book' },\n      { id: 'quiz', name: '问答', icon: 'fas fa-question-circle' },\n      { id: 'game', name: '游戏', icon: 'fas fa-gamepad' }\n    ])\n    \n    // 故事内容\n    const stories = ref([\n      {\n        title: '小兔子的冒险',\n        content: '从前有一只小兔子，它住在美丽的森林里...'\n      },\n      {\n        title: '勇敢的小狮子',\n        content: '在非洲大草原上，有一只勇敢的小狮子...'\n      }\n    ])\n    \n    const currentStoryIndex = ref(0)\n    const currentStory = computed(() => stories.value[currentStoryIndex.value])\n    \n    // 问答内容\n    const quizzes = ref([\n      {\n        question: '小兔子最喜欢吃什么？',\n        options: ['胡萝卜', '肉', '鱼', '草'],\n        correct: 0\n      },\n      {\n        question: '狮子是什么动物？',\n        options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\n        correct: 1\n      }\n    ])\n    \n    const currentQuizIndex = ref(0)\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value])\n    \n    // 事件处理\n    const handleCardClick = () => {\n      emit('card-click', 'kidEducation')\n    }\n    \n    const selectLearningMode = (mode) => {\n      selectedMode.value = mode.id\n      emit('mode-changed', mode.id)\n      \n      // 重置相关状态\n      if (mode.id === 'quiz') {\n        selectedOption.value = null\n        showAnswer.value = false\n      }\n    }\n    \n    const previousStory = () => {\n      if (currentStoryIndex.value > 0) {\n        currentStoryIndex.value--\n      }\n    }\n    \n    const nextStory = () => {\n      if (currentStoryIndex.value < stories.value.length - 1) {\n        currentStoryIndex.value++\n      }\n    }\n    \n    const toggleStoryPlay = () => {\n      isPlaying.value = !isPlaying.value\n      // 这里可以集成TTS服务来朗读故事\n    }\n    \n    const selectQuizOption = (index) => {\n      if (showAnswer.value) return\n      \n      selectedOption.value = index\n      showAnswer.value = true\n      \n      // 2秒后自动进入下一题\n      setTimeout(() => {\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\n          currentQuizIndex.value++\n          selectedOption.value = null\n          showAnswer.value = false\n        } else {\n          // 完成所有问题\n          emit('lesson-completed', 'quiz')\n        }\n      }, 2000)\n    }\n    \n    const gameAction = (action) => {\n      switch (action) {\n        case 'add':\n          gameNumber.value++\n          break\n        case 'subtract':\n          if (gameNumber.value > 0) {\n            gameNumber.value--\n          }\n          break\n        case 'reset':\n          gameNumber.value = 0\n          break\n      }\n    }\n    \n    const openFullEducation = () => {\n      // 打开完整的教育界面\n      console.log('打开完整教育界面')\n    }\n    \n    // 初始化\n    onMounted(() => {\n      // 可以在这里加载用户的学习进度\n      console.log('儿童教育卡片已加载')\n    })\n    \n    return {\n      selectedMode,\n      isPlaying,\n      selectedOption,\n      showAnswer,\n      gameNumber,\n      todayStars,\n      currentLesson,\n      learningModes,\n      currentStory,\n      currentQuiz,\n      handleCardClick,\n      selectLearningMode,\n      previousStory,\n      nextStory,\n      toggleStoryPlay,\n      selectQuizOption,\n      gameAction,\n      openFullEducation\n    }\n  }\n}\n</script>\n\n<style scoped>\n.kid-education-card {\n  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);\n}\n\n.education-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n}\n\n/* 当前课程 */\n.current-lesson {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 15px;\n}\n\n.lesson-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 10px;\n}\n\n.lesson-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: var(--card-primary-color, #ff6b6b);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n}\n\n.lesson-info h4 {\n  margin: 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 16px;\n}\n\n.lesson-info p {\n  margin: 5px 0 0 0;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n  font-size: 12px;\n}\n\n.lesson-progress {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.progress-bar {\n  flex: 1;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: var(--card-secondary-color, #4ecdc4);\n  border-radius: 3px;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n/* 学习模式 */\n.learning-modes {\n  display: flex;\n  gap: 8px;\n}\n\n.mode-btn {\n  flex: 1;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  transition: all 0.3s ease;\n}\n\n.mode-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.mode-btn.active {\n  background: var(--card-primary-color, #ff6b6b);\n  border-color: var(--card-primary-color, #ff6b6b);\n}\n\n.mode-btn i {\n  font-size: 16px;\n}\n\n/* 互动区域 */\n.interaction-area {\n  flex: 1;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 10px;\n  padding: 15px;\n  min-height: 120px;\n}\n\n/* 故事模式 */\n.story-content h5 {\n  margin: 0 0 10px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n}\n\n.story-content p {\n  margin: 0;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.9;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\n.story-controls {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 15px;\n}\n\n.story-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: var(--card-primary-color, #ff6b6b);\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.story-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n\n.play-btn {\n  width: 40px;\n  height: 40px;\n}\n\n/* 问答模式 */\n.quiz-question h5 {\n  margin: 0 0 15px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n}\n\n.quiz-options {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.quiz-option {\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 6px;\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  font-size: 11px;\n  transition: all 0.3s ease;\n}\n\n.quiz-option:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.quiz-option.selected {\n  background: var(--card-primary-color, #ff6b6b);\n}\n\n.quiz-option.correct {\n  background: #2ecc71;\n  border-color: #2ecc71;\n}\n\n.quiz-option.wrong {\n  background: #e74c3c;\n  border-color: #e74c3c;\n}\n\n/* 游戏模式 */\n.game-content h5 {\n  margin: 0 0 15px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n  text-align: center;\n}\n\n.number-game {\n  text-align: center;\n}\n\n.game-display {\n  font-size: 32px;\n  font-weight: bold;\n  color: var(--card-primary-color, #ff6b6b);\n  margin-bottom: 15px;\n}\n\n.game-controls {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n}\n\n.game-btn {\n  padding: 6px 12px;\n  background: var(--card-primary-color, #ff6b6b);\n  border: none;\n  border-radius: 6px;\n  color: white;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s ease;\n}\n\n.game-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n\n/* 卡片底部 */\n.card-footer-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.achievement-info {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n}\n\n.achievement-info i {\n  color: #f1c40f;\n}\n\n.expand-btn {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  padding: 6px 12px;\n  background: var(--card-primary-color, #ff6b6b);\n  border: none;\n  border-radius: 6px;\n  color: white;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s ease;\n}\n\n.expand-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n</style>\n"], "mappings": ";;EAcSA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAc;;EACrBA,KAAK,EAAC;AAAoB;;EAK5BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAMnBA,KAAK,EAAC;AAAe;;EAK1BA,KAAK,EAAC;AAAgB;;;EAatBA,KAAK,EAAC;AAAkB;;;EACUA,KAAK,EAAC;;;EACpCA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAgB;;;EAaYA,KAAK,EAAC;;;EACxCA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAc;;;;EAiBYA,KAAK,EAAC;;;EACxCA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAe;;EAY7BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;;uBA7GnCC,YAAA,CAuHWC,mBAAA;IAtHT,WAAS,EAAC,cAAc;IACxBC,IAAI,EAAC,OAAO;IACXC,QAAQ,EAAEC,MAAA,CAAAD,QAAQ;IAClBE,KAAK,EAAED,MAAA,CAAAC,KAAK;IACZ,cAAY,EAAED,MAAA,CAAAE,WAAW;IACzBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAE,IAAI;IACf,aAAW,EAAE,IAAI;IACjBC,OAAK,EAAEC,MAAA,CAAAC,eAAe;IACvBb,KAAK,EAAC;;IAgGKc,MAAM,EAAAC,QAAA,CACf,MASM,CATNC,mBAAA,CASM,OATNC,WASM,GARJD,mBAAA,CAGM,OAHNE,WAGM,G,4BAFJF,mBAAA,CAA2B;MAAxBhB,KAAK,EAAC;IAAa,4BACtBgB,mBAAA,CAAqC,cAA/B,OAAK,GAAAG,gBAAA,CAAGP,MAAA,CAAAQ,UAAU,IAAG,KAAG,gB,GAEhCJ,mBAAA,CAGS;MAHAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAW,iBAAA,IAAAX,MAAA,CAAAW,iBAAA,IAAAD,IAAA,CAAiB;MAAEtB,KAAK,EAAC;oCACvCgB,mBAAA,CAA6B;MAA1BhB,KAAK,EAAC;IAAe,2BACxBgB,mBAAA,CAAe,cAAT,IAAE,mB;sBAtGd,MA4FM,CA5FNA,mBAAA,CA4FM,OA5FNQ,UA4FM,GA3FJC,mBAAA,YAAe,EACfT,mBAAA,CAqBM,OArBNU,UAqBM,GApBJV,mBAAA,CAQM,OARNW,UAQM,GAPJX,mBAAA,CAEM,OAFNY,UAEM,GADJZ,mBAAA,CAAmC;MAA/BhB,KAAK,EAAA6B,eAAA,CAAEjB,MAAA,CAAAkB,aAAa,CAACrB,IAAI;+BAE/BO,mBAAA,CAGM,OAHNe,UAGM,GAFJf,mBAAA,CAAuD,MAAvDgB,UAAuD,EAAAb,gBAAA,CAA3BP,MAAA,CAAAkB,aAAa,CAACtB,KAAK,kBAC/CQ,mBAAA,CAAiE,KAAjEiB,UAAiE,EAAAd,gBAAA,CAAhCP,MAAA,CAAAkB,aAAa,CAACI,WAAW,iB,KAI9DT,mBAAA,UAAa,EACbT,mBAAA,CAQM,OARNmB,UAQM,GAPJnB,mBAAA,CAKM,OALNoB,UAKM,GAJJpB,mBAAA,CAGO;MAFLhB,KAAK,EAAC,eAAe;MACpBqC,KAAK,EAAAC,eAAA;QAAAC,KAAA,KAAc3B,MAAA,CAAAkB,aAAa,CAACU,QAAQ;MAAA;+BAG9CxB,mBAAA,CAAmE,QAAnEyB,WAAmE,EAAAtB,gBAAA,CAApCP,MAAA,CAAAkB,aAAa,CAACU,QAAQ,IAAG,MAAI,gB,KAIhEf,mBAAA,YAAe,EACfT,mBAAA,CAUM,OAVN0B,WAUM,I,kBATJC,mBAAA,CAQSC,SAAA,QAAAC,WAAA,CAPQjC,MAAA,CAAAkC,aAAa,EAArBC,IAAI;2BADbJ,mBAAA,CAQS;QANNK,GAAG,EAAED,IAAI,CAACE,EAAE;QACZtC,OAAK,EAAAuC,MAAA,IAAEtC,MAAA,CAAAuC,kBAAkB,CAACJ,IAAI;QAC9B/C,KAAK,EAAA6B,eAAA;UAAAuB,MAAA,EAAyBxC,MAAA,CAAAyC,YAAY,KAAKN,IAAI,CAACE;QAAE;UAEvDjC,mBAAA,CAA0B;QAAtBhB,KAAK,EAAA6B,eAAA,CAAEkB,IAAI,CAACtC,IAAI;+BACpBO,mBAAA,CAA4B,cAAAG,gBAAA,CAAnB4B,IAAI,CAACO,IAAI,iB;sCAItB7B,mBAAA,UAAa,EACbT,mBAAA,CAoDM,OApDNuC,WAoDM,GAnDO3C,MAAA,CAAAyC,YAAY,gB,cAAvBV,mBAAA,CAgBM,OAhBNa,WAgBM,GAfJxC,mBAAA,CAGM,OAHNyC,WAGM,GAFJzC,mBAAA,CAAiC,YAAAG,gBAAA,CAA1BP,MAAA,CAAA8C,YAAY,CAAClD,KAAK,kBACzBQ,mBAAA,CAAiC,WAAAG,gBAAA,CAA3BP,MAAA,CAAA8C,YAAY,CAACC,OAAO,iB,GAE5B3C,mBAAA,CAUM,OAVN4C,WAUM,GATJ5C,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAiD,aAAA,IAAAjD,MAAA,CAAAiD,aAAA,IAAAvC,IAAA,CAAa;MAAEtB,KAAK,EAAC;kCACnCgB,mBAAA,CAAoC;MAAjChB,KAAK,EAAC;IAAsB,0B,IAEjCgB,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAkD,eAAA,IAAAlD,MAAA,CAAAkD,eAAA,IAAAxC,IAAA,CAAe;MAAEtB,KAAK,EAAC;QACrCgB,mBAAA,CAA2D;MAAvDhB,KAAK,EAAA6B,eAAA,CAAEjB,MAAA,CAAAmD,SAAS;+BAEtB/C,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAoD,SAAA,IAAApD,MAAA,CAAAoD,SAAA,IAAA1C,IAAA,CAAS;MAAEtB,KAAK,EAAC;kCAC/BgB,mBAAA,CAAmC;MAAhChB,KAAK,EAAC;IAAqB,0B,UAKpBY,MAAA,CAAAyC,YAAY,e,cAA5BV,mBAAA,CAkBM,OAlBNsB,WAkBM,GAjBJjD,mBAAA,CAgBM,OAhBNkD,WAgBM,GAfJlD,mBAAA,CAAmC,YAAAG,gBAAA,CAA5BP,MAAA,CAAAuD,WAAW,CAACC,QAAQ,kBAC3BpD,mBAAA,CAaM,OAbNqD,WAaM,I,kBAZJ1B,mBAAA,CAWSC,SAAA,QAAAC,WAAA,CAVmBjC,MAAA,CAAAuD,WAAW,CAACG,OAAO,GAArCC,MAAM,EAAEC,KAAK;2BADvB7B,mBAAA,CAWS;QATNK,GAAG,EAAEwB,KAAK;QACV7D,OAAK,EAAAuC,MAAA,IAAEtC,MAAA,CAAA6D,gBAAgB,CAACD,KAAK;QAC7BxE,KAAK,EAAA6B,eAAA;oBAAiDjB,MAAA,CAAA8D,cAAc,KAAKF,KAAK;mBAA6B5D,MAAA,CAAA+D,UAAU,IAAIH,KAAK,KAAK5D,MAAA,CAAAuD,WAAW,CAACS,OAAO;iBAA2BhE,MAAA,CAAA+D,UAAU,IAAI/D,MAAA,CAAA8D,cAAc,KAAKF,KAAK,IAAIA,KAAK,KAAK5D,MAAA,CAAAuD,WAAW,CAACS;;0BAM/OL,MAAM,gCAAAM,WAAA;4CAMDjE,MAAA,CAAAyC,YAAY,e,cAA5BV,mBAAA,CAYM,OAZNmC,WAYM,GAXJ9D,mBAAA,CAUM,OAVN+D,WAUM,G,0BATJ/D,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAOM,OAPNgE,WAOM,GANJhE,mBAAA,CAAgD,OAAhDiE,WAAgD,EAAA9D,gBAAA,CAAnBP,MAAA,CAAAsE,UAAU,kBACvClE,mBAAA,CAIM,OAJNmE,WAIM,GAHJnE,mBAAA,CAA+D;MAAtDL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAASpF,KAAK,EAAC;OAAW,IAAE,GACtDgB,mBAAA,CAAoE;MAA3DL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAAcpF,KAAK,EAAC;OAAW,IAAE,GAC3DgB,mBAAA,CAAiE;MAAxDL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAAWpF,KAAK,EAAC;OAAW,IAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}