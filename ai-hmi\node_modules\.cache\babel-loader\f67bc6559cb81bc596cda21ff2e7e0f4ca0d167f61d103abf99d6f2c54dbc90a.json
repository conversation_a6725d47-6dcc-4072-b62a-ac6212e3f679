{"ast": null, "code": "import { computed, ref } from 'vue';\nimport { useVPAStore } from '@/store/modules/vpa';\nexport default {\n  name: 'VPAAvatarWidget',\n  props: {\n    // 组件尺寸：2x2, 2x4, 3x3\n    size: {\n      type: String,\n      default: '2x2',\n      validator: value => ['2x2', '2x4', '3x3'].includes(value)\n    },\n    // 是否可交互\n    interactive: {\n      type: Boolean,\n      default: true\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['click', 'mode-change', 'interaction'],\n  setup(props, {\n    emit\n  }) {\n    const vpaStore = useVPAStore();\n    const imageError = ref(false);\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      };\n      return expressions[vpaStore.currentExpression] || expressions.neutral;\n    });\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴',\n        'interactive': '交互',\n        'restricted': '访客'\n      };\n      return modes[vpaStore.currentMode] || '未知';\n    });\n    const shortGreeting = computed(() => {\n      const greeting = vpaStore.contextualGreeting;\n      // 根据尺寸截断问候语\n      const maxLength = props.size === '2x2' ? 10 : props.size === '2x4' ? 20 : 30;\n      return greeting.length > maxLength ? greeting.substring(0, maxLength) + '...' : greeting;\n    });\n    const showInfo = computed(() => {\n      // 只在较大尺寸时显示信息\n      return ['2x4', '3x3'].includes(props.size);\n    });\n    const widgetStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position);\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      };\n    });\n\n    // 方法\n    const handleClick = () => {\n      if (!props.interactive || !vpaStore.isActive) return;\n      emit('click', {\n        mode: vpaStore.currentMode,\n        size: props.size,\n        position: props.position\n      });\n\n      // 根据当前模式执行不同操作\n      if (vpaStore.currentMode === 'companion') {\n        vpaStore.switchMode('interactive');\n        emit('mode-change', 'interactive');\n      } else {\n        vpaStore.startConversation();\n        emit('interaction', 'conversation-start');\n      }\n    };\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation();\n      } else {\n        vpaStore.startConversation();\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start');\n    };\n    const handleImageError = () => {\n      imageError.value = true;\n      console.warn('VPA avatar image failed to load');\n    };\n    return {\n      vpaStore,\n      imageError,\n      expressionEmoji,\n      modeText,\n      shortGreeting,\n      showInfo,\n      widgetStyles,\n      handleClick,\n      toggleListening,\n      handleImageError\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "useVPAStore", "name", "props", "size", "type", "String", "default", "validator", "value", "includes", "interactive", "Boolean", "position", "Object", "x", "y", "customStyles", "emits", "setup", "emit", "vpaStore", "imageError", "expressionEmoji", "expressions", "currentExpression", "neutral", "modeText", "modes", "currentMode", "shortGreeting", "greeting", "contextualGreeting", "max<PERSON><PERSON><PERSON>", "length", "substring", "showInfo", "widgetStyles", "gridPosition", "calculateGridPosition", "handleClick", "isActive", "mode", "switchMode", "startConversation", "toggleListening", "isListening", "endConversation", "handleImageError", "console", "warn"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-avatar-widget\"\n    :class=\"[\n      `size-${size}`,\n      `mode-${vpaStore.currentMode}`,\n      `expression-${vpaStore.currentExpression}`,\n      { 'active': vpaStore.isActive, 'interactive': interactive }\n    ]\"\n    :style=\"widgetStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 背景光环 -->\n    <div class=\"widget-aura\" :class=\"{ 'pulsing': vpaStore.isListening || vpaStore.isSpeaking }\"></div>\n    \n    <!-- 主要内容区 -->\n    <div class=\"widget-content\">\n      <!-- VPA头像 -->\n      <div class=\"avatar-container\">\n        <img \n          :src=\"`/docs/${vpaStore.avatar}`\" \n          :alt=\"`VPA ${vpaStore.currentExpression}`\"\n          class=\"avatar-image\"\n          @error=\"handleImageError\"\n        />\n        \n        <!-- 表情指示器 -->\n        <div class=\"expression-indicator\">\n          {{ expressionEmoji }}\n        </div>\n        \n        <!-- 状态指示器 -->\n        <div v-if=\"vpaStore.isListening\" class=\"status-indicator listening\">\n          <div class=\"pulse-ring\"></div>\n          🎤\n        </div>\n        <div v-else-if=\"vpaStore.isSpeaking\" class=\"status-indicator speaking\">\n          <div class=\"wave-animation\"></div>\n          🔊\n        </div>\n      </div>\n      \n      <!-- 信息显示（仅在较大尺寸时显示） -->\n      <div v-if=\"showInfo\" class=\"widget-info\">\n        <div class=\"greeting-text\">{{ shortGreeting }}</div>\n        <div class=\"status-badges\">\n          <span class=\"mode-badge\">{{ modeText }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 快速操作（仅在交互模式下显示） -->\n    <div v-if=\"interactive && vpaStore.isActive\" class=\"quick-actions\">\n      <button \n        class=\"action-btn\"\n        @click.stop=\"toggleListening\"\n        :class=\"{ active: vpaStore.isListening }\"\n      >\n        {{ vpaStore.isListening ? '⏹️' : '🎤' }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPAAvatarWidget',\n  props: {\n    // 组件尺寸：2x2, 2x4, 3x3\n    size: {\n      type: String,\n      default: '2x2',\n      validator: value => ['2x2', '2x4', '3x3'].includes(value)\n    },\n    // 是否可交互\n    interactive: {\n      type: Boolean,\n      default: true\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['click', 'mode-change', 'interaction'],\n  setup(props, { emit }) {\n    const vpaStore = useVPAStore()\n    const imageError = ref(false)\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[vpaStore.currentExpression] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴',\n        'interactive': '交互',\n        'restricted': '访客'\n      }\n      return modes[vpaStore.currentMode] || '未知'\n    })\n\n    const shortGreeting = computed(() => {\n      const greeting = vpaStore.contextualGreeting\n      // 根据尺寸截断问候语\n      const maxLength = props.size === '2x2' ? 10 : props.size === '2x4' ? 20 : 30\n      return greeting.length > maxLength ? greeting.substring(0, maxLength) + '...' : greeting\n    })\n\n    const showInfo = computed(() => {\n      // 只在较大尺寸时显示信息\n      return ['2x4', '3x3'].includes(props.size)\n    })\n\n    const widgetStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position)\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      }\n    })\n\n    // 方法\n    const handleClick = () => {\n      if (!props.interactive || !vpaStore.isActive) return\n      \n      emit('click', {\n        mode: vpaStore.currentMode,\n        size: props.size,\n        position: props.position\n      })\n      \n      // 根据当前模式执行不同操作\n      if (vpaStore.currentMode === 'companion') {\n        vpaStore.switchMode('interactive')\n        emit('mode-change', 'interactive')\n      } else {\n        vpaStore.startConversation()\n        emit('interaction', 'conversation-start')\n      }\n    }\n\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start')\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    return {\n      vpaStore,\n      imageError,\n      expressionEmoji,\n      modeText,\n      shortGreeting,\n      showInfo,\n      widgetStyles,\n      handleClick,\n      toggleListening,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-avatar-widget {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.vpa-avatar-widget:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.vpa-avatar-widget.interactive:hover {\n  border-color: rgba(74, 144, 226, 0.5);\n}\n\n/* 背景光环 */\n.widget-aura {\n  position: absolute;\n  top: -5px;\n  left: -5px;\n  right: -5px;\n  bottom: -5px;\n  border-radius: 20px;\n  background: radial-gradient(\n    circle,\n    rgba(74, 144, 226, 0.2) 0%,\n    rgba(74, 144, 226, 0.05) 50%,\n    transparent 100%\n  );\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.widget-aura.pulsing {\n  opacity: 1;\n  animation: auraPulse 2s ease-in-out infinite;\n}\n\n@keyframes auraPulse {\n  0%, 100% { transform: scale(1); opacity: 0.2; }\n  50% { transform: scale(1.05); opacity: 0.4; }\n}\n\n/* 主要内容区 */\n.widget-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  padding: 8px;\n  gap: 8px;\n}\n\n/* 头像容器 */\n.avatar-container {\n  position: relative;\n  flex-shrink: 0;\n}\n\n/* 不同尺寸的头像大小 */\n.size-2x2 .avatar-container {\n  width: 40px;\n  height: 40px;\n}\n\n.size-2x4 .avatar-container {\n  width: 50px;\n  height: 50px;\n}\n\n.size-3x3 .avatar-container {\n  width: 60px;\n  height: 60px;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n  transition: all 0.3s ease;\n}\n\n.expression-indicator {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 16px;\n  height: 16px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 8px;\n}\n\n/* 状态指示器 */\n.status-indicator {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: white;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 1px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.2); }\n}\n\n/* 信息显示 */\n.widget-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  min-height: 0;\n}\n\n.greeting-text {\n  color: white;\n  font-size: 11px;\n  line-height: 1.2;\n  margin-bottom: 4px;\n  word-break: break-all;\n}\n\n.status-badges {\n  display: flex;\n  gap: 4px;\n}\n\n.mode-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 8px;\n  padding: 2px 4px;\n  border-radius: 8px;\n}\n\n/* 快速操作 */\n.quick-actions {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n}\n\n.action-btn {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(5px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.action-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n.action-btn.active {\n  background: rgba(76, 175, 80, 0.8);\n}\n\n/* 模式样式 */\n.mode-companion .avatar-image {\n  border: 2px solid rgba(76, 175, 80, 0.5);\n}\n\n.mode-interactive .avatar-image {\n  border: 2px solid rgba(33, 150, 243, 0.5);\n}\n\n.mode-restricted .avatar-image {\n  border: 2px solid rgba(158, 158, 158, 0.5);\n  opacity: 0.6;\n}\n\n/* 表情动画 */\n.expression-happy .avatar-image {\n  filter: brightness(1.1) saturate(1.2);\n}\n\n.expression-thinking .avatar-image {\n  filter: grayscale(0.3);\n}\n\n.expression-excited .avatar-image {\n  filter: brightness(1.2) saturate(1.3);\n  animation: excitedBounce 1s ease-in-out infinite;\n}\n\n@keyframes excitedBounce {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n/* 响应式适配 */\n@media (max-width: 768px) {\n  .size-2x2 .avatar-container {\n    width: 35px;\n    height: 35px;\n  }\n  \n  .size-2x4 .avatar-container {\n    width: 45px;\n    height: 45px;\n  }\n  \n  .size-3x3 .avatar-container {\n    width: 55px;\n    height: 55px;\n  }\n  \n  .greeting-text {\n    font-size: 10px;\n  }\n  \n  .mode-badge {\n    font-size: 7px;\n    padding: 1px 3px;\n  }\n}\n</style>\n"], "mappings": "AAiEA,SAASA,QAAQ,EAAEC,GAAE,QAAS,KAAI;AAClC,SAASC,WAAU,QAAS,qBAAoB;AAEhD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE;IACL;IACAC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAEC,KAAI,IAAK,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,KAAK;IAC1D,CAAC;IACD;IACAE,WAAW,EAAE;MACXN,IAAI,EAAEO,OAAO;MACbL,OAAO,EAAE;IACX,CAAC;IACD;IACAM,QAAQ,EAAE;MACRR,IAAI,EAAES,MAAM;MACZP,OAAO,EAAEA,CAAA,MAAO;QAAEQ,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACD;IACAC,YAAY,EAAE;MACZZ,IAAI,EAAES,MAAM;MACZP,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EACDW,KAAK,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC;EAC9CC,KAAKA,CAAChB,KAAK,EAAE;IAAEiB;EAAK,CAAC,EAAE;IACrB,MAAMC,QAAO,GAAIpB,WAAW,CAAC;IAC7B,MAAMqB,UAAS,GAAItB,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMuB,eAAc,GAAIxB,QAAQ,CAAC,MAAM;MACrC,MAAMyB,WAAU,GAAI;QAClB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE;MACd;MACA,OAAOA,WAAW,CAACH,QAAQ,CAACI,iBAAiB,KAAKD,WAAW,CAACE,OAAM;IACtE,CAAC;IAED,MAAMC,QAAO,GAAI5B,QAAQ,CAAC,MAAM;MAC9B,MAAM6B,KAAI,GAAI;QACZ,WAAW,EAAE,IAAI;QACjB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE;MAChB;MACA,OAAOA,KAAK,CAACP,QAAQ,CAACQ,WAAW,KAAK,IAAG;IAC3C,CAAC;IAED,MAAMC,aAAY,GAAI/B,QAAQ,CAAC,MAAM;MACnC,MAAMgC,QAAO,GAAIV,QAAQ,CAACW,kBAAiB;MAC3C;MACA,MAAMC,SAAQ,GAAI9B,KAAK,CAACC,IAAG,KAAM,KAAI,GAAI,EAAC,GAAID,KAAK,CAACC,IAAG,KAAM,KAAI,GAAI,EAAC,GAAI,EAAC;MAC3E,OAAO2B,QAAQ,CAACG,MAAK,GAAID,SAAQ,GAAIF,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAEF,SAAS,IAAI,KAAI,GAAIF,QAAO;IACzF,CAAC;IAED,MAAMK,QAAO,GAAIrC,QAAQ,CAAC,MAAM;MAC9B;MACA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACW,QAAQ,CAACP,KAAK,CAACC,IAAI;IAC3C,CAAC;IAED,MAAMiC,YAAW,GAAItC,QAAQ,CAAC,MAAM;MAClC,MAAMuC,YAAW,GAAIjB,QAAQ,CAACkB,qBAAqB,CAACpC,KAAK,CAACC,IAAI,EAAED,KAAK,CAACU,QAAQ;MAC9E,OAAO;QACL,GAAGyB,YAAY;QACf,GAAGnC,KAAK,CAACc;MACX;IACF,CAAC;;IAED;IACA,MAAMuB,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,CAACrC,KAAK,CAACQ,WAAU,IAAK,CAACU,QAAQ,CAACoB,QAAQ,EAAE;MAE9CrB,IAAI,CAAC,OAAO,EAAE;QACZsB,IAAI,EAAErB,QAAQ,CAACQ,WAAW;QAC1BzB,IAAI,EAAED,KAAK,CAACC,IAAI;QAChBS,QAAQ,EAAEV,KAAK,CAACU;MAClB,CAAC;;MAED;MACA,IAAIQ,QAAQ,CAACQ,WAAU,KAAM,WAAW,EAAE;QACxCR,QAAQ,CAACsB,UAAU,CAAC,aAAa;QACjCvB,IAAI,CAAC,aAAa,EAAE,aAAa;MACnC,OAAO;QACLC,QAAQ,CAACuB,iBAAiB,CAAC;QAC3BxB,IAAI,CAAC,aAAa,EAAE,oBAAoB;MAC1C;IACF;IAEA,MAAMyB,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAIxB,QAAQ,CAACyB,WAAW,EAAE;QACxBzB,QAAQ,CAAC0B,eAAe,CAAC;MAC3B,OAAO;QACL1B,QAAQ,CAACuB,iBAAiB,CAAC;MAC7B;MACAxB,IAAI,CAAC,aAAa,EAAEC,QAAQ,CAACyB,WAAU,GAAI,gBAAe,GAAI,iBAAiB;IACjF;IAEA,MAAME,gBAAe,GAAIA,CAAA,KAAM;MAC7B1B,UAAU,CAACb,KAAI,GAAI,IAAG;MACtBwC,OAAO,CAACC,IAAI,CAAC,iCAAiC;IAChD;IAEA,OAAO;MACL7B,QAAQ;MACRC,UAAU;MACVC,eAAe;MACfI,QAAQ;MACRG,aAAa;MACbM,QAAQ;MACRC,YAAY;MACZG,WAAW;MACXK,eAAe;MACfG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}