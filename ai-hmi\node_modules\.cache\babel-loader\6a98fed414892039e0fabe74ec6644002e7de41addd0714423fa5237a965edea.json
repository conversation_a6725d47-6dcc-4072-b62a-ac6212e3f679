{"ast": null, "code": "class AsrService {\n  constructor() {\n    // 使用系统自带的ASR服务\n    this.recognition = null;\n    this.isSupported = 'webkitSpeechRecognition' in window;\n  }\n  initRecognition() {\n    if (!this.isSupported) {\n      console.warn('浏览器不支持语音识别');\n      return null;\n    }\n\n    // eslint-disable-next-line no-undef\n    this.recognition = new webkitSpeechRecognition();\n    this.recognition.continuous = false;\n    this.recognition.interimResults = true;\n    this.recognition.lang = 'zh-CN';\n    return this.recognition;\n  }\n  async startRecognition() {\n    return new Promise((resolve, reject) => {\n      if (!this.isSupported) {\n        reject(new Error('浏览器不支持语音识别'));\n        return;\n      }\n      const recognition = this.initRecognition();\n      recognition.onstart = () => {\n        console.log('语音识别开始');\n      };\n      recognition.onresult = event => {\n        const last = event.results.length - 1;\n        const transcript = event.results[last][0].transcript;\n        if (event.results[last].isFinal) {\n          resolve(transcript.trim());\n        }\n      };\n      recognition.onerror = event => {\n        console.error('语音识别错误:', event.error);\n        reject(new Error(`语音识别失败: ${event.error}`));\n      };\n      recognition.onend = () => {\n        if (recognition.finalResult === undefined) {\n          reject(new Error('语音识别超时'));\n        }\n      };\n      recognition.start();\n    });\n  }\n  stopRecognition() {\n    if (this.recognition) {\n      this.recognition.stop();\n    }\n  }\n}\nexport default AsrService;", "map": {"version": 3, "names": ["AsrService", "constructor", "recognition", "isSupported", "window", "initRecognition", "console", "warn", "webkitSpeechRecognition", "continuous", "interimResults", "lang", "startRecognition", "Promise", "resolve", "reject", "Error", "onstart", "log", "on<PERSON>ult", "event", "last", "results", "length", "transcript", "isFinal", "trim", "onerror", "error", "onend", "finalResult", "undefined", "start", "stopRecognition", "stop"], "sources": ["F:/工作/theme/ai-hmi/src/services/AsrService.js"], "sourcesContent": ["class AsrService {\r\n  constructor() {\r\n    // 使用系统自带的ASR服务\r\n    this.recognition = null\r\n    this.isSupported = 'webkitSpeechRecognition' in window\r\n  }\r\n\r\n  initRecognition() {\r\n    if (!this.isSupported) {\r\n      console.warn('浏览器不支持语音识别')\r\n      return null\r\n    }\r\n\r\n    // eslint-disable-next-line no-undef\r\n    this.recognition = new webkitSpeechRecognition()\r\n    this.recognition.continuous = false\r\n    this.recognition.interimResults = true\r\n    this.recognition.lang = 'zh-CN'\r\n\r\n    return this.recognition\r\n  }\r\n\r\n  async startRecognition() {\r\n    return new Promise((resolve, reject) => {\r\n      if (!this.isSupported) {\r\n        reject(new Error('浏览器不支持语音识别'))\r\n        return\r\n      }\r\n\r\n      const recognition = this.initRecognition()\r\n      \r\n      recognition.onstart = () => {\r\n        console.log('语音识别开始')\r\n      }\r\n\r\n      recognition.onresult = (event) => {\r\n        const last = event.results.length - 1\r\n        const transcript = event.results[last][0].transcript\r\n        \r\n        if (event.results[last].isFinal) {\r\n          resolve(transcript.trim())\r\n        }\r\n      }\r\n\r\n      recognition.onerror = (event) => {\r\n        console.error('语音识别错误:', event.error)\r\n        reject(new Error(`语音识别失败: ${event.error}`))\r\n      }\r\n\r\n      recognition.onend = () => {\r\n        if (recognition.finalResult === undefined) {\r\n          reject(new Error('语音识别超时'))\r\n        }\r\n      }\r\n\r\n      recognition.start()\r\n    })\r\n  }\r\n\r\n  stopRecognition() {\r\n    if (this.recognition) {\r\n      this.recognition.stop()\r\n    }\r\n  }\r\n}\r\n\r\nexport default AsrService"], "mappings": "AAAA,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,yBAAyB,IAAIC,MAAM;EACxD;EAEAC,eAAeA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;MACrBG,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;MAC1B,OAAO,IAAI;IACb;;IAEA;IACA,IAAI,CAACL,WAAW,GAAG,IAAIM,uBAAuB,CAAC,CAAC;IAChD,IAAI,CAACN,WAAW,CAACO,UAAU,GAAG,KAAK;IACnC,IAAI,CAACP,WAAW,CAACQ,cAAc,GAAG,IAAI;IACtC,IAAI,CAACR,WAAW,CAACS,IAAI,GAAG,OAAO;IAE/B,OAAO,IAAI,CAACT,WAAW;EACzB;EAEA,MAAMU,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI,CAAC,IAAI,CAACZ,WAAW,EAAE;QACrBY,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/B;MACF;MAEA,MAAMd,WAAW,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC;MAE1CH,WAAW,CAACe,OAAO,GAAG,MAAM;QAC1BX,OAAO,CAACY,GAAG,CAAC,QAAQ,CAAC;MACvB,CAAC;MAEDhB,WAAW,CAACiB,QAAQ,GAAIC,KAAK,IAAK;QAChC,MAAMC,IAAI,GAAGD,KAAK,CAACE,OAAO,CAACC,MAAM,GAAG,CAAC;QACrC,MAAMC,UAAU,GAAGJ,KAAK,CAACE,OAAO,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAACG,UAAU;QAEpD,IAAIJ,KAAK,CAACE,OAAO,CAACD,IAAI,CAAC,CAACI,OAAO,EAAE;UAC/BX,OAAO,CAACU,UAAU,CAACE,IAAI,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC;MAEDxB,WAAW,CAACyB,OAAO,GAAIP,KAAK,IAAK;QAC/Bd,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAER,KAAK,CAACQ,KAAK,CAAC;QACrCb,MAAM,CAAC,IAAIC,KAAK,CAAC,WAAWI,KAAK,CAACQ,KAAK,EAAE,CAAC,CAAC;MAC7C,CAAC;MAED1B,WAAW,CAAC2B,KAAK,GAAG,MAAM;QACxB,IAAI3B,WAAW,CAAC4B,WAAW,KAAKC,SAAS,EAAE;UACzChB,MAAM,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B;MACF,CAAC;MAEDd,WAAW,CAAC8B,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC/B,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACgC,IAAI,CAAC,CAAC;IACzB;EACF;AACF;AAEA,eAAelC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}