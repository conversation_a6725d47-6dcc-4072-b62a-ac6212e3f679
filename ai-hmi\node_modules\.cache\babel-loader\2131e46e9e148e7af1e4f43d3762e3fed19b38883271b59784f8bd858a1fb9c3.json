{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"voice-interaction-manager\"\n};\nconst _hoisted_2 = [\"title\"];\nconst _hoisted_3 = {\n  key: 0,\n  class: \"recognition-result\"\n};\nconst _hoisted_4 = {\n  class: \"result-text\"\n};\nconst _hoisted_5 = {\n  class: \"result-actions\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"status-message\"\n};\nconst _hoisted_7 = {\n  key: 2,\n  class: \"text-input-fallback\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 语音输入按钮 \"), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleVoiceInteraction && $setup.toggleVoiceInteraction(...args)),\n    class: _normalizeClass(['voice-btn', {\n      listening: $setup.isListening,\n      processing: $setup.isProcessing,\n      speaking: $setup.isSpeaking\n    }])\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass($setup.getVoiceButtonIcon)\n  }, null, 2 /* CLASS */)], 2 /* CLASS */), _createCommentVNode(\" 文本输入切换按钮 \"), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleTextInput && $setup.toggleTextInput(...args)),\n    class: \"text-toggle-btn\",\n    title: $setup.showTextInput ? '隐藏文本输入' : '显示文本输入'\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass($setup.showTextInput ? 'fas fa-keyboard' : 'fas fa-edit')\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_2), _createCommentVNode(\" 识别结果显示 \"), $setup.recognitionResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.recognitionResult), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.acceptResult && $setup.acceptResult(...args)),\n    class: \"action-btn accept\"\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.rejectResult && $setup.rejectResult(...args)),\n    class: \"action-btn reject\"\n  }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 处理状态显示 \"), $setup.statusMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n    class: \"fas fa-info-circle\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($setup.statusMessage), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文本输入备用方案 \"), $setup.showTextInput ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.textInput = $event),\n    onKeyup: _cache[5] || (_cache[5] = _withKeys((...args) => $setup.submitTextInput && $setup.submitTextInput(...args), [\"enter\"])),\n    placeholder: \"或直接输入描述...\",\n    class: \"text-input-field\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.textInput]]), _createElementVNode(\"button\", {\n    onClick: _cache[6] || (_cache[6] = (...args) => $setup.submitTextInput && $setup.submitTextInput(...args)),\n    class: \"submit-btn\"\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n    class: \"fas fa-paper-plane\"\n  }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "onClick", "_cache", "args", "$setup", "toggleVoiceInteraction", "_normalizeClass", "isListening", "isProcessing", "isSpeaking", "getVoiceButtonIcon", "toggleTextInput", "title", "showTextInput", "recognitionResult", "_hoisted_3", "_hoisted_4", "_toDisplayString", "_hoisted_5", "acceptResult", "rejectResult", "statusMessage", "_hoisted_6", "_hoisted_7", "textInput", "$event", "onKeyup", "_with<PERSON><PERSON><PERSON>", "submitTextInput", "placeholder"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"voice-interaction-manager\">\r\n    <!-- 语音输入按钮 -->\r\n    <button\r\n      @click=\"toggleVoiceInteraction\"\r\n      :class=\"['voice-btn', {\r\n        listening: isListening,\r\n        processing: isProcessing,\r\n        speaking: isSpeaking\r\n      }]\"\r\n    >\r\n      <i :class=\"getVoiceButtonIcon\"></i>\r\n    </button>\r\n\r\n    <!-- 文本输入切换按钮 -->\r\n    <button\r\n      @click=\"toggleTextInput\"\r\n      class=\"text-toggle-btn\"\r\n      :title=\"showTextInput ? '隐藏文本输入' : '显示文本输入'\"\r\n    >\r\n      <i :class=\"showTextInput ? 'fas fa-keyboard' : 'fas fa-edit'\"></i>\r\n    </button>\r\n\r\n    <!-- 识别结果显示 -->\r\n    <div v-if=\"recognitionResult\" class=\"recognition-result\">\r\n      <div class=\"result-text\">{{ recognitionResult }}</div>\r\n      <div class=\"result-actions\">\r\n        <button @click=\"acceptResult\" class=\"action-btn accept\">\r\n          <i class=\"fas fa-check\"></i>\r\n        </button>\r\n        <button @click=\"rejectResult\" class=\"action-btn reject\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 处理状态显示 -->\r\n    <div v-if=\"statusMessage\" class=\"status-message\">\r\n      <i class=\"fas fa-info-circle\"></i>\r\n      {{ statusMessage }}\r\n    </div>\r\n\r\n    <!-- 文本输入备用方案 -->\r\n    <div v-if=\"showTextInput\" class=\"text-input-fallback\">\r\n      <input\r\n        v-model=\"textInput\"\r\n        @keyup.enter=\"submitTextInput\"\r\n        placeholder=\"或直接输入描述...\"\r\n        class=\"text-input-field\"\r\n      />\r\n      <button @click=\"submitTextInput\" class=\"submit-btn\">\r\n        <i class=\"fas fa-paper-plane\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport AsrService from '@/services/AsrService'\r\nimport TtsService from '@/services/TtsService'\r\nimport LlmService from '@/services/LlmService'\r\n\r\nexport default {\r\n  name: 'VoiceInteractionManager',\r\n  emits: ['wallpaper-prompt-ready', 'scene-switch-requested'],\r\n\r\n  setup(props, { emit }) {\r\n    const asrService = new AsrService()\r\n    const ttsService = new TtsService()\r\n    const llmService = new LlmService()\r\n\r\n    const isListening = ref(false)\r\n    const isProcessing = ref(false)\r\n    const isSpeaking = ref(false)\r\n    const recognitionResult = ref('')\r\n    const textInput = ref('')\r\n    const statusMessage = ref('')\r\n    const showTextInput = ref(false) // 默认隐藏文本输入框\r\n\r\n    const voiceState = computed(() => {\r\n      if (isSpeaking.value) return 'speaking'\r\n      if (isProcessing.value) return 'processing'\r\n      if (isListening.value) return 'listening'\r\n      return 'idle'\r\n    })\r\n\r\n    const getVoiceButtonIcon = computed(() => {\r\n      switch (voiceState.value) {\r\n        case 'speaking':\r\n          return 'fas fa-volume-up'\r\n        case 'processing':\r\n          return 'fas fa-spinner fa-spin'\r\n        case 'listening':\r\n          return 'fas fa-stop'\r\n        default:\r\n          return 'fas fa-microphone'\r\n      }\r\n    })\r\n\r\n    const toggleVoiceInteraction = async () => {\r\n      if (isListening.value) {\r\n        stopVoiceInteraction()\r\n      } else {\r\n        startVoiceInteraction()\r\n      }\r\n    }\r\n\r\n    const startVoiceInteraction = async () => {\r\n      if (isListening.value || isProcessing.value) return\r\n\r\n      try {\r\n        isListening.value = true\r\n        statusMessage.value = '正在聆听...'\r\n        \r\n        // 使用ASR服务进行语音识别\r\n        const result = await asrService.startRecognition()\r\n        recognitionResult.value = result\r\n        \r\n        // 自动接受结果并处理\r\n        await processVoiceResult(result)\r\n        \r\n      } catch (error) {\r\n        console.error('语音识别失败:', error)\r\n        statusMessage.value = '语音识别失败，请重试'\r\n        \r\n        // 语音反馈\r\n        await ttsService.speak('语音识别失败，请重试或使用文本输入')\r\n      } finally {\r\n        isListening.value = false\r\n        statusMessage.value = ''\r\n      }\r\n    }\r\n\r\n    const stopVoiceInteraction = () => {\r\n      if (asrService) {\r\n        asrService.stopRecognition()\r\n      }\r\n      if (ttsService) {\r\n        ttsService.stop()\r\n      }\r\n      isListening.value = false\r\n      statusMessage.value = ''\r\n    }\r\n\r\n    const processVoiceResult = async (userInput) => {\r\n      isProcessing.value = true\r\n      statusMessage.value = '正在处理...'\r\n\r\n      try {\r\n        // 首先尝试识别场景切换指令\r\n        statusMessage.value = '正在理解您的需求...'\r\n        const sceneResult = await llmService.detectSceneFromVoice(userInput)\r\n        \r\n        if (sceneResult.sceneId && sceneResult.confidence > 0.6) {\r\n          // 场景切换逻辑\r\n          statusMessage.value = `正在切换到${getSceneName(sceneResult.sceneId)}...`\r\n          await ttsService.speak(`正在为您切换到${getSceneName(sceneResult.sceneId)}`)\r\n          \r\n          // 发送场景切换请求\r\n          emit('scene-switch-requested', {\r\n            sceneId: sceneResult.sceneId,\r\n            confidence: sceneResult.confidence,\r\n            reason: sceneResult.reason\r\n          })\r\n          \r\n          // 完成反馈\r\n          setTimeout(async () => {\r\n            await ttsService.speak('场景切换完成')\r\n            statusMessage.value = ''\r\n          }, 2000)\r\n          \r\n          return\r\n        }\r\n        \r\n        // 如果不是场景切换，则按原来的逻辑处理壁纸生成\r\n        const enhancedPrompt = await llmService.generateResponse(userInput)\r\n        \r\n        // 语音反馈处理结果\r\n        statusMessage.value = '正在生成壁纸...'\r\n        await ttsService.speak('正在为您生成壁纸，请稍候')\r\n        \r\n        // 发送最终提示词给主题管理器\r\n        emit('wallpaper-prompt-ready', enhancedPrompt)\r\n        \r\n        // 完成反馈\r\n        setTimeout(async () => {\r\n          await ttsService.speak('壁纸生成完成')\r\n          statusMessage.value = ''\r\n        }, 3000)\r\n        \r\n      } catch (error) {\r\n        console.error('处理语音输入失败:', error)\r\n        statusMessage.value = '处理失败，请重试'\r\n        \r\n        await ttsService.speak('处理失败，请重试')\r\n        \r\n        // 降级方案：直接使用原始输入\r\n        emit('wallpaper-prompt-ready', userInput)\r\n      } finally {\r\n        isProcessing.value = false\r\n        recognitionResult.value = ''\r\n      }\r\n    }\r\n\r\n    const acceptResult = async () => {\r\n      if (recognitionResult.value.trim()) {\r\n        await processVoiceResult(recognitionResult.value)\r\n      }\r\n    }\r\n\r\n    const rejectResult = () => {\r\n      recognitionResult.value = ''\r\n      statusMessage.value = ''\r\n      ttsService.speak('请重新描述')\r\n    }\r\n\r\n    const submitTextInput = async () => {\r\n      if (textInput.value.trim()) {\r\n        await processVoiceResult(textInput.value)\r\n        textInput.value = ''\r\n      }\r\n    }\r\n\r\n    const toggleTextInput = () => {\r\n      showTextInput.value = !showTextInput.value\r\n    }\r\n\r\n    // 获取场景的中文名称\r\n    const getSceneName = (sceneId) => {\r\n      const sceneNames = {\r\n        morningCommuteFamily: '家庭出行模式',\r\n        morningCommuteFocus: '专注通勤模式',\r\n        eveningCommute: '下班通勤模式',\r\n        waitingMode: '等待休息模式',\r\n        rainyNight: '雨夜模式',\r\n        familyTrip: '家庭出游模式',\r\n        longDistance: '长途驾驶模式',\r\n        guestMode: '访客模式',\r\n        petMode: '宠物模式',\r\n        carWashMode: '洗车模式',\r\n        romanticMode: '浪漫模式',\r\n        chargingMode: '充电模式',\r\n        fatigueDetection: '疲劳检测模式',\r\n        userSwitch: '用户切换模式',\r\n        parkingMode: '泊车模式',\r\n        emergencyMode: '紧急模式'\r\n      }\r\n      return sceneNames[sceneId] || sceneId\r\n    }\r\n\r\n    // 初始化TTS语音\r\n    onMounted(() => {\r\n      // 等待语音列表加载\r\n      if (ttsService.isSupported) {\r\n        setTimeout(() => {\r\n          ttsService.getVoices()\r\n        }, 100)\r\n      }\r\n    })\r\n\r\n    return {\r\n      isListening,\r\n      isProcessing,\r\n      isSpeaking,\r\n      recognitionResult,\r\n      textInput,\r\n      statusMessage,\r\n      showTextInput,\r\n      voiceState,\r\n      getVoiceButtonIcon,\r\n      toggleVoiceInteraction,\r\n      toggleTextInput,\r\n      acceptResult,\r\n      rejectResult,\r\n      submitTextInput,\r\n      getSceneName\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.voice-interaction-manager {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 20px;\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 10px;\r\n  max-width: 420px;\r\n}\r\n\r\n.voice-btn {\r\n  width: 64px;\r\n  height: 64px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(12px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.voice-btn:hover {\r\n  transform: scale(1.1);\r\n  background: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.voice-btn.listening {\r\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.voice-btn.processing {\r\n  background: linear-gradient(45deg, #4834d4, #686de0);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.voice-btn.speaking {\r\n  background: linear-gradient(45deg, #00d2d3, #54a0ff);\r\n  animation: wave 2s infinite;\r\n}\r\n\r\n.text-toggle-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: none;\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  font-size: 16px;\r\n}\r\n\r\n.text-toggle-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.recognition-result {\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(12px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.result-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.result-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.accept {\r\n  background: #2ed573;\r\n  color: white;\r\n}\r\n\r\n.action-btn.reject {\r\n  background: #ff4757;\r\n  color: white;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.status-message {\r\n  padding: 12px 15px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 10px;\r\n  font-size: 13px;\r\n  color: #333;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.text-input-fallback {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.text-input-field {\r\n  flex: 1;\r\n  padding: 12px 18px;\r\n  border: none;\r\n  border-radius: 25px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  font-size: 14px;\r\n  outline: none;\r\n  backdrop-filter: blur(10px);\r\n  color: #333;\r\n}\r\n\r\n.submit-btn {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { transform: scale(1); }\r\n  25% { transform: scale(1.02); }\r\n  75% { transform: scale(1.02); }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;;;EAuBNA,KAAK,EAAC;;;EAC7BA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;;EAWHA,KAAK,EAAC;;;;EAMNA,KAAK,EAAC;;;uBA1ClCC,mBAAA,CAqDM,OArDNC,UAqDM,GApDJC,mBAAA,YAAe,EACfC,mBAAA,CASS;IARNC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAC,sBAAA,IAAAD,MAAA,CAAAC,sBAAA,IAAAF,IAAA,CAAsB;IAC7BP,KAAK,EAAAU,eAAA;iBAAsCF,MAAA,CAAAG,WAAW;kBAAuBH,MAAA,CAAAI,YAAY;gBAAqBJ,MAAA,CAAAK;;MAM/GT,mBAAA,CAAmC;IAA/BJ,KAAK,EAAAU,eAAA,CAAEF,MAAA,CAAAM,kBAAkB;4CAG/BX,mBAAA,cAAiB,EACjBC,mBAAA,CAMS;IALNC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAO,eAAA,IAAAP,MAAA,CAAAO,eAAA,IAAAR,IAAA,CAAe;IACvBP,KAAK,EAAC,iBAAiB;IACtBgB,KAAK,EAAER,MAAA,CAAAS,aAAa;MAErBb,mBAAA,CAAkE;IAA9DJ,KAAK,EAAAU,eAAA,CAAEF,MAAA,CAAAS,aAAa;wDAG1Bd,mBAAA,YAAe,EACJK,MAAA,CAAAU,iBAAiB,I,cAA5BjB,mBAAA,CAUM,OAVNkB,UAUM,GATJf,mBAAA,CAAsD,OAAtDgB,UAAsD,EAAAC,gBAAA,CAA1Bb,MAAA,CAAAU,iBAAiB,kBAC7Cd,mBAAA,CAOM,OAPNkB,UAOM,GANJlB,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAe,YAAA,IAAAf,MAAA,CAAAe,YAAA,IAAAhB,IAAA,CAAY;IAAEP,KAAK,EAAC;gCAClCI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,0B,IAEzBI,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAgB,YAAA,IAAAhB,MAAA,CAAAgB,YAAA,IAAAjB,IAAA,CAAY;IAAEP,KAAK,EAAC;gCAClCI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,0B,6CAK7BG,mBAAA,YAAe,EACJK,MAAA,CAAAiB,aAAa,I,cAAxBxB,mBAAA,CAGM,OAHNyB,UAGM,G,0BAFJtB,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,4B,iBAAK,GAClC,GAAAqB,gBAAA,CAAGb,MAAA,CAAAiB,aAAa,iB,wCAGlBtB,mBAAA,cAAiB,EACNK,MAAA,CAAAS,aAAa,I,cAAxBhB,mBAAA,CAUM,OAVN0B,UAUM,G,gBATJvB,mBAAA,CAKE;+DAJSI,MAAA,CAAAoB,SAAS,GAAAC,MAAA;IACjBC,OAAK,EAAAxB,MAAA,QAAAA,MAAA,MAAAyB,SAAA,KAAAxB,IAAA,KAAQC,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAwB,eAAA,IAAAzB,IAAA,CAAe;IAC7B0B,WAAW,EAAC,YAAY;IACxBjC,KAAK,EAAC;iEAHGQ,MAAA,CAAAoB,SAAS,E,GAKpBxB,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAwB,eAAA,IAAAzB,IAAA,CAAe;IAAEP,KAAK,EAAC;kCACrCI,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}