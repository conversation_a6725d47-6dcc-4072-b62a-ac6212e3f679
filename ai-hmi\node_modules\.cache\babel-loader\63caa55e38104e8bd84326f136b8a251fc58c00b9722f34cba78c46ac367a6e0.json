{"ast": null, "code": "// Pinia Store 入口文件\nimport { createPinia } from 'pinia';\nconst pinia = createPinia();\nexport default pinia;", "map": {"version": 3, "names": ["createPinia", "pinia"], "sources": ["F:/工作/theme/ai-hmi/src/store/index.js"], "sourcesContent": ["// Pinia Store 入口文件\nimport { createPinia } from 'pinia'\n\nconst pinia = createPinia()\n\nexport default pinia\n"], "mappings": "AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AAEnC,MAAMC,KAAK,GAAGD,WAAW,CAAC,CAAC;AAE3B,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}