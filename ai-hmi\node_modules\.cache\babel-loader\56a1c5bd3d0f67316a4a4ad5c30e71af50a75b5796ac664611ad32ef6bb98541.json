{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nconst DEBOUNCE_DEFAULTS = {\n  trailing: true\n};\nfunction debounce(fn, wait = 25, options = {}) {\n  options = {\n    ...DEBOUNCE_DEFAULTS,\n    ...options\n  };\n  if (!Number.isFinite(wait)) {\n    throw new TypeError(\"Expected `wait` to be a finite number\");\n  }\n  let leadingValue;\n  let timeout;\n  let resolveList = [];\n  let currentPromise;\n  let trailingArgs;\n  const applyFn = (_this, args) => {\n    currentPromise = _applyPromised(fn, _this, args);\n    currentPromise.finally(() => {\n      currentPromise = null;\n      if (options.trailing && trailingArgs && !timeout) {\n        const promise = applyFn(_this, trailingArgs);\n        trailingArgs = null;\n        return promise;\n      }\n    });\n    return currentPromise;\n  };\n  return function (...args) {\n    if (currentPromise) {\n      if (options.trailing) {\n        trailingArgs = args;\n      }\n      return currentPromise;\n    }\n    return new Promise(resolve => {\n      const shouldCallNow = !timeout && options.leading;\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        timeout = null;\n        const promise = options.leading ? leadingValue : applyFn(this, args);\n        for (const _resolve of resolveList) {\n          _resolve(promise);\n        }\n        resolveList = [];\n      }, wait);\n      if (shouldCallNow) {\n        leadingValue = applyFn(this, args);\n        resolve(leadingValue);\n      } else {\n        resolveList.push(resolve);\n      }\n    });\n  };\n}\nasync function _applyPromised(fn, _this, args) {\n  return await fn.apply(_this, args);\n}\nexport { debounce };", "map": {"version": 3, "names": ["DEBOUNCE_DEFAULTS", "trailing", "debounce", "fn", "wait", "options", "Number", "isFinite", "TypeError", "leadingValue", "timeout", "resolveList", "currentPromise", "trailingArgs", "applyFn", "_this", "args", "_applyPromised", "finally", "promise", "Promise", "resolve", "shouldCallNow", "leading", "clearTimeout", "setTimeout", "_resolve", "push", "apply"], "sources": ["F:/工作/theme/ai-hmi/node_modules/perfect-debounce/dist/index.mjs"], "sourcesContent": ["const DEBOUNCE_DEFAULTS = {\n  trailing: true\n};\nfunction debounce(fn, wait = 25, options = {}) {\n  options = { ...DEBOUNCE_DEFAULTS, ...options };\n  if (!Number.isFinite(wait)) {\n    throw new TypeError(\"Expected `wait` to be a finite number\");\n  }\n  let leadingValue;\n  let timeout;\n  let resolveList = [];\n  let currentPromise;\n  let trailingArgs;\n  const applyFn = (_this, args) => {\n    currentPromise = _applyPromised(fn, _this, args);\n    currentPromise.finally(() => {\n      currentPromise = null;\n      if (options.trailing && trailingArgs && !timeout) {\n        const promise = applyFn(_this, trailingArgs);\n        trailingArgs = null;\n        return promise;\n      }\n    });\n    return currentPromise;\n  };\n  return function(...args) {\n    if (currentPromise) {\n      if (options.trailing) {\n        trailingArgs = args;\n      }\n      return currentPromise;\n    }\n    return new Promise((resolve) => {\n      const shouldCallNow = !timeout && options.leading;\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        timeout = null;\n        const promise = options.leading ? leadingValue : applyFn(this, args);\n        for (const _resolve of resolveList) {\n          _resolve(promise);\n        }\n        resolveList = [];\n      }, wait);\n      if (shouldCallNow) {\n        leadingValue = applyFn(this, args);\n        resolve(leadingValue);\n      } else {\n        resolveList.push(resolve);\n      }\n    });\n  };\n}\nasync function _applyPromised(fn, _this, args) {\n  return await fn.apply(_this, args);\n}\n\nexport { debounce };\n"], "mappings": ";AAAA,MAAMA,iBAAiB,GAAG;EACxBC,QAAQ,EAAE;AACZ,CAAC;AACD,SAASC,QAAQA,CAACC,EAAE,EAAEC,IAAI,GAAG,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC7CA,OAAO,GAAG;IAAE,GAAGL,iBAAiB;IAAE,GAAGK;EAAQ,CAAC;EAC9C,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACH,IAAI,CAAC,EAAE;IAC1B,MAAM,IAAII,SAAS,CAAC,uCAAuC,CAAC;EAC9D;EACA,IAAIC,YAAY;EAChB,IAAIC,OAAO;EACX,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,MAAMC,OAAO,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;IAC/BJ,cAAc,GAAGK,cAAc,CAACd,EAAE,EAAEY,KAAK,EAAEC,IAAI,CAAC;IAChDJ,cAAc,CAACM,OAAO,CAAC,MAAM;MAC3BN,cAAc,GAAG,IAAI;MACrB,IAAIP,OAAO,CAACJ,QAAQ,IAAIY,YAAY,IAAI,CAACH,OAAO,EAAE;QAChD,MAAMS,OAAO,GAAGL,OAAO,CAACC,KAAK,EAAEF,YAAY,CAAC;QAC5CA,YAAY,GAAG,IAAI;QACnB,OAAOM,OAAO;MAChB;IACF,CAAC,CAAC;IACF,OAAOP,cAAc;EACvB,CAAC;EACD,OAAO,UAAS,GAAGI,IAAI,EAAE;IACvB,IAAIJ,cAAc,EAAE;MAClB,IAAIP,OAAO,CAACJ,QAAQ,EAAE;QACpBY,YAAY,GAAGG,IAAI;MACrB;MACA,OAAOJ,cAAc;IACvB;IACA,OAAO,IAAIQ,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,aAAa,GAAG,CAACZ,OAAO,IAAIL,OAAO,CAACkB,OAAO;MACjDC,YAAY,CAACd,OAAO,CAAC;MACrBA,OAAO,GAAGe,UAAU,CAAC,MAAM;QACzBf,OAAO,GAAG,IAAI;QACd,MAAMS,OAAO,GAAGd,OAAO,CAACkB,OAAO,GAAGd,YAAY,GAAGK,OAAO,CAAC,IAAI,EAAEE,IAAI,CAAC;QACpE,KAAK,MAAMU,QAAQ,IAAIf,WAAW,EAAE;UAClCe,QAAQ,CAACP,OAAO,CAAC;QACnB;QACAR,WAAW,GAAG,EAAE;MAClB,CAAC,EAAEP,IAAI,CAAC;MACR,IAAIkB,aAAa,EAAE;QACjBb,YAAY,GAAGK,OAAO,CAAC,IAAI,EAAEE,IAAI,CAAC;QAClCK,OAAO,CAACZ,YAAY,CAAC;MACvB,CAAC,MAAM;QACLE,WAAW,CAACgB,IAAI,CAACN,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;AACH;AACA,eAAeJ,cAAcA,CAACd,EAAE,EAAEY,KAAK,EAAEC,IAAI,EAAE;EAC7C,OAAO,MAAMb,EAAE,CAACyB,KAAK,CAACb,KAAK,EAAEC,IAAI,CAAC;AACpC;AAEA,SAASd,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}