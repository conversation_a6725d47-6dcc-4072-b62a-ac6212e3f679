{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useLayoutStore } from '@/store/modules/layout';\nimport { useVPAStore } from '@/store/modules/vpa';\nimport MainLayoutContainer from '@/components/layout/MainLayoutContainer.vue';\nimport GridSystem16x9 from '@/components/layout/GridSystem16x9.vue';\nimport VPADigitalHuman from '@/components/vpa/VPADigitalHuman.vue';\nimport SceneManager from './components/SceneManager.vue';\nimport TestSceneGeneration from './components/TestSceneGeneration.vue';\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue';\nexport default {\n  name: 'App',\n  components: {\n    MainLayoutContainer,\n    GridSystem16x9,\n    VPADigitalHuman,\n    SceneManager,\n    TestSceneGeneration,\n    DynamicWallpaperManager\n  },\n  setup() {\n    const layoutStore = useLayoutStore();\n    const vpaStore = useVPAStore();\n\n    // 响应式状态\n    const isTestMode = ref(false);\n    const currentScene = ref('family');\n    const initialScene = ref('default');\n    const autoSwitchEnabled = ref(false);\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围');\n    const themeColors = ref(null);\n    const currentTime = ref('');\n    const showDebugPanel = ref(false);\n    const showDebugGrid = ref(false);\n\n    // 计算属性\n    const appStyles = computed(() => ({\n      ...layoutStore.gridCSSVariables,\n      '--current-scene': currentScene.value\n    }));\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭',\n        'focus': '专注',\n        'minimal': '简约',\n        'entertainment': '娱乐'\n      };\n      return scenes[currentScene.value] || '通用';\n    });\n\n    // 方法\n    const updateTime = () => {\n      const now = new Date();\n      currentTime.value = now.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    };\n    const toggleMode = () => {\n      isTestMode.value = !isTestMode.value;\n    };\n    const handleSceneChange = newScene => {\n      if (typeof newScene === 'string') {\n        currentScene.value = newScene;\n      } else if (newScene && newScene.target) {\n        currentScene.value = newScene.target.value;\n      }\n      vpaStore.updateScene(currentScene.value);\n      layoutStore.setLayoutMode(currentScene.value);\n      console.log('场景切换到:', currentScene.value);\n    };\n    const handleWallpaperPrompt = promptData => {\n      console.log('收到壁纸提示词:', promptData);\n\n      // 处理不同格式的提示词数据\n      if (typeof promptData === 'string') {\n        currentWallpaperPrompt.value = promptData;\n      } else if (promptData && promptData.prompt) {\n        // 使用生成的情感化提示词\n        currentWallpaperPrompt.value = promptData.prompt;\n\n        // 记录详细的生成信息\n        console.log('🎨 情感化提示词生成详情:', {\n          prompt: promptData.prompt,\n          scene: promptData.scene?.name,\n          context: promptData.context,\n          originalPrompt: promptData.originalPrompt\n        });\n      }\n    };\n    const handleSceneChanged = event => {\n      console.log('场景切换事件:', event);\n\n      // 兼容旧的事件格式\n      if (event && event.sceneName) {\n        handleSceneChange(event.sceneName);\n      }\n\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\n      console.log('等待情感化提示词生成...');\n    };\n    const handleWallpaperChanged = wallpaper => {\n      console.log('壁纸已更换:', wallpaper);\n    };\n    const handleColorsExtracted = colors => {\n      console.log('颜色已提取:', colors);\n      themeColors.value = colors;\n    };\n\n    // 页面加载时的欢迎和初始化\n    const welcomeUser = async () => {\n      console.log('欢迎使用AI-HMI智能场景系统 - 16:9布局版本');\n\n      // 根据时间设置初始场景\n      const now = new Date();\n      const hour = now.getHours();\n      if (hour >= 7 && hour <= 9) {\n        initialScene.value = 'morningCommuteFamily';\n        currentScene.value = 'family';\n      } else if (hour >= 17 && hour <= 19) {\n        initialScene.value = 'eveningCommute';\n        currentScene.value = 'focus';\n      } else if (hour >= 20 || hour <= 6) {\n        initialScene.value = 'rainyNight';\n        currentScene.value = 'minimal';\n      } else {\n        currentScene.value = 'family';\n      }\n    };\n\n    // 生命周期\n    let timeInterval = null;\n    onMounted(() => {\n      // 初始化布局和VPA\n      layoutStore.initializeLayout();\n      vpaStore.initializeVPA();\n\n      // 更新时间\n      updateTime();\n      timeInterval = setInterval(updateTime, 1000);\n\n      // 欢迎用户并设置初始场景\n      welcomeUser();\n\n      // 设置初始场景到stores\n      handleSceneChange(currentScene.value);\n    });\n    onUnmounted(() => {\n      if (timeInterval) {\n        clearInterval(timeInterval);\n      }\n    });\n    return {\n      layoutStore,\n      isTestMode,\n      currentScene,\n      initialScene,\n      autoSwitchEnabled,\n      currentWallpaperPrompt,\n      themeColors,\n      currentTime,\n      showDebugPanel,\n      showDebugGrid,\n      appStyles,\n      sceneText,\n      toggleMode,\n      handleSceneChange,\n      handleWallpaperPrompt,\n      handleSceneChanged,\n      handleWallpaperChanged,\n      handleColorsExtracted\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useLayoutStore", "useVPAStore", "MainLayoutContainer", "GridSystem16x9", "VPADigitalHuman", "SceneManager", "TestSceneGeneration", "DynamicWallpaperManager", "name", "components", "setup", "layoutStore", "vpaStore", "isTestMode", "currentScene", "initialScene", "autoSwitchEnabled", "currentWallpaperPrompt", "themeColors", "currentTime", "showDebugPanel", "showDebugGrid", "appStyles", "gridCSSVariables", "value", "sceneText", "scenes", "updateTime", "now", "Date", "toLocaleTimeString", "hour", "minute", "toggleMode", "handleSceneChange", "newScene", "target", "updateScene", "setLayoutMode", "console", "log", "handleWallpaperPrompt", "promptData", "prompt", "scene", "context", "originalPrompt", "handleSceneChanged", "event", "scene<PERSON><PERSON>", "handleWallpaperChanged", "wallpaper", "handleColorsExtracted", "colors", "welcomeUser", "getHours", "timeInterval", "initializeLayout", "initializeVPA", "setInterval", "clearInterval"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\" :style=\"appStyles\">\r\n    <!-- 动态壁纸背景 -->\r\n    <DynamicWallpaperManager\r\n      :scene-prompt=\"currentWallpaperPrompt\"\r\n      :auto-generate=\"true\"\r\n      :enable-config=\"true\"\r\n      @wallpaper-changed=\"handleWallpaperChanged\"\r\n      @colors-extracted=\"handleColorsExtracted\"\r\n    />\r\n\r\n    <!-- 主布局容器 - 16:9网格系统 -->\r\n    <MainLayoutContainer :scene=\"currentScene\">\r\n      <!-- 灵动岛 -->\r\n      <template #dynamic-island>\r\n        <div class=\"dynamic-island\">\r\n          <span class=\"island-title\">AI-HMI 智能座舱</span>\r\n          <div class=\"island-status\">\r\n            <span class=\"time\">{{ currentTime }}</span>\r\n            <span class=\"scene-indicator\">{{ sceneText }}</span>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 卡片区域 -->\r\n      <template #card-zone=\"{ layout }\">\r\n        <GridSystem16x9\r\n          mode=\"standard\"\r\n          :show-grid-lines=\"showDebugGrid\"\r\n          class=\"card-zone-grid\"\r\n        >\r\n          <template #default=\"{ grid }\">\r\n            <!-- 场景管理器 - 使用新的16:9布局 -->\r\n            <SceneManager\r\n              :initial-scene=\"initialScene\"\r\n              :current-scene=\"currentScene\"\r\n              :show-indicator=\"true\"\r\n              :auto-switch=\"autoSwitchEnabled\"\r\n              :theme-colors=\"themeColors\"\r\n              @scene-changed=\"handleSceneChanged\"\r\n              @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n            />\r\n          </template>\r\n        </GridSystem16x9>\r\n      </template>\r\n\r\n      <!-- 驾驶区域 -->\r\n      <template #driving-zone=\"{ layout }\">\r\n        <div class=\"driving-zone-content\">\r\n          <div class=\"navigation-placeholder\">\r\n            <div class=\"nav-icon\">🗺️</div>\r\n            <h3>导航地图</h3>\r\n            <p>16:9布局 - 驾驶信息区</p>\r\n            <div class=\"layout-info\">\r\n              <small>网格: {{ layoutStore.gridColumns }}x{{ layoutStore.gridRows }}</small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- VPA数字人 -->\r\n      <template #vpa=\"{ layout }\">\r\n        <VPADigitalHuman\r\n          size=\"medium\"\r\n          :show-info-panel=\"true\"\r\n          :interactive=\"true\"\r\n        />\r\n      </template>\r\n    </MainLayoutContainer>\r\n\r\n    <!-- 测试模式切换 -->\r\n    <div class=\"mode-toggle\">\r\n      <button @click=\"toggleMode\" class=\"toggle-btn\">\r\n        {{ isTestMode ? '🚗 正常模式' : '🧪 测试模式' }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 测试模式覆盖层 -->\r\n    <div v-if=\"isTestMode\" class=\"test-mode-overlay\">\r\n      <TestSceneGeneration\r\n        :theme-colors=\"themeColors\"\r\n        @colors-extracted=\"handleColorsExtracted\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 调试控制面板 -->\r\n    <div v-if=\"showDebugPanel\" class=\"debug-panel\">\r\n      <h4>🔧 调试面板</h4>\r\n      <div class=\"debug-controls\">\r\n        <label>\r\n          <input type=\"checkbox\" v-model=\"showDebugGrid\" />\r\n          显示网格线\r\n        </label>\r\n        <label>\r\n          场景:\r\n          <select v-model=\"currentScene\" @change=\"handleSceneChange\">\r\n            <option value=\"family\">家庭</option>\r\n            <option value=\"focus\">专注</option>\r\n            <option value=\"minimal\">简约</option>\r\n            <option value=\"entertainment\">娱乐</option>\r\n          </select>\r\n        </label>\r\n        <div class=\"debug-info\">\r\n          <small>网格: {{ layoutStore.gridColumns }}x{{ layoutStore.gridRows }}</small>\r\n          <small>间距: {{ layoutStore.gridGap }}px</small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 调试按钮 -->\r\n    <button\r\n      class=\"debug-toggle\"\r\n      @click=\"showDebugPanel = !showDebugPanel\"\r\n    >\r\n      🔧\r\n    </button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useLayoutStore } from '@/store/modules/layout'\r\nimport { useVPAStore } from '@/store/modules/vpa'\r\nimport MainLayoutContainer from '@/components/layout/MainLayoutContainer.vue'\r\nimport GridSystem16x9 from '@/components/layout/GridSystem16x9.vue'\r\nimport VPADigitalHuman from '@/components/vpa/VPADigitalHuman.vue'\r\nimport SceneManager from './components/SceneManager.vue'\r\nimport TestSceneGeneration from './components/TestSceneGeneration.vue'\r\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    MainLayoutContainer,\r\n    GridSystem16x9,\r\n    VPADigitalHuman,\r\n    SceneManager,\r\n    TestSceneGeneration,\r\n    DynamicWallpaperManager\r\n  },\r\n\r\n  setup() {\r\n    const layoutStore = useLayoutStore()\r\n    const vpaStore = useVPAStore()\r\n\r\n    // 响应式状态\r\n    const isTestMode = ref(false)\r\n    const currentScene = ref('family')\r\n    const initialScene = ref('default')\r\n    const autoSwitchEnabled = ref(false)\r\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围')\r\n    const themeColors = ref(null)\r\n    const currentTime = ref('')\r\n    const showDebugPanel = ref(false)\r\n    const showDebugGrid = ref(false)\r\n\r\n    // 计算属性\r\n    const appStyles = computed(() => ({\r\n      ...layoutStore.gridCSSVariables,\r\n      '--current-scene': currentScene.value\r\n    }))\r\n\r\n    const sceneText = computed(() => {\r\n      const scenes = {\r\n        'family': '家庭',\r\n        'focus': '专注',\r\n        'minimal': '简约',\r\n        'entertainment': '娱乐'\r\n      }\r\n      return scenes[currentScene.value] || '通用'\r\n    })\r\n\r\n    // 方法\r\n    const updateTime = () => {\r\n      const now = new Date()\r\n      currentTime.value = now.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      })\r\n    }\r\n\r\n    const toggleMode = () => {\r\n      isTestMode.value = !isTestMode.value\r\n    }\r\n\r\n    const handleSceneChange = (newScene) => {\r\n      if (typeof newScene === 'string') {\r\n        currentScene.value = newScene\r\n      } else if (newScene && newScene.target) {\r\n        currentScene.value = newScene.target.value\r\n      }\r\n\r\n      vpaStore.updateScene(currentScene.value)\r\n      layoutStore.setLayoutMode(currentScene.value)\r\n\r\n      console.log('场景切换到:', currentScene.value)\r\n    }\r\n\r\n    const handleWallpaperPrompt = (promptData) => {\r\n      console.log('收到壁纸提示词:', promptData)\r\n\r\n      // 处理不同格式的提示词数据\r\n      if (typeof promptData === 'string') {\r\n        currentWallpaperPrompt.value = promptData\r\n      } else if (promptData && promptData.prompt) {\r\n        // 使用生成的情感化提示词\r\n        currentWallpaperPrompt.value = promptData.prompt\r\n\r\n        // 记录详细的生成信息\r\n        console.log('🎨 情感化提示词生成详情:', {\r\n          prompt: promptData.prompt,\r\n          scene: promptData.scene?.name,\r\n          context: promptData.context,\r\n          originalPrompt: promptData.originalPrompt\r\n        })\r\n      }\r\n    }\r\n\r\n    const handleSceneChanged = (event) => {\r\n      console.log('场景切换事件:', event)\r\n\r\n      // 兼容旧的事件格式\r\n      if (event && event.sceneName) {\r\n        handleSceneChange(event.sceneName)\r\n      }\r\n\r\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\r\n      console.log('等待情感化提示词生成...')\r\n    }\r\n\r\n    const handleWallpaperChanged = (wallpaper) => {\r\n      console.log('壁纸已更换:', wallpaper)\r\n    }\r\n\r\n    const handleColorsExtracted = (colors) => {\r\n      console.log('颜色已提取:', colors)\r\n      themeColors.value = colors\r\n    }\r\n\r\n    // 页面加载时的欢迎和初始化\r\n    const welcomeUser = async () => {\r\n      console.log('欢迎使用AI-HMI智能场景系统 - 16:9布局版本')\r\n\r\n      // 根据时间设置初始场景\r\n      const now = new Date()\r\n      const hour = now.getHours()\r\n\r\n      if (hour >= 7 && hour <= 9) {\r\n        initialScene.value = 'morningCommuteFamily'\r\n        currentScene.value = 'family'\r\n      } else if (hour >= 17 && hour <= 19) {\r\n        initialScene.value = 'eveningCommute'\r\n        currentScene.value = 'focus'\r\n      } else if (hour >= 20 || hour <= 6) {\r\n        initialScene.value = 'rainyNight'\r\n        currentScene.value = 'minimal'\r\n      } else {\r\n        currentScene.value = 'family'\r\n      }\r\n    }\r\n\r\n    // 生命周期\r\n    let timeInterval = null\r\n\r\n    onMounted(() => {\r\n      // 初始化布局和VPA\r\n      layoutStore.initializeLayout()\r\n      vpaStore.initializeVPA()\r\n\r\n      // 更新时间\r\n      updateTime()\r\n      timeInterval = setInterval(updateTime, 1000)\r\n\r\n      // 欢迎用户并设置初始场景\r\n      welcomeUser()\r\n\r\n      // 设置初始场景到stores\r\n      handleSceneChange(currentScene.value)\r\n    })\r\n\r\n    onUnmounted(() => {\r\n      if (timeInterval) {\r\n        clearInterval(timeInterval)\r\n      }\r\n    })\r\n\r\n    return {\r\n      layoutStore,\r\n      isTestMode,\r\n      currentScene,\r\n      initialScene,\r\n      autoSwitchEnabled,\r\n      currentWallpaperPrompt,\r\n      themeColors,\r\n      currentTime,\r\n      showDebugPanel,\r\n      showDebugGrid,\r\n      appStyles,\r\n      sceneText,\r\n      toggleMode,\r\n      handleSceneChange,\r\n      handleWallpaperPrompt,\r\n      handleSceneChanged,\r\n      handleWallpaperChanged,\r\n      handleColorsExtracted\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式重置 */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#app {\r\n  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n/* 灵动岛样式 */\r\n.dynamic-island {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 22px;\r\n  padding: 8px 24px;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  min-width: 300px;\r\n  height: 44px;\r\n}\r\n\r\n.island-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.island-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  font-size: 12px;\r\n}\r\n\r\n.time {\r\n  font-weight: 500;\r\n}\r\n\r\n.scene-indicator {\r\n  background: rgba(74, 144, 226, 0.8);\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  font-size: 11px;\r\n}\r\n\r\n/* 卡片区域网格 */\r\n.card-zone-grid {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 驾驶区域 */\r\n.driving-zone-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 15px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.navigation-placeholder {\r\n  text-align: center;\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.navigation-placeholder h3 {\r\n  margin-bottom: 5px;\r\n  font-size: 18px;\r\n}\r\n\r\n.navigation-placeholder p {\r\n  font-size: 14px;\r\n  opacity: 0.7;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.layout-info {\r\n  margin-top: 10px;\r\n  padding: 5px 10px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 模式切换按钮 */\r\n.mode-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 9999;\r\n}\r\n\r\n.toggle-btn {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #333;\r\n  font-weight: 600;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 测试模式覆盖层 */\r\n.test-mode-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 8000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 调试面板 */\r\n.debug-panel {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  color: white;\r\n  font-size: 12px;\r\n  z-index: 10000;\r\n  min-width: 200px;\r\n}\r\n\r\n.debug-panel h4 {\r\n  margin-bottom: 10px;\r\n  color: #4a90e2;\r\n}\r\n\r\n.debug-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.debug-controls label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.debug-controls input,\r\n.debug-controls select {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: white;\r\n  padding: 4px 8px;\r\n}\r\n\r\n.debug-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  margin-top: 8px;\r\n  padding-top: 8px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.debug-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 20px;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(20px);\r\n  color: white;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n  z-index: 9999;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debug-toggle:hover {\r\n  background: rgba(0, 0, 0, 0.9);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .dynamic-island {\r\n    min-width: 250px;\r\n    padding: 6px 20px;\r\n  }\r\n\r\n  .island-title {\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .dynamic-island {\r\n    min-width: 200px;\r\n    padding: 4px 16px;\r\n  }\r\n\r\n  .island-title {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .debug-panel {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 10px;\r\n    min-width: 150px;\r\n  }\r\n}\r\n\r\n/* 全局字体引入 */\r\n@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\r\n\r\n/* 图标库引入 */\r\n@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');\r\n</style>\r\n"], "mappings": "AAwHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,SAASC,cAAa,QAAS,wBAAuB;AACtD,SAASC,WAAU,QAAS,qBAAoB;AAChD,OAAOC,mBAAkB,MAAO,6CAA4C;AAC5E,OAAOC,cAAa,MAAO,wCAAuC;AAClE,OAAOC,eAAc,MAAO,sCAAqC;AACjE,OAAOC,YAAW,MAAO,+BAA8B;AACvD,OAAOC,mBAAkB,MAAO,sCAAqC;AACrE,OAAOC,uBAAsB,MAAO,0CAAyC;AAE7E,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVP,mBAAmB;IACnBC,cAAc;IACdC,eAAe;IACfC,YAAY;IACZC,mBAAmB;IACnBC;EACF,CAAC;EAEDG,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIX,cAAc,CAAC;IACnC,MAAMY,QAAO,GAAIX,WAAW,CAAC;;IAE7B;IACA,MAAMY,UAAS,GAAIjB,GAAG,CAAC,KAAK;IAC5B,MAAMkB,YAAW,GAAIlB,GAAG,CAAC,QAAQ;IACjC,MAAMmB,YAAW,GAAInB,GAAG,CAAC,SAAS;IAClC,MAAMoB,iBAAgB,GAAIpB,GAAG,CAAC,KAAK;IACnC,MAAMqB,sBAAqB,GAAIrB,GAAG,CAAC,mCAAmC;IACtE,MAAMsB,WAAU,GAAItB,GAAG,CAAC,IAAI;IAC5B,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,EAAE;IAC1B,MAAMwB,cAAa,GAAIxB,GAAG,CAAC,KAAK;IAChC,MAAMyB,aAAY,GAAIzB,GAAG,CAAC,KAAK;;IAE/B;IACA,MAAM0B,SAAQ,GAAIzB,QAAQ,CAAC,OAAO;MAChC,GAAGc,WAAW,CAACY,gBAAgB;MAC/B,iBAAiB,EAAET,YAAY,CAACU;IAClC,CAAC,CAAC;IAEF,MAAMC,SAAQ,GAAI5B,QAAQ,CAAC,MAAM;MAC/B,MAAM6B,MAAK,GAAI;QACb,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;QACf,eAAe,EAAE;MACnB;MACA,OAAOA,MAAM,CAACZ,YAAY,CAACU,KAAK,KAAK,IAAG;IAC1C,CAAC;;IAED;IACA,MAAMG,UAAS,GAAIA,CAAA,KAAM;MACvB,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrBV,WAAW,CAACK,KAAI,GAAII,GAAG,CAACE,kBAAkB,CAAC,OAAO,EAAE;QAClDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC;IACH;IAEA,MAAMC,UAAS,GAAIA,CAAA,KAAM;MACvBpB,UAAU,CAACW,KAAI,GAAI,CAACX,UAAU,CAACW,KAAI;IACrC;IAEA,MAAMU,iBAAgB,GAAKC,QAAQ,IAAK;MACtC,IAAI,OAAOA,QAAO,KAAM,QAAQ,EAAE;QAChCrB,YAAY,CAACU,KAAI,GAAIW,QAAO;MAC9B,OAAO,IAAIA,QAAO,IAAKA,QAAQ,CAACC,MAAM,EAAE;QACtCtB,YAAY,CAACU,KAAI,GAAIW,QAAQ,CAACC,MAAM,CAACZ,KAAI;MAC3C;MAEAZ,QAAQ,CAACyB,WAAW,CAACvB,YAAY,CAACU,KAAK;MACvCb,WAAW,CAAC2B,aAAa,CAACxB,YAAY,CAACU,KAAK;MAE5Ce,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE1B,YAAY,CAACU,KAAK;IAC1C;IAEA,MAAMiB,qBAAoB,GAAKC,UAAU,IAAK;MAC5CH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEE,UAAU;;MAElC;MACA,IAAI,OAAOA,UAAS,KAAM,QAAQ,EAAE;QAClCzB,sBAAsB,CAACO,KAAI,GAAIkB,UAAS;MAC1C,OAAO,IAAIA,UAAS,IAAKA,UAAU,CAACC,MAAM,EAAE;QAC1C;QACA1B,sBAAsB,CAACO,KAAI,GAAIkB,UAAU,CAACC,MAAK;;QAE/C;QACAJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BG,MAAM,EAAED,UAAU,CAACC,MAAM;UACzBC,KAAK,EAAEF,UAAU,CAACE,KAAK,EAAEpC,IAAI;UAC7BqC,OAAO,EAAEH,UAAU,CAACG,OAAO;UAC3BC,cAAc,EAAEJ,UAAU,CAACI;QAC7B,CAAC;MACH;IACF;IAEA,MAAMC,kBAAiB,GAAKC,KAAK,IAAK;MACpCT,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEQ,KAAK;;MAE5B;MACA,IAAIA,KAAI,IAAKA,KAAK,CAACC,SAAS,EAAE;QAC5Bf,iBAAiB,CAACc,KAAK,CAACC,SAAS;MACnC;;MAEA;MACAV,OAAO,CAACC,GAAG,CAAC,eAAe;IAC7B;IAEA,MAAMU,sBAAqB,GAAKC,SAAS,IAAK;MAC5CZ,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEW,SAAS;IACjC;IAEA,MAAMC,qBAAoB,GAAKC,MAAM,IAAK;MACxCd,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEa,MAAM;MAC5BnC,WAAW,CAACM,KAAI,GAAI6B,MAAK;IAC3B;;IAEA;IACA,MAAMC,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9Bf,OAAO,CAACC,GAAG,CAAC,6BAA6B;;MAEzC;MACA,MAAMZ,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrB,MAAME,IAAG,GAAIH,GAAG,CAAC2B,QAAQ,CAAC;MAE1B,IAAIxB,IAAG,IAAK,KAAKA,IAAG,IAAK,CAAC,EAAE;QAC1BhB,YAAY,CAACS,KAAI,GAAI,sBAAqB;QAC1CV,YAAY,CAACU,KAAI,GAAI,QAAO;MAC9B,OAAO,IAAIO,IAAG,IAAK,EAAC,IAAKA,IAAG,IAAK,EAAE,EAAE;QACnChB,YAAY,CAACS,KAAI,GAAI,gBAAe;QACpCV,YAAY,CAACU,KAAI,GAAI,OAAM;MAC7B,OAAO,IAAIO,IAAG,IAAK,EAAC,IAAKA,IAAG,IAAK,CAAC,EAAE;QAClChB,YAAY,CAACS,KAAI,GAAI,YAAW;QAChCV,YAAY,CAACU,KAAI,GAAI,SAAQ;MAC/B,OAAO;QACLV,YAAY,CAACU,KAAI,GAAI,QAAO;MAC9B;IACF;;IAEA;IACA,IAAIgC,YAAW,GAAI,IAAG;IAEtB1D,SAAS,CAAC,MAAM;MACd;MACAa,WAAW,CAAC8C,gBAAgB,CAAC;MAC7B7C,QAAQ,CAAC8C,aAAa,CAAC;;MAEvB;MACA/B,UAAU,CAAC;MACX6B,YAAW,GAAIG,WAAW,CAAChC,UAAU,EAAE,IAAI;;MAE3C;MACA2B,WAAW,CAAC;;MAEZ;MACApB,iBAAiB,CAACpB,YAAY,CAACU,KAAK;IACtC,CAAC;IAEDzB,WAAW,CAAC,MAAM;MAChB,IAAIyD,YAAY,EAAE;QAChBI,aAAa,CAACJ,YAAY;MAC5B;IACF,CAAC;IAED,OAAO;MACL7C,WAAW;MACXE,UAAU;MACVC,YAAY;MACZC,YAAY;MACZC,iBAAiB;MACjBC,sBAAsB;MACtBC,WAAW;MACXC,WAAW;MACXC,cAAc;MACdC,aAAa;MACbC,SAAS;MACTG,SAAS;MACTQ,UAAU;MACVC,iBAAiB;MACjBO,qBAAqB;MACrBM,kBAAkB;MAClBG,sBAAsB;MACtBE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}