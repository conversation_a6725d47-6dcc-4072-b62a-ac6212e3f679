{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"panel-header\"\n};\nconst _hoisted_2 = {\n  class: \"vpa-avatar-section\"\n};\nconst _hoisted_3 = {\n  class: \"avatar-container\"\n};\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  class: \"expression-overlay\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"status-indicator listening\"\n};\nconst _hoisted_7 = {\n  key: 1,\n  class: \"status-indicator speaking\"\n};\nconst _hoisted_8 = {\n  class: \"vpa-info\"\n};\nconst _hoisted_9 = {\n  class: \"vpa-status\"\n};\nconst _hoisted_10 = {\n  class: \"mode-badge\"\n};\nconst _hoisted_11 = {\n  class: \"scene-badge\"\n};\nconst _hoisted_12 = {\n  class: \"panel-controls\"\n};\nconst _hoisted_13 = [\"title\"];\nconst _hoisted_14 = {\n  class: \"panel-body\"\n};\nconst _hoisted_15 = {\n  class: \"conversation-area\"\n};\nconst _hoisted_16 = {\n  class: \"conversation-header\"\n};\nconst _hoisted_17 = {\n  class: \"messages-container\",\n  ref: \"messagesContainer\"\n};\nconst _hoisted_18 = {\n  class: \"message-content\"\n};\nconst _hoisted_19 = {\n  class: \"message-time\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"empty-conversation\"\n};\nconst _hoisted_21 = {\n  class: \"interaction-controls\"\n};\nconst _hoisted_22 = {\n  class: \"voice-controls\"\n};\nconst _hoisted_23 = {\n  class: \"btn-icon\"\n};\nconst _hoisted_24 = {\n  class: \"btn-text\"\n};\nconst _hoisted_25 = {\n  class: \"btn-icon\"\n};\nconst _hoisted_26 = {\n  class: \"btn-text\"\n};\nconst _hoisted_27 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"emotion-indicator\"\n};\nconst _hoisted_29 = {\n  class: \"emotion-status\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"vpa-interaction-panel\", [`size-${$props.size}`, `mode-${$setup.vpaStore.currentMode}`, {\n      'active': $setup.vpaStore.isActive,\n      'expanded': $setup.isExpanded\n    }]]),\n    style: _normalizeStyle($setup.panelStyles)\n  }, [_createCommentVNode(\" 面板头部 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n    src: `/docs/${$setup.vpaStore.avatar}`,\n    alt: `VPA ${$setup.vpaStore.currentExpression}`,\n    class: \"avatar-image\",\n    onError: _cache[0] || (_cache[0] = (...args) => $setup.handleImageError && $setup.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.expressionEmoji), 1 /* TEXT */), _createCommentVNode(\" 状态指示器 \"), $setup.vpaStore.isListening ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n    class: \"pulse-ring\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 🎤 \", -1 /* CACHED */)]))) : $setup.vpaStore.isSpeaking ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _cache[11] || (_cache[11] = [_createElementVNode(\"div\", {\n    class: \"wave-animation\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 🔊 \", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"vpa-name\"\n  }, \"AI助手\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.modeText), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_11, _toDisplayString($setup.sceneText), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"button\", {\n    class: \"control-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleExpanded && $setup.toggleExpanded(...args)),\n    title: $setup.isExpanded ? '收起面板' : '展开面板'\n  }, _toDisplayString($setup.isExpanded ? '📐' : '📏'), 9 /* TEXT, PROPS */, _hoisted_13), _createElementVNode(\"button\", {\n    class: \"control-btn\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.switchMode && $setup.switchMode(...args)),\n    title: \"切换模式\"\n  }, \" 🔄 \")])]), _createCommentVNode(\" 面板主体内容 \"), _createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 对话区域 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n    class: \"conversation-title\"\n  }, \"对话记录\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    class: \"clear-btn\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.clearConversation && $setup.clearConversation(...args)),\n    title: \"清空对话\"\n  }, \" 🗑️ \")]), _createElementVNode(\"div\", _hoisted_17, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentMessages, (message, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"message-item\", {\n        'user-message': message.type === 'user',\n        'vpa-message': message.type === 'vpa'\n      }])\n    }, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString(message.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, _toDisplayString($setup.formatTime(message.timestamp)), 1 /* TEXT */)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), $setup.recentMessages.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, _cache[14] || (_cache[14] = [_createElementVNode(\"div\", {\n    class: \"empty-icon\"\n  }, \"💬\", -1 /* CACHED */), _createElementVNode(\"div\", {\n    class: \"empty-text\"\n  }, \"开始对话吧\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */)]), _createCommentVNode(\" 交互控制区 \"), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"voice-btn\", {\n      active: $setup.vpaStore.isListening\n    }]),\n    onClick: _cache[4] || (_cache[4] = (...args) => $setup.toggleListening && $setup.toggleListening(...args))\n  }, [_createElementVNode(\"span\", _hoisted_23, _toDisplayString($setup.vpaStore.isListening ? '⏹️' : '🎤'), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_24, _toDisplayString($setup.vpaStore.isListening ? '停止' : '语音'), 1 /* TEXT */)], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"voice-btn\", {\n      active: $setup.vpaStore.isSpeaking\n    }]),\n    onClick: _cache[5] || (_cache[5] = (...args) => $setup.toggleSpeaking && $setup.toggleSpeaking(...args))\n  }, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.vpaStore.isSpeaking ? '🔇' : '🔊'), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString($setup.vpaStore.isSpeaking ? '静音' : '播放'), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"button\", {\n    class: \"quick-btn\",\n    onClick: _cache[6] || (_cache[6] = $event => $setup.quickAction('weather')),\n    title: \"天气查询\"\n  }, \" 🌤️ \"), _createElementVNode(\"button\", {\n    class: \"quick-btn\",\n    onClick: _cache[7] || (_cache[7] = $event => $setup.quickAction('navigation')),\n    title: \"导航助手\"\n  }, \" 🗺️ \"), _createElementVNode(\"button\", {\n    class: \"quick-btn\",\n    onClick: _cache[8] || (_cache[8] = $event => $setup.quickAction('music')),\n    title: \"音乐控制\"\n  }, \" 🎵 \"), _createElementVNode(\"button\", {\n    class: \"quick-btn\",\n    onClick: _cache[9] || (_cache[9] = $event => $setup.quickAction('schedule')),\n    title: \"日程管理\"\n  }, \" 📅 \")])])]), _createCommentVNode(\" 情绪感知指示器 \"), $props.showEmotionIndicator ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"emotion-label\"\n  }, \"情绪感知\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($setup.emotionStatus), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_normalizeClass", "$props", "size", "$setup", "vpaStore", "currentMode", "isActive", "isExpanded", "style", "_normalizeStyle", "panelStyles", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "src", "avatar", "alt", "currentExpression", "onError", "_cache", "args", "handleImageError", "_hoisted_5", "_toDisplayString", "expressionEmoji", "isListening", "_hoisted_6", "isSpeaking", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "modeText", "_hoisted_11", "sceneText", "_hoisted_12", "onClick", "toggleExpanded", "title", "_hoisted_13", "switchMode", "_hoisted_14", "_hoisted_15", "_hoisted_16", "clearConversation", "_hoisted_17", "_Fragment", "_renderList", "recentMessages", "message", "index", "key", "type", "_hoisted_18", "content", "_hoisted_19", "formatTime", "timestamp", "length", "_hoisted_20", "_hoisted_21", "_hoisted_22", "active", "toggleListening", "_hoisted_23", "_hoisted_24", "toggleSpeaking", "_hoisted_25", "_hoisted_26", "_hoisted_27", "$event", "quickAction", "showEmotionIndicator", "_hoisted_28", "_hoisted_29", "emotionStatus"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAInteractionPanel.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-interaction-panel\"\n    :class=\"[\n      `size-${size}`,\n      `mode-${vpaStore.currentMode}`,\n      { 'active': vpaStore.isActive, 'expanded': isExpanded }\n    ]\"\n    :style=\"panelStyles\"\n  >\n    <!-- 面板头部 -->\n    <div class=\"panel-header\">\n      <div class=\"vpa-avatar-section\">\n        <div class=\"avatar-container\">\n          <img \n            :src=\"`/docs/${vpaStore.avatar}`\" \n            :alt=\"`VPA ${vpaStore.currentExpression}`\"\n            class=\"avatar-image\"\n            @error=\"handleImageError\"\n          />\n          <div class=\"expression-overlay\">{{ expressionEmoji }}</div>\n          \n          <!-- 状态指示器 -->\n          <div v-if=\"vpaStore.isListening\" class=\"status-indicator listening\">\n            <div class=\"pulse-ring\"></div>\n            🎤\n          </div>\n          <div v-else-if=\"vpaStore.isSpeaking\" class=\"status-indicator speaking\">\n            <div class=\"wave-animation\"></div>\n            🔊\n          </div>\n        </div>\n        \n        <div class=\"vpa-info\">\n          <div class=\"vpa-name\">AI助手</div>\n          <div class=\"vpa-status\">\n            <span class=\"mode-badge\">{{ modeText }}</span>\n            <span class=\"scene-badge\">{{ sceneText }}</span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"panel-controls\">\n        <button \n          class=\"control-btn\"\n          @click=\"toggleExpanded\"\n          :title=\"isExpanded ? '收起面板' : '展开面板'\"\n        >\n          {{ isExpanded ? '📐' : '📏' }}\n        </button>\n        <button \n          class=\"control-btn\"\n          @click=\"switchMode\"\n          title=\"切换模式\"\n        >\n          🔄\n        </button>\n      </div>\n    </div>\n    \n    <!-- 面板主体内容 -->\n    <div class=\"panel-body\">\n      <!-- 对话区域 -->\n      <div class=\"conversation-area\">\n        <div class=\"conversation-header\">\n          <span class=\"conversation-title\">对话记录</span>\n          <button \n            class=\"clear-btn\"\n            @click=\"clearConversation\"\n            title=\"清空对话\"\n          >\n            🗑️\n          </button>\n        </div>\n        \n        <div class=\"messages-container\" ref=\"messagesContainer\">\n          <div \n            v-for=\"(message, index) in recentMessages\" \n            :key=\"index\"\n            class=\"message-item\"\n            :class=\"{ 'user-message': message.type === 'user', 'vpa-message': message.type === 'vpa' }\"\n          >\n            <div class=\"message-content\">{{ message.content }}</div>\n            <div class=\"message-time\">{{ formatTime(message.timestamp) }}</div>\n          </div>\n          \n          <div v-if=\"recentMessages.length === 0\" class=\"empty-conversation\">\n            <div class=\"empty-icon\">💬</div>\n            <div class=\"empty-text\">开始对话吧</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 交互控制区 -->\n      <div class=\"interaction-controls\">\n        <div class=\"voice-controls\">\n          <button \n            class=\"voice-btn\"\n            :class=\"{ active: vpaStore.isListening }\"\n            @click=\"toggleListening\"\n          >\n            <span class=\"btn-icon\">{{ vpaStore.isListening ? '⏹️' : '🎤' }}</span>\n            <span class=\"btn-text\">{{ vpaStore.isListening ? '停止' : '语音' }}</span>\n          </button>\n          \n          <button \n            class=\"voice-btn\"\n            :class=\"{ active: vpaStore.isSpeaking }\"\n            @click=\"toggleSpeaking\"\n          >\n            <span class=\"btn-icon\">{{ vpaStore.isSpeaking ? '🔇' : '🔊' }}</span>\n            <span class=\"btn-text\">{{ vpaStore.isSpeaking ? '静音' : '播放' }}</span>\n          </button>\n        </div>\n        \n        <div class=\"quick-actions\">\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('weather')\"\n            title=\"天气查询\"\n          >\n            🌤️\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('navigation')\"\n            title=\"导航助手\"\n          >\n            🗺️\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('music')\"\n            title=\"音乐控制\"\n          >\n            🎵\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('schedule')\"\n            title=\"日程管理\"\n          >\n            📅\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 情绪感知指示器 -->\n    <div v-if=\"showEmotionIndicator\" class=\"emotion-indicator\">\n      <div class=\"emotion-label\">情绪感知</div>\n      <div class=\"emotion-status\">{{ emotionStatus }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref, nextTick, watch } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPAInteractionPanel',\n  props: {\n    // 组件尺寸：4x4, 8x9\n    size: {\n      type: String,\n      default: '4x4',\n      validator: value => ['4x4', '8x9'].includes(value)\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示情绪指示器\n    showEmotionIndicator: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['mode-change', 'interaction', 'quick-action'],\n  setup(props, { emit }) {\n    const vpaStore = useVPAStore()\n    const messagesContainer = ref(null)\n    const isExpanded = ref(false)\n    const imageError = ref(false)\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[vpaStore.currentExpression] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      }\n      return modes[vpaStore.currentMode] || '未知模式'\n    })\n\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭',\n        'focus': '专注',\n        'minimal': '简约',\n        'entertainment': '娱乐'\n      }\n      return scenes[vpaStore.currentScene] || '通用'\n    })\n\n    const recentMessages = computed(() => {\n      // 显示最近的消息，根据面板大小限制数量\n      const maxMessages = props.size === '8x9' ? 10 : 5\n      return vpaStore.conversationHistory.slice(-maxMessages)\n    })\n\n    const emotionStatus = computed(() => {\n      // 模拟情绪感知状态\n      const emotions = ['平静', '愉悦', '专注', '关注']\n      return emotions[Math.floor(Math.random() * emotions.length)]\n    })\n\n    const panelStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position)\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      }\n    })\n\n    // 方法\n    const toggleExpanded = () => {\n      isExpanded.value = !isExpanded.value\n    }\n\n    const switchMode = () => {\n      const modes = ['companion', 'interactive', 'restricted']\n      const currentIndex = modes.indexOf(vpaStore.currentMode)\n      const nextMode = modes[(currentIndex + 1) % modes.length]\n      vpaStore.switchMode(nextMode)\n      emit('mode-change', nextMode)\n    }\n\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start')\n    }\n\n    const toggleSpeaking = () => {\n      // 切换播放状态的逻辑\n      if (vpaStore.isSpeaking) {\n        vpaStore.setAction('idle')\n      } else {\n        vpaStore.speak('正在为您服务')\n      }\n      emit('interaction', vpaStore.isSpeaking ? 'speaking-stop' : 'speaking-start')\n    }\n\n    const clearConversation = () => {\n      vpaStore.conversationHistory.splice(0)\n      emit('interaction', 'conversation-clear')\n    }\n\n    const quickAction = (action) => {\n      const actions = {\n        weather: '查询天气信息',\n        navigation: '打开导航助手',\n        music: '控制音乐播放',\n        schedule: '查看日程安排'\n      }\n      \n      vpaStore.addMessage('user', actions[action])\n      vpaStore.addMessage('vpa', `正在为您${actions[action]}...`)\n      \n      emit('quick-action', action)\n      emit('interaction', `quick-action-${action}`)\n    }\n\n    const formatTime = (timestamp) => {\n      return new Date(timestamp).toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    // 监听消息变化，自动滚动到底部\n    watch(() => vpaStore.conversationHistory.length, () => {\n      nextTick(() => {\n        if (messagesContainer.value) {\n          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight\n        }\n      })\n    })\n\n    return {\n      vpaStore,\n      messagesContainer,\n      isExpanded,\n      imageError,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      recentMessages,\n      emotionStatus,\n      panelStyles,\n      toggleExpanded,\n      switchMode,\n      toggleListening,\n      toggleSpeaking,\n      clearConversation,\n      quickAction,\n      formatTime,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-interaction-panel {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(15px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.vpa-interaction-panel.expanded {\n  transform: scale(1.02);\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);\n}\n\n/* 面板头部 */\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.vpa-avatar-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.avatar-container {\n  position: relative;\n  width: 48px;\n  height: 48px;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid rgba(74, 144, 226, 0.5);\n}\n\n.expression-overlay {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 18px;\n  height: 18px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.status-indicator {\n  position: absolute;\n  top: -6px;\n  right: -6px;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: white;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 2px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.3); }\n}\n\n.vpa-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.vpa-name {\n  color: white;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.vpa-status {\n  display: flex;\n  gap: 6px;\n}\n\n.mode-badge,\n.scene-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.scene-badge {\n  background: rgba(156, 39, 176, 0.8);\n}\n\n.panel-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n}\n\n.control-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: scale(1.1);\n}\n\n/* 面板主体 */\n.panel-body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 16px;\n  gap: 16px;\n  min-height: 0;\n}\n\n/* 对话区域 */\n.conversation-area {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.conversation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.conversation-title {\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.clear-btn {\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  font-size: 12px;\n  transition: color 0.3s ease;\n}\n\n.clear-btn:hover {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.messages-container {\n  flex: 1;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  min-height: 0;\n  max-height: 200px;\n}\n\n.message-item {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.message-content {\n  padding: 6px 10px;\n  border-radius: 12px;\n  font-size: 11px;\n  line-height: 1.4;\n  max-width: 80%;\n}\n\n.user-message .message-content {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  align-self: flex-end;\n}\n\n.vpa-message .message-content {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  align-self: flex-start;\n}\n\n.message-time {\n  font-size: 9px;\n  color: rgba(255, 255, 255, 0.5);\n  align-self: flex-end;\n}\n\n.vpa-message .message-time {\n  align-self: flex-start;\n}\n\n.empty-conversation {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  color: rgba(255, 255, 255, 0.5);\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 24px;\n  margin-bottom: 8px;\n}\n\n.empty-text {\n  font-size: 12px;\n}\n\n/* 交互控制区 */\n.interaction-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.voice-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.voice-btn {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.voice-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.voice-btn.active {\n  background: rgba(74, 144, 226, 0.8);\n  border-color: rgba(74, 144, 226, 1);\n}\n\n.btn-icon {\n  font-size: 14px;\n}\n\n.btn-text {\n  font-size: 11px;\n}\n\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 8px;\n}\n\n.quick-btn {\n  aspect-ratio: 1;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.quick-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: scale(1.05);\n}\n\n/* 情绪感知指示器 */\n.emotion-indicator {\n  position: absolute;\n  bottom: 12px;\n  right: 12px;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(10px);\n  border-radius: 8px;\n  padding: 4px 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2px;\n}\n\n.emotion-label {\n  font-size: 8px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.emotion-status {\n  font-size: 10px;\n  color: rgba(76, 175, 80, 0.9);\n  font-weight: 600;\n}\n\n/* 响应式适配 */\n@media (max-width: 768px) {\n  .panel-header {\n    padding: 10px 12px;\n  }\n  \n  .avatar-container {\n    width: 40px;\n    height: 40px;\n  }\n  \n  .vpa-name {\n    font-size: 12px;\n  }\n  \n  .panel-body {\n    padding: 12px;\n    gap: 12px;\n  }\n  \n  .voice-btn {\n    padding: 6px 8px;\n  }\n  \n  .btn-text {\n    font-size: 10px;\n  }\n  \n  .quick-btn {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";;EAWSA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAkB;;;EAOtBA,KAAK,EAAC;AAAoB;;;EAGEA,KAAK,EAAC;;;;EAIFA,KAAK,EAAC;;;EAMxCA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;EAK1BA,KAAK,EAAC;AAAgB;;;EAmBxBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAqB;;EAW3BA,KAAK,EAAC,oBAAoB;EAACC,GAAG,EAAC;;;EAO3BD,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAc;;;EAGaA,KAAK,EAAC;;;EAQ7CA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAgB;;EAMjBA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAU;;EAQhBA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAU;;EAIrBA,KAAK,EAAC;AAAe;;;EAkCGA,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAgB;;uBAtJ/BE,mBAAA,CAwJM;IAvJJF,KAAK,EAAAG,eAAA,EAAC,uBAAuB,G,QACLC,MAAA,CAAAC,IAAI,I,QAAkBC,MAAA,CAAAC,QAAQ,CAACC,WAAW,I;gBAAsBF,MAAA,CAAAC,QAAQ,CAACE,QAAQ;MAAA,YAAcH,MAAA,CAAAI;IAAU,E;IAKhIC,KAAK,EAAAC,eAAA,CAAEN,MAAA,CAAAO,WAAW;MAEnBC,mBAAA,UAAa,EACbC,mBAAA,CA+CM,OA/CNC,UA+CM,GA9CJD,mBAAA,CA4BM,OA5BNE,UA4BM,GA3BJF,mBAAA,CAkBM,OAlBNG,UAkBM,GAjBJH,mBAAA,CAKE;IAJCI,GAAG,WAAWb,MAAA,CAAAC,QAAQ,CAACa,MAAM;IAC7BC,GAAG,SAASf,MAAA,CAAAC,QAAQ,CAACe,iBAAiB;IACvCtB,KAAK,EAAC,cAAc;IACnBuB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAoB,gBAAA,IAAApB,MAAA,CAAAoB,gBAAA,IAAAD,IAAA,CAAgB;wDAE1BV,mBAAA,CAA2D,OAA3DY,UAA2D,EAAAC,gBAAA,CAAxBtB,MAAA,CAAAuB,eAAe,kBAElDf,mBAAA,WAAc,EACHR,MAAA,CAAAC,QAAQ,CAACuB,WAAW,I,cAA/B5B,mBAAA,CAGM,OAHN6B,UAGM,EAAAP,MAAA,SAAAA,MAAA,QAFJT,mBAAA,CAA8B;IAAzBf,KAAK,EAAC;EAAY,2B,iBAAO,MAEhC,mB,MACgBM,MAAA,CAAAC,QAAQ,CAACyB,UAAU,I,cAAnC9B,mBAAA,CAGM,OAHN+B,UAGM,EAAAT,MAAA,SAAAA,MAAA,QAFJT,mBAAA,CAAkC;IAA7Bf,KAAK,EAAC;EAAgB,2B,iBAAO,MAEpC,mB,2CAGFe,mBAAA,CAMM,OANNmB,UAMM,G,4BALJnB,mBAAA,CAAgC;IAA3Bf,KAAK,EAAC;EAAU,GAAC,MAAI,qBAC1Be,mBAAA,CAGM,OAHNoB,UAGM,GAFJpB,mBAAA,CAA8C,QAA9CqB,WAA8C,EAAAR,gBAAA,CAAlBtB,MAAA,CAAA+B,QAAQ,kBACpCtB,mBAAA,CAAgD,QAAhDuB,WAAgD,EAAAV,gBAAA,CAAnBtB,MAAA,CAAAiC,SAAS,iB,OAK5CxB,mBAAA,CAeM,OAfNyB,WAeM,GAdJzB,mBAAA,CAMS;IALPf,KAAK,EAAC,aAAa;IAClByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAoC,cAAA,IAAApC,MAAA,CAAAoC,cAAA,IAAAjB,IAAA,CAAc;IACrBkB,KAAK,EAAErC,MAAA,CAAAI,UAAU;sBAEfJ,MAAA,CAAAI,UAAU,sCAAAkC,WAAA,GAEf7B,mBAAA,CAMS;IALPf,KAAK,EAAC,aAAa;IAClByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAuC,UAAA,IAAAvC,MAAA,CAAAuC,UAAA,IAAApB,IAAA,CAAU;IAClBkB,KAAK,EAAC;KACP,MAED,E,KAIJ7B,mBAAA,YAAe,EACfC,mBAAA,CAqFM,OArFN+B,WAqFM,GApFJhC,mBAAA,UAAa,EACbC,mBAAA,CA4BM,OA5BNgC,WA4BM,GA3BJhC,mBAAA,CASM,OATNiC,WASM,G,4BARJjC,mBAAA,CAA4C;IAAtCf,KAAK,EAAC;EAAoB,GAAC,MAAI,qBACrCe,mBAAA,CAMS;IALPf,KAAK,EAAC,WAAW;IAChByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAA2C,iBAAA,IAAA3C,MAAA,CAAA2C,iBAAA,IAAAxB,IAAA,CAAiB;IACzBkB,KAAK,EAAC;KACP,OAED,E,GAGF5B,mBAAA,CAeM,OAfNmC,WAeM,I,kBAdJhD,mBAAA,CAQMiD,SAAA,QAAAC,WAAA,CAPuB9C,MAAA,CAAA+C,cAAc,GAAjCC,OAAO,EAAEC,KAAK;yBADxBrD,mBAAA,CAQM;MANHsD,GAAG,EAAED,KAAK;MACXvD,KAAK,EAAAG,eAAA,EAAC,cAAc;QAAA,gBACMmD,OAAO,CAACG,IAAI;QAAA,eAA4BH,OAAO,CAACG,IAAI;MAAA;QAE9E1C,mBAAA,CAAwD,OAAxD2C,WAAwD,EAAA9B,gBAAA,CAAxB0B,OAAO,CAACK,OAAO,kBAC/C5C,mBAAA,CAAmE,OAAnE6C,WAAmE,EAAAhC,gBAAA,CAAtCtB,MAAA,CAAAuD,UAAU,CAACP,OAAO,CAACQ,SAAS,kB;kCAGhDxD,MAAA,CAAA+C,cAAc,CAACU,MAAM,U,cAAhC7D,mBAAA,CAGM,OAHN8D,WAGM,EAAAxC,MAAA,SAAAA,MAAA,QAFJT,mBAAA,CAAgC;IAA3Bf,KAAK,EAAC;EAAY,GAAC,IAAE,oBAC1Be,mBAAA,CAAmC;IAA9Bf,KAAK,EAAC;EAAY,GAAC,OAAK,mB,mEAKnCc,mBAAA,WAAc,EACdC,mBAAA,CAmDM,OAnDNkD,WAmDM,GAlDJlD,mBAAA,CAkBM,OAlBNmD,WAkBM,GAjBJnD,mBAAA,CAOS;IANPf,KAAK,EAAAG,eAAA,EAAC,WAAW;MAAAgE,MAAA,EACC7D,MAAA,CAAAC,QAAQ,CAACuB;IAAW;IACrCW,OAAK,EAAAjB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAA8D,eAAA,IAAA9D,MAAA,CAAA8D,eAAA,IAAA3C,IAAA,CAAe;MAEvBV,mBAAA,CAAsE,QAAtEsD,WAAsE,EAAAzC,gBAAA,CAA5CtB,MAAA,CAAAC,QAAQ,CAACuB,WAAW,gCAC9Cf,mBAAA,CAAsE,QAAtEuD,WAAsE,EAAA1C,gBAAA,CAA5CtB,MAAA,CAAAC,QAAQ,CAACuB,WAAW,+B,kBAGhDf,mBAAA,CAOS;IANPf,KAAK,EAAAG,eAAA,EAAC,WAAW;MAAAgE,MAAA,EACC7D,MAAA,CAAAC,QAAQ,CAACyB;IAAU;IACpCS,OAAK,EAAAjB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAiE,cAAA,IAAAjE,MAAA,CAAAiE,cAAA,IAAA9C,IAAA,CAAc;MAEtBV,mBAAA,CAAqE,QAArEyD,WAAqE,EAAA5C,gBAAA,CAA3CtB,MAAA,CAAAC,QAAQ,CAACyB,UAAU,gCAC7CjB,mBAAA,CAAqE,QAArE0D,WAAqE,EAAA7C,gBAAA,CAA3CtB,MAAA,CAAAC,QAAQ,CAACyB,UAAU,+B,oBAIjDjB,mBAAA,CA6BM,OA7BN2D,WA6BM,GA5BJ3D,mBAAA,CAMS;IALPf,KAAK,EAAC,WAAW;IAChByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAmD,MAAA,IAAErE,MAAA,CAAAsE,WAAW;IACnBjC,KAAK,EAAC;KACP,OAED,GACA5B,mBAAA,CAMS;IALPf,KAAK,EAAC,WAAW;IAChByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAmD,MAAA,IAAErE,MAAA,CAAAsE,WAAW;IACnBjC,KAAK,EAAC;KACP,OAED,GACA5B,mBAAA,CAMS;IALPf,KAAK,EAAC,WAAW;IAChByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAmD,MAAA,IAAErE,MAAA,CAAAsE,WAAW;IACnBjC,KAAK,EAAC;KACP,MAED,GACA5B,mBAAA,CAMS;IALPf,KAAK,EAAC,WAAW;IAChByC,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAmD,MAAA,IAAErE,MAAA,CAAAsE,WAAW;IACnBjC,KAAK,EAAC;KACP,MAED,E,OAKN7B,mBAAA,aAAgB,EACLV,MAAA,CAAAyE,oBAAoB,I,cAA/B3E,mBAAA,CAGM,OAHN4E,WAGM,G,4BAFJ/D,mBAAA,CAAqC;IAAhCf,KAAK,EAAC;EAAe,GAAC,MAAI,qBAC/Be,mBAAA,CAAqD,OAArDgE,WAAqD,EAAAnD,gBAAA,CAAtBtB,MAAA,CAAA0E,aAAa,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}