{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nconst TYPE_REQUEST = \"q\";\nconst TYPE_RESPONSE = \"s\";\nconst DEFAULT_TIMEOUT = 6e4;\nfunction defaultSerialize(i) {\n  return i;\n}\nconst defaultDeserialize = defaultSerialize;\nconst {\n  clearTimeout,\n  setTimeout\n} = globalThis;\nconst random = Math.random.bind(Math);\nfunction createBirpc(functions, options) {\n  const {\n    post,\n    on,\n    off = () => {},\n    eventNames = [],\n    serialize = defaultSerialize,\n    deserialize = defaultDeserialize,\n    resolver,\n    bind = \"rpc\",\n    timeout = DEFAULT_TIMEOUT\n  } = options;\n  const rpcPromiseMap = /* @__PURE__ */new Map();\n  let _promise;\n  let closed = false;\n  const rpc = new Proxy({}, {\n    get(_, method) {\n      if (method === \"$functions\") return functions;\n      if (method === \"$close\") return close;\n      if (method === \"$rejectPendingCalls\") {\n        return rejectPendingCalls;\n      }\n      if (method === \"$closed\") return closed;\n      if (method === \"then\" && !eventNames.includes(\"then\") && !(\"then\" in functions)) return void 0;\n      const sendEvent = (...args) => {\n        post(serialize({\n          m: method,\n          a: args,\n          t: TYPE_REQUEST\n        }));\n      };\n      if (eventNames.includes(method)) {\n        sendEvent.asEvent = sendEvent;\n        return sendEvent;\n      }\n      const sendCall = async (...args) => {\n        if (closed) throw new Error(`[birpc] rpc is closed, cannot call \"${method}\"`);\n        if (_promise) {\n          try {\n            await _promise;\n          } finally {\n            _promise = void 0;\n          }\n        }\n        return new Promise((resolve, reject) => {\n          const id = nanoid();\n          let timeoutId;\n          if (timeout >= 0) {\n            timeoutId = setTimeout(() => {\n              try {\n                const handleResult = options.onTimeoutError?.(method, args);\n                if (handleResult !== true) throw new Error(`[birpc] timeout on calling \"${method}\"`);\n              } catch (e) {\n                reject(e);\n              }\n              rpcPromiseMap.delete(id);\n            }, timeout);\n            if (typeof timeoutId === \"object\") timeoutId = timeoutId.unref?.();\n          }\n          rpcPromiseMap.set(id, {\n            resolve,\n            reject,\n            timeoutId,\n            method\n          });\n          post(serialize({\n            m: method,\n            a: args,\n            i: id,\n            t: \"q\"\n          }));\n        });\n      };\n      sendCall.asEvent = sendEvent;\n      return sendCall;\n    }\n  });\n  function close(customError) {\n    closed = true;\n    rpcPromiseMap.forEach(({\n      reject,\n      method\n    }) => {\n      const error = new Error(`[birpc] rpc is closed, cannot call \"${method}\"`);\n      if (customError) {\n        customError.cause ??= error;\n        return reject(customError);\n      }\n      reject(error);\n    });\n    rpcPromiseMap.clear();\n    off(onMessage);\n  }\n  function rejectPendingCalls(handler) {\n    const entries = Array.from(rpcPromiseMap.values());\n    const handlerResults = entries.map(({\n      method,\n      reject\n    }) => {\n      if (!handler) {\n        return reject(new Error(`[birpc]: rejected pending call \"${method}\".`));\n      }\n      return handler({\n        method,\n        reject\n      });\n    });\n    rpcPromiseMap.clear();\n    return handlerResults;\n  }\n  async function onMessage(data, ...extra) {\n    let msg;\n    try {\n      msg = deserialize(data);\n    } catch (e) {\n      if (options.onGeneralError?.(e) !== true) throw e;\n      return;\n    }\n    if (msg.t === TYPE_REQUEST) {\n      const {\n        m: method,\n        a: args\n      } = msg;\n      let result, error;\n      const fn = resolver ? resolver(method, functions[method]) : functions[method];\n      if (!fn) {\n        error = new Error(`[birpc] function \"${method}\" not found`);\n      } else {\n        try {\n          result = await fn.apply(bind === \"rpc\" ? rpc : functions, args);\n        } catch (e) {\n          error = e;\n        }\n      }\n      if (msg.i) {\n        if (error && options.onError) options.onError(error, method, args);\n        if (error && options.onFunctionError) {\n          if (options.onFunctionError(error, method, args) === true) return;\n        }\n        if (!error) {\n          try {\n            post(serialize({\n              t: TYPE_RESPONSE,\n              i: msg.i,\n              r: result\n            }), ...extra);\n            return;\n          } catch (e) {\n            error = e;\n            if (options.onGeneralError?.(e, method, args) !== true) throw e;\n          }\n        }\n        try {\n          post(serialize({\n            t: TYPE_RESPONSE,\n            i: msg.i,\n            e: error\n          }), ...extra);\n        } catch (e) {\n          if (options.onGeneralError?.(e, method, args) !== true) throw e;\n        }\n      }\n    } else {\n      const {\n        i: ack,\n        r: result,\n        e: error\n      } = msg;\n      const promise = rpcPromiseMap.get(ack);\n      if (promise) {\n        clearTimeout(promise.timeoutId);\n        if (error) promise.reject(error);else promise.resolve(result);\n      }\n      rpcPromiseMap.delete(ack);\n    }\n  }\n  _promise = on(onMessage);\n  return rpc;\n}\nconst cacheMap = /* @__PURE__ */new WeakMap();\nfunction cachedMap(items, fn) {\n  return items.map(i => {\n    let r = cacheMap.get(i);\n    if (!r) {\n      r = fn(i);\n      cacheMap.set(i, r);\n    }\n    return r;\n  });\n}\nfunction createBirpcGroup(functions, channels, options = {}) {\n  const getChannels = () => typeof channels === \"function\" ? channels() : channels;\n  const getClients = (channels2 = getChannels()) => cachedMap(channels2, s => createBirpc(functions, {\n    ...options,\n    ...s\n  }));\n  const broadcastProxy = new Proxy({}, {\n    get(_, method) {\n      const client = getClients();\n      const callbacks = client.map(c => c[method]);\n      const sendCall = (...args) => {\n        return Promise.all(callbacks.map(i => i(...args)));\n      };\n      sendCall.asEvent = (...args) => {\n        callbacks.map(i => i.asEvent(...args));\n      };\n      return sendCall;\n    }\n  });\n  function updateChannels(fn) {\n    const channels2 = getChannels();\n    fn?.(channels2);\n    return getClients(channels2);\n  }\n  getClients();\n  return {\n    get clients() {\n      return getClients();\n    },\n    functions,\n    updateChannels,\n    broadcast: broadcastProxy,\n    /**\n     * @deprecated use `broadcast`\n     */\n    // @ts-expect-error deprecated\n    boardcast: broadcastProxy\n  };\n}\nconst urlAlphabet = \"useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict\";\nfunction nanoid(size = 21) {\n  let id = \"\";\n  let i = size;\n  while (i--) id += urlAlphabet[random() * 64 | 0];\n  return id;\n}\nexport { DEFAULT_TIMEOUT, cachedMap, createBirpc, createBirpcGroup };", "map": {"version": 3, "names": ["TYPE_REQUEST", "TYPE_RESPONSE", "DEFAULT_TIMEOUT", "defaultSerialize", "i", "defaultDeserialize", "clearTimeout", "setTimeout", "globalThis", "random", "Math", "bind", "createBirpc", "functions", "options", "post", "on", "off", "eventNames", "serialize", "deserialize", "resolver", "timeout", "rpcPromiseMap", "Map", "_promise", "closed", "rpc", "Proxy", "get", "_", "method", "close", "rejectPendingCalls", "includes", "sendEvent", "args", "m", "a", "t", "asEvent", "sendCall", "Error", "Promise", "resolve", "reject", "id", "nanoid", "timeoutId", "handleResult", "onTimeoutError", "e", "delete", "unref", "set", "customError", "for<PERSON>ach", "error", "cause", "clear", "onMessage", "handler", "entries", "Array", "from", "values", "handlerResults", "map", "data", "extra", "msg", "onGeneralError", "result", "fn", "apply", "onError", "onFunctionError", "r", "ack", "promise", "cacheMap", "WeakMap", "cachedMap", "items", "createBirpcGroup", "channels", "getChannels", "getClients", "channels2", "s", "broadcastProxy", "client", "callbacks", "c", "all", "updateChannels", "clients", "broadcast", "boardcast", "url<PERSON>l<PERSON><PERSON>", "size"], "sources": ["F:/工作/theme/ai-hmi/node_modules/birpc/dist/index.mjs"], "sourcesContent": ["const TYPE_REQUEST = \"q\";\nconst TYPE_RESPONSE = \"s\";\nconst DEFAULT_TIMEOUT = 6e4;\nfunction defaultSerialize(i) {\n  return i;\n}\nconst defaultDeserialize = defaultSerialize;\nconst { clearTimeout, setTimeout } = globalThis;\nconst random = Math.random.bind(Math);\nfunction createBirpc(functions, options) {\n  const {\n    post,\n    on,\n    off = () => {\n    },\n    eventNames = [],\n    serialize = defaultSerialize,\n    deserialize = defaultDeserialize,\n    resolver,\n    bind = \"rpc\",\n    timeout = DEFAULT_TIMEOUT\n  } = options;\n  const rpcPromiseMap = /* @__PURE__ */ new Map();\n  let _promise;\n  let closed = false;\n  const rpc = new Proxy({}, {\n    get(_, method) {\n      if (method === \"$functions\")\n        return functions;\n      if (method === \"$close\")\n        return close;\n      if (method === \"$rejectPendingCalls\") {\n        return rejectPendingCalls;\n      }\n      if (method === \"$closed\")\n        return closed;\n      if (method === \"then\" && !eventNames.includes(\"then\") && !(\"then\" in functions))\n        return void 0;\n      const sendEvent = (...args) => {\n        post(serialize({ m: method, a: args, t: TYPE_REQUEST }));\n      };\n      if (eventNames.includes(method)) {\n        sendEvent.asEvent = sendEvent;\n        return sendEvent;\n      }\n      const sendCall = async (...args) => {\n        if (closed)\n          throw new Error(`[birpc] rpc is closed, cannot call \"${method}\"`);\n        if (_promise) {\n          try {\n            await _promise;\n          } finally {\n            _promise = void 0;\n          }\n        }\n        return new Promise((resolve, reject) => {\n          const id = nanoid();\n          let timeoutId;\n          if (timeout >= 0) {\n            timeoutId = setTimeout(() => {\n              try {\n                const handleResult = options.onTimeoutError?.(method, args);\n                if (handleResult !== true)\n                  throw new Error(`[birpc] timeout on calling \"${method}\"`);\n              } catch (e) {\n                reject(e);\n              }\n              rpcPromiseMap.delete(id);\n            }, timeout);\n            if (typeof timeoutId === \"object\")\n              timeoutId = timeoutId.unref?.();\n          }\n          rpcPromiseMap.set(id, { resolve, reject, timeoutId, method });\n          post(serialize({ m: method, a: args, i: id, t: \"q\" }));\n        });\n      };\n      sendCall.asEvent = sendEvent;\n      return sendCall;\n    }\n  });\n  function close(customError) {\n    closed = true;\n    rpcPromiseMap.forEach(({ reject, method }) => {\n      const error = new Error(`[birpc] rpc is closed, cannot call \"${method}\"`);\n      if (customError) {\n        customError.cause ??= error;\n        return reject(customError);\n      }\n      reject(error);\n    });\n    rpcPromiseMap.clear();\n    off(onMessage);\n  }\n  function rejectPendingCalls(handler) {\n    const entries = Array.from(rpcPromiseMap.values());\n    const handlerResults = entries.map(({ method, reject }) => {\n      if (!handler) {\n        return reject(new Error(`[birpc]: rejected pending call \"${method}\".`));\n      }\n      return handler({ method, reject });\n    });\n    rpcPromiseMap.clear();\n    return handlerResults;\n  }\n  async function onMessage(data, ...extra) {\n    let msg;\n    try {\n      msg = deserialize(data);\n    } catch (e) {\n      if (options.onGeneralError?.(e) !== true)\n        throw e;\n      return;\n    }\n    if (msg.t === TYPE_REQUEST) {\n      const { m: method, a: args } = msg;\n      let result, error;\n      const fn = resolver ? resolver(method, functions[method]) : functions[method];\n      if (!fn) {\n        error = new Error(`[birpc] function \"${method}\" not found`);\n      } else {\n        try {\n          result = await fn.apply(bind === \"rpc\" ? rpc : functions, args);\n        } catch (e) {\n          error = e;\n        }\n      }\n      if (msg.i) {\n        if (error && options.onError)\n          options.onError(error, method, args);\n        if (error && options.onFunctionError) {\n          if (options.onFunctionError(error, method, args) === true)\n            return;\n        }\n        if (!error) {\n          try {\n            post(serialize({ t: TYPE_RESPONSE, i: msg.i, r: result }), ...extra);\n            return;\n          } catch (e) {\n            error = e;\n            if (options.onGeneralError?.(e, method, args) !== true)\n              throw e;\n          }\n        }\n        try {\n          post(serialize({ t: TYPE_RESPONSE, i: msg.i, e: error }), ...extra);\n        } catch (e) {\n          if (options.onGeneralError?.(e, method, args) !== true)\n            throw e;\n        }\n      }\n    } else {\n      const { i: ack, r: result, e: error } = msg;\n      const promise = rpcPromiseMap.get(ack);\n      if (promise) {\n        clearTimeout(promise.timeoutId);\n        if (error)\n          promise.reject(error);\n        else\n          promise.resolve(result);\n      }\n      rpcPromiseMap.delete(ack);\n    }\n  }\n  _promise = on(onMessage);\n  return rpc;\n}\nconst cacheMap = /* @__PURE__ */ new WeakMap();\nfunction cachedMap(items, fn) {\n  return items.map((i) => {\n    let r = cacheMap.get(i);\n    if (!r) {\n      r = fn(i);\n      cacheMap.set(i, r);\n    }\n    return r;\n  });\n}\nfunction createBirpcGroup(functions, channels, options = {}) {\n  const getChannels = () => typeof channels === \"function\" ? channels() : channels;\n  const getClients = (channels2 = getChannels()) => cachedMap(channels2, (s) => createBirpc(functions, { ...options, ...s }));\n  const broadcastProxy = new Proxy({}, {\n    get(_, method) {\n      const client = getClients();\n      const callbacks = client.map((c) => c[method]);\n      const sendCall = (...args) => {\n        return Promise.all(callbacks.map((i) => i(...args)));\n      };\n      sendCall.asEvent = (...args) => {\n        callbacks.map((i) => i.asEvent(...args));\n      };\n      return sendCall;\n    }\n  });\n  function updateChannels(fn) {\n    const channels2 = getChannels();\n    fn?.(channels2);\n    return getClients(channels2);\n  }\n  getClients();\n  return {\n    get clients() {\n      return getClients();\n    },\n    functions,\n    updateChannels,\n    broadcast: broadcastProxy,\n    /**\n     * @deprecated use `broadcast`\n     */\n    // @ts-expect-error deprecated\n    boardcast: broadcastProxy\n  };\n}\nconst urlAlphabet = \"useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict\";\nfunction nanoid(size = 21) {\n  let id = \"\";\n  let i = size;\n  while (i--)\n    id += urlAlphabet[random() * 64 | 0];\n  return id;\n}\n\nexport { DEFAULT_TIMEOUT, cachedMap, createBirpc, createBirpcGroup };\n"], "mappings": ";;;AAAA,MAAMA,YAAY,GAAG,GAAG;AACxB,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,eAAe,GAAG,GAAG;AAC3B,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EAC3B,OAAOA,CAAC;AACV;AACA,MAAMC,kBAAkB,GAAGF,gBAAgB;AAC3C,MAAM;EAAEG,YAAY;EAAEC;AAAW,CAAC,GAAGC,UAAU;AAC/C,MAAMC,MAAM,GAAGC,IAAI,CAACD,MAAM,CAACE,IAAI,CAACD,IAAI,CAAC;AACrC,SAASE,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACvC,MAAM;IACJC,IAAI;IACJC,EAAE;IACFC,GAAG,GAAGA,CAAA,KAAM,CACZ,CAAC;IACDC,UAAU,GAAG,EAAE;IACfC,SAAS,GAAGhB,gBAAgB;IAC5BiB,WAAW,GAAGf,kBAAkB;IAChCgB,QAAQ;IACRV,IAAI,GAAG,KAAK;IACZW,OAAO,GAAGpB;EACZ,CAAC,GAAGY,OAAO;EACX,MAAMS,aAAa,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC/C,IAAIC,QAAQ;EACZ,IAAIC,MAAM,GAAG,KAAK;EAClB,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,EAAE;IACxBC,GAAGA,CAACC,CAAC,EAAEC,MAAM,EAAE;MACb,IAAIA,MAAM,KAAK,YAAY,EACzB,OAAOlB,SAAS;MAClB,IAAIkB,MAAM,KAAK,QAAQ,EACrB,OAAOC,KAAK;MACd,IAAID,MAAM,KAAK,qBAAqB,EAAE;QACpC,OAAOE,kBAAkB;MAC3B;MACA,IAAIF,MAAM,KAAK,SAAS,EACtB,OAAOL,MAAM;MACf,IAAIK,MAAM,KAAK,MAAM,IAAI,CAACb,UAAU,CAACgB,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,IAAIrB,SAAS,CAAC,EAC7E,OAAO,KAAK,CAAC;MACf,MAAMsB,SAAS,GAAGA,CAAC,GAAGC,IAAI,KAAK;QAC7BrB,IAAI,CAACI,SAAS,CAAC;UAAEkB,CAAC,EAAEN,MAAM;UAAEO,CAAC,EAAEF,IAAI;UAAEG,CAAC,EAAEvC;QAAa,CAAC,CAAC,CAAC;MAC1D,CAAC;MACD,IAAIkB,UAAU,CAACgB,QAAQ,CAACH,MAAM,CAAC,EAAE;QAC/BI,SAAS,CAACK,OAAO,GAAGL,SAAS;QAC7B,OAAOA,SAAS;MAClB;MACA,MAAMM,QAAQ,GAAG,MAAAA,CAAO,GAAGL,IAAI,KAAK;QAClC,IAAIV,MAAM,EACR,MAAM,IAAIgB,KAAK,CAAC,uCAAuCX,MAAM,GAAG,CAAC;QACnE,IAAIN,QAAQ,EAAE;UACZ,IAAI;YACF,MAAMA,QAAQ;UAChB,CAAC,SAAS;YACRA,QAAQ,GAAG,KAAK,CAAC;UACnB;QACF;QACA,OAAO,IAAIkB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtC,MAAMC,EAAE,GAAGC,MAAM,CAAC,CAAC;UACnB,IAAIC,SAAS;UACb,IAAI1B,OAAO,IAAI,CAAC,EAAE;YAChB0B,SAAS,GAAGzC,UAAU,CAAC,MAAM;cAC3B,IAAI;gBACF,MAAM0C,YAAY,GAAGnC,OAAO,CAACoC,cAAc,GAAGnB,MAAM,EAAEK,IAAI,CAAC;gBAC3D,IAAIa,YAAY,KAAK,IAAI,EACvB,MAAM,IAAIP,KAAK,CAAC,+BAA+BX,MAAM,GAAG,CAAC;cAC7D,CAAC,CAAC,OAAOoB,CAAC,EAAE;gBACVN,MAAM,CAACM,CAAC,CAAC;cACX;cACA5B,aAAa,CAAC6B,MAAM,CAACN,EAAE,CAAC;YAC1B,CAAC,EAAExB,OAAO,CAAC;YACX,IAAI,OAAO0B,SAAS,KAAK,QAAQ,EAC/BA,SAAS,GAAGA,SAAS,CAACK,KAAK,GAAG,CAAC;UACnC;UACA9B,aAAa,CAAC+B,GAAG,CAACR,EAAE,EAAE;YAAEF,OAAO;YAAEC,MAAM;YAAEG,SAAS;YAAEjB;UAAO,CAAC,CAAC;UAC7DhB,IAAI,CAACI,SAAS,CAAC;YAAEkB,CAAC,EAAEN,MAAM;YAAEO,CAAC,EAAEF,IAAI;YAAEhC,CAAC,EAAE0C,EAAE;YAAEP,CAAC,EAAE;UAAI,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC;MACJ,CAAC;MACDE,QAAQ,CAACD,OAAO,GAAGL,SAAS;MAC5B,OAAOM,QAAQ;IACjB;EACF,CAAC,CAAC;EACF,SAAST,KAAKA,CAACuB,WAAW,EAAE;IAC1B7B,MAAM,GAAG,IAAI;IACbH,aAAa,CAACiC,OAAO,CAAC,CAAC;MAAEX,MAAM;MAAEd;IAAO,CAAC,KAAK;MAC5C,MAAM0B,KAAK,GAAG,IAAIf,KAAK,CAAC,uCAAuCX,MAAM,GAAG,CAAC;MACzE,IAAIwB,WAAW,EAAE;QACfA,WAAW,CAACG,KAAK,KAAKD,KAAK;QAC3B,OAAOZ,MAAM,CAACU,WAAW,CAAC;MAC5B;MACAV,MAAM,CAACY,KAAK,CAAC;IACf,CAAC,CAAC;IACFlC,aAAa,CAACoC,KAAK,CAAC,CAAC;IACrB1C,GAAG,CAAC2C,SAAS,CAAC;EAChB;EACA,SAAS3B,kBAAkBA,CAAC4B,OAAO,EAAE;IACnC,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACzC,aAAa,CAAC0C,MAAM,CAAC,CAAC,CAAC;IAClD,MAAMC,cAAc,GAAGJ,OAAO,CAACK,GAAG,CAAC,CAAC;MAAEpC,MAAM;MAAEc;IAAO,CAAC,KAAK;MACzD,IAAI,CAACgB,OAAO,EAAE;QACZ,OAAOhB,MAAM,CAAC,IAAIH,KAAK,CAAC,mCAAmCX,MAAM,IAAI,CAAC,CAAC;MACzE;MACA,OAAO8B,OAAO,CAAC;QAAE9B,MAAM;QAAEc;MAAO,CAAC,CAAC;IACpC,CAAC,CAAC;IACFtB,aAAa,CAACoC,KAAK,CAAC,CAAC;IACrB,OAAOO,cAAc;EACvB;EACA,eAAeN,SAASA,CAACQ,IAAI,EAAE,GAAGC,KAAK,EAAE;IACvC,IAAIC,GAAG;IACP,IAAI;MACFA,GAAG,GAAGlD,WAAW,CAACgD,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOjB,CAAC,EAAE;MACV,IAAIrC,OAAO,CAACyD,cAAc,GAAGpB,CAAC,CAAC,KAAK,IAAI,EACtC,MAAMA,CAAC;MACT;IACF;IACA,IAAImB,GAAG,CAAC/B,CAAC,KAAKvC,YAAY,EAAE;MAC1B,MAAM;QAAEqC,CAAC,EAAEN,MAAM;QAAEO,CAAC,EAAEF;MAAK,CAAC,GAAGkC,GAAG;MAClC,IAAIE,MAAM,EAAEf,KAAK;MACjB,MAAMgB,EAAE,GAAGpD,QAAQ,GAAGA,QAAQ,CAACU,MAAM,EAAElB,SAAS,CAACkB,MAAM,CAAC,CAAC,GAAGlB,SAAS,CAACkB,MAAM,CAAC;MAC7E,IAAI,CAAC0C,EAAE,EAAE;QACPhB,KAAK,GAAG,IAAIf,KAAK,CAAC,qBAAqBX,MAAM,aAAa,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI;UACFyC,MAAM,GAAG,MAAMC,EAAE,CAACC,KAAK,CAAC/D,IAAI,KAAK,KAAK,GAAGgB,GAAG,GAAGd,SAAS,EAAEuB,IAAI,CAAC;QACjE,CAAC,CAAC,OAAOe,CAAC,EAAE;UACVM,KAAK,GAAGN,CAAC;QACX;MACF;MACA,IAAImB,GAAG,CAAClE,CAAC,EAAE;QACT,IAAIqD,KAAK,IAAI3C,OAAO,CAAC6D,OAAO,EAC1B7D,OAAO,CAAC6D,OAAO,CAAClB,KAAK,EAAE1B,MAAM,EAAEK,IAAI,CAAC;QACtC,IAAIqB,KAAK,IAAI3C,OAAO,CAAC8D,eAAe,EAAE;UACpC,IAAI9D,OAAO,CAAC8D,eAAe,CAACnB,KAAK,EAAE1B,MAAM,EAAEK,IAAI,CAAC,KAAK,IAAI,EACvD;QACJ;QACA,IAAI,CAACqB,KAAK,EAAE;UACV,IAAI;YACF1C,IAAI,CAACI,SAAS,CAAC;cAAEoB,CAAC,EAAEtC,aAAa;cAAEG,CAAC,EAAEkE,GAAG,CAAClE,CAAC;cAAEyE,CAAC,EAAEL;YAAO,CAAC,CAAC,EAAE,GAAGH,KAAK,CAAC;YACpE;UACF,CAAC,CAAC,OAAOlB,CAAC,EAAE;YACVM,KAAK,GAAGN,CAAC;YACT,IAAIrC,OAAO,CAACyD,cAAc,GAAGpB,CAAC,EAAEpB,MAAM,EAAEK,IAAI,CAAC,KAAK,IAAI,EACpD,MAAMe,CAAC;UACX;QACF;QACA,IAAI;UACFpC,IAAI,CAACI,SAAS,CAAC;YAAEoB,CAAC,EAAEtC,aAAa;YAAEG,CAAC,EAAEkE,GAAG,CAAClE,CAAC;YAAE+C,CAAC,EAAEM;UAAM,CAAC,CAAC,EAAE,GAAGY,KAAK,CAAC;QACrE,CAAC,CAAC,OAAOlB,CAAC,EAAE;UACV,IAAIrC,OAAO,CAACyD,cAAc,GAAGpB,CAAC,EAAEpB,MAAM,EAAEK,IAAI,CAAC,KAAK,IAAI,EACpD,MAAMe,CAAC;QACX;MACF;IACF,CAAC,MAAM;MACL,MAAM;QAAE/C,CAAC,EAAE0E,GAAG;QAAED,CAAC,EAAEL,MAAM;QAAErB,CAAC,EAAEM;MAAM,CAAC,GAAGa,GAAG;MAC3C,MAAMS,OAAO,GAAGxD,aAAa,CAACM,GAAG,CAACiD,GAAG,CAAC;MACtC,IAAIC,OAAO,EAAE;QACXzE,YAAY,CAACyE,OAAO,CAAC/B,SAAS,CAAC;QAC/B,IAAIS,KAAK,EACPsB,OAAO,CAAClC,MAAM,CAACY,KAAK,CAAC,CAAC,KAEtBsB,OAAO,CAACnC,OAAO,CAAC4B,MAAM,CAAC;MAC3B;MACAjD,aAAa,CAAC6B,MAAM,CAAC0B,GAAG,CAAC;IAC3B;EACF;EACArD,QAAQ,GAAGT,EAAE,CAAC4C,SAAS,CAAC;EACxB,OAAOjC,GAAG;AACZ;AACA,MAAMqD,QAAQ,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;AAC9C,SAASC,SAASA,CAACC,KAAK,EAAEV,EAAE,EAAE;EAC5B,OAAOU,KAAK,CAAChB,GAAG,CAAE/D,CAAC,IAAK;IACtB,IAAIyE,CAAC,GAAGG,QAAQ,CAACnD,GAAG,CAACzB,CAAC,CAAC;IACvB,IAAI,CAACyE,CAAC,EAAE;MACNA,CAAC,GAAGJ,EAAE,CAACrE,CAAC,CAAC;MACT4E,QAAQ,CAAC1B,GAAG,CAAClD,CAAC,EAAEyE,CAAC,CAAC;IACpB;IACA,OAAOA,CAAC;EACV,CAAC,CAAC;AACJ;AACA,SAASO,gBAAgBA,CAACvE,SAAS,EAAEwE,QAAQ,EAAEvE,OAAO,GAAG,CAAC,CAAC,EAAE;EAC3D,MAAMwE,WAAW,GAAGA,CAAA,KAAM,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ;EAChF,MAAME,UAAU,GAAGA,CAACC,SAAS,GAAGF,WAAW,CAAC,CAAC,KAAKJ,SAAS,CAACM,SAAS,EAAGC,CAAC,IAAK7E,WAAW,CAACC,SAAS,EAAE;IAAE,GAAGC,OAAO;IAAE,GAAG2E;EAAE,CAAC,CAAC,CAAC;EAC3H,MAAMC,cAAc,GAAG,IAAI9D,KAAK,CAAC,CAAC,CAAC,EAAE;IACnCC,GAAGA,CAACC,CAAC,EAAEC,MAAM,EAAE;MACb,MAAM4D,MAAM,GAAGJ,UAAU,CAAC,CAAC;MAC3B,MAAMK,SAAS,GAAGD,MAAM,CAACxB,GAAG,CAAE0B,CAAC,IAAKA,CAAC,CAAC9D,MAAM,CAAC,CAAC;MAC9C,MAAMU,QAAQ,GAAGA,CAAC,GAAGL,IAAI,KAAK;QAC5B,OAAOO,OAAO,CAACmD,GAAG,CAACF,SAAS,CAACzB,GAAG,CAAE/D,CAAC,IAAKA,CAAC,CAAC,GAAGgC,IAAI,CAAC,CAAC,CAAC;MACtD,CAAC;MACDK,QAAQ,CAACD,OAAO,GAAG,CAAC,GAAGJ,IAAI,KAAK;QAC9BwD,SAAS,CAACzB,GAAG,CAAE/D,CAAC,IAAKA,CAAC,CAACoC,OAAO,CAAC,GAAGJ,IAAI,CAAC,CAAC;MAC1C,CAAC;MACD,OAAOK,QAAQ;IACjB;EACF,CAAC,CAAC;EACF,SAASsD,cAAcA,CAACtB,EAAE,EAAE;IAC1B,MAAMe,SAAS,GAAGF,WAAW,CAAC,CAAC;IAC/Bb,EAAE,GAAGe,SAAS,CAAC;IACf,OAAOD,UAAU,CAACC,SAAS,CAAC;EAC9B;EACAD,UAAU,CAAC,CAAC;EACZ,OAAO;IACL,IAAIS,OAAOA,CAAA,EAAG;MACZ,OAAOT,UAAU,CAAC,CAAC;IACrB,CAAC;IACD1E,SAAS;IACTkF,cAAc;IACdE,SAAS,EAAEP,cAAc;IACzB;AACJ;AACA;IACI;IACAQ,SAAS,EAAER;EACb,CAAC;AACH;AACA,MAAMS,WAAW,GAAG,kEAAkE;AACtF,SAASpD,MAAMA,CAACqD,IAAI,GAAG,EAAE,EAAE;EACzB,IAAItD,EAAE,GAAG,EAAE;EACX,IAAI1C,CAAC,GAAGgG,IAAI;EACZ,OAAOhG,CAAC,EAAE,EACR0C,EAAE,IAAIqD,WAAW,CAAC1F,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACtC,OAAOqC,EAAE;AACX;AAEA,SAAS5C,eAAe,EAAEgF,SAAS,EAAEtE,WAAW,EAAEwE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}