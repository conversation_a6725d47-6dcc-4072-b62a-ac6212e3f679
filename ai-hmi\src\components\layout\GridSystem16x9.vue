<template>
  <div 
    :class="['grid-system-16x9', `layout-${layoutMode}`, { 'debug-mode': debugMode }]"
    :style="gridStyles"
  >
    <!-- 网格调试覆盖层 -->
    <div v-if="debugMode" class="debug-overlay">
      <div 
        v-for="(row, rowIndex) in 9" 
        :key="`row-${rowIndex}`"
        class="debug-row"
      >
        <div 
          v-for="(col, colIndex) in 16" 
          :key="`cell-${rowIndex}-${colIndex}`"
          class="debug-cell"
          :data-position="`${colIndex + 1},${rowIndex + 1}`"
        >
          {{ colIndex + 1 }},{{ rowIndex + 1 }}
        </div>
      </div>
    </div>

    <!-- 网格内容插槽 -->
    <slot :grid-config="gridConfig" :layout-mode="layoutMode">
      <!-- 默认内容 -->
      <div class="default-grid-content">
        <p>16x9 网格系统已就绪</p>
        <p>当前布局模式: {{ layoutMode }}</p>
      </div>
    </slot>
  </div>
</template>

<script>
import { computed, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '@/store'

export default {
  name: 'GridSystem16x9',
  props: {
    // 布局模式
    layoutMode: {
      type: String,
      default: 'default'
    },
    
    // 调试模式
    debugMode: {
      type: Boolean,
      default: false
    },
    
    // 自定义网格配置
    customConfig: {
      type: Object,
      default: () => ({})
    },
    
    // 响应式配置
    responsive: {
      type: Boolean,
      default: true
    },
    
    // 动画配置
    animated: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['layout-changed', 'grid-ready'],
  
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    
    // 计算网格样式
    const gridStyles = computed(() => {
      const baseConfig = props.responsive 
        ? layoutStore.responsiveGridConfig 
        : layoutStore.currentGridConfig
      
      const customConfig = props.customConfig
      
      return {
        display: 'grid',
        ...baseConfig,
        ...customConfig,
        transition: props.animated ? 'all 0.3s ease' : 'none'
      }
    })
    
    // 网格配置信息
    const gridConfig = computed(() => ({
      columns: 16,
      rows: 9,
      totalCells: 144,
      layoutMode: props.layoutMode,
      responsive: props.responsive,
      animated: props.animated
    }))
    
    // 处理窗口大小变化
    const handleResize = () => {
      if (props.responsive) {
        layoutStore.updateScreenSize(window.innerWidth)
        emit('layout-changed', {
          screenSize: layoutStore.screenSize,
          gridConfig: layoutStore.responsiveGridConfig
        })
      }
    }
    
    // 组件挂载时的初始化
    onMounted(() => {
      if (props.responsive) {
        window.addEventListener('resize', handleResize)
        handleResize() // 初始化屏幕尺寸
      }
      
      // 切换到指定布局模式
      if (props.layoutMode !== layoutStore.currentLayout) {
        layoutStore.switchLayout(props.layoutMode)
      }
      
      emit('grid-ready', gridConfig.value)
    })
    
    // 组件卸载时清理
    onUnmounted(() => {
      if (props.responsive) {
        window.removeEventListener('resize', handleResize)
      }
    })
    
    return {
      gridStyles,
      gridConfig,
      layoutStore
    }
  }
}
</script>

<style scoped>
.grid-system-16x9 {
  width: 100%;
  height: 100vh;
  min-height: 600px;
  position: relative;
  overflow: hidden;
}

/* 调试模式样式 */
.debug-mode {
  position: relative;
}

.debug-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9999;
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: var(--grid-gap, min(1vw, 10px));
  padding: var(--grid-padding, min(1vw, 10px));
}

.debug-cell {
  border: 1px solid rgba(255, 0, 0, 0.3);
  background: rgba(255, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: rgba(255, 0, 0, 0.7);
  font-weight: bold;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

/* 默认内容样式 */
.default-grid-content {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  gap: 10px;
}

.default-grid-content p {
  margin: 0;
  font-size: 18px;
}

/* 布局模式特定样式 */
.layout-family {
  /* 家庭出行模式的特定样式 */
}

.layout-focus {
  /* 专注通勤模式的特定样式 */
}

.layout-entertainment {
  /* 娱乐模式的特定样式 */
}

.layout-minimal {
  /* 极简模式的特定样式 */
}

.layout-immersive {
  /* 沉浸式模式的特定样式 */
  display: block; /* 沉浸式模式不使用网格 */
}

/* 响应式样式 */
@media (max-width: 768px) {
  .grid-system-16x9 {
    min-height: 500px;
  }
  
  .debug-overlay {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(12, 1fr);
  }
}

@media (max-width: 1024px) {
  .grid-system-16x9 {
    min-height: 550px;
  }
  
  .debug-overlay {
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(10, 1fr);
  }
}

/* 动画效果 */
.grid-system-16x9.animated {
  transition: all 0.3s ease;
}

.grid-system-16x9.animated > * {
  transition: all 0.3s ease;
}

/* 网格项目的通用样式 */
.grid-system-16x9 > :not(.debug-overlay):not(.default-grid-content) {
  min-height: 0;
  overflow: hidden;
}
</style>
