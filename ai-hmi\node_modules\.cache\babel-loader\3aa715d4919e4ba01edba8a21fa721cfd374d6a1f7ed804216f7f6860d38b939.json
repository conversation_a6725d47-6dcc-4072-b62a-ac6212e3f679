{"ast": null, "code": "import { ref, computed, onMounted } from 'vue';\nimport AsrService from '@/services/AsrService';\nimport TtsService from '@/services/TtsService';\nimport LlmService from '@/services/LlmService';\nexport default {\n  name: 'VoiceInteractionManager',\n  emits: ['wallpaper-prompt-ready', 'scene-switch-requested'],\n  setup(props, {\n    emit\n  }) {\n    const asrService = new AsrService();\n    const ttsService = new TtsService();\n    const llmService = new LlmService();\n    const isListening = ref(false);\n    const isProcessing = ref(false);\n    const isSpeaking = ref(false);\n    const recognitionResult = ref('');\n    const textInput = ref('');\n    const statusMessage = ref('');\n    const showTextInput = ref(false); // 默认隐藏文本输入框\n\n    const voiceState = computed(() => {\n      if (isSpeaking.value) return 'speaking';\n      if (isProcessing.value) return 'processing';\n      if (isListening.value) return 'listening';\n      return 'idle';\n    });\n    const getVoiceButtonIcon = computed(() => {\n      switch (voiceState.value) {\n        case 'speaking':\n          return 'fas fa-volume-up';\n        case 'processing':\n          return 'fas fa-spinner fa-spin';\n        case 'listening':\n          return 'fas fa-stop';\n        default:\n          return 'fas fa-microphone';\n      }\n    });\n    const toggleVoiceInteraction = async () => {\n      if (isListening.value) {\n        stopVoiceInteraction();\n      } else {\n        startVoiceInteraction();\n      }\n    };\n    const startVoiceInteraction = async () => {\n      if (isListening.value || isProcessing.value) return;\n      try {\n        isListening.value = true;\n        statusMessage.value = '正在聆听...';\n\n        // 使用ASR服务进行语音识别\n        const result = await asrService.startRecognition();\n        recognitionResult.value = result;\n\n        // 自动接受结果并处理\n        await processVoiceResult(result);\n      } catch (error) {\n        console.error('语音识别失败:', error);\n        statusMessage.value = '语音识别失败，请重试';\n\n        // 语音反馈\n        await ttsService.speak('语音识别失败，请重试或使用文本输入');\n      } finally {\n        isListening.value = false;\n        statusMessage.value = '';\n      }\n    };\n    const stopVoiceInteraction = () => {\n      if (asrService) {\n        asrService.stopRecognition();\n      }\n      if (ttsService) {\n        ttsService.stop();\n      }\n      isListening.value = false;\n      statusMessage.value = '';\n    };\n    const processVoiceResult = async userInput => {\n      isProcessing.value = true;\n      statusMessage.value = '正在处理...';\n      try {\n        // 首先尝试识别场景切换指令\n        statusMessage.value = '正在理解您的需求...';\n        const sceneResult = await llmService.detectSceneFromVoice(userInput);\n        if (sceneResult.sceneId && sceneResult.confidence > 0.6) {\n          // 场景切换逻辑\n          statusMessage.value = `正在切换到${getSceneName(sceneResult.sceneId)}...`;\n          await ttsService.speak(`正在为您切换到${getSceneName(sceneResult.sceneId)}`);\n\n          // 发送场景切换请求\n          emit('scene-switch-requested', {\n            sceneId: sceneResult.sceneId,\n            confidence: sceneResult.confidence,\n            reason: sceneResult.reason\n          });\n\n          // 完成反馈\n          setTimeout(async () => {\n            await ttsService.speak('场景切换完成');\n            statusMessage.value = '';\n          }, 2000);\n          return;\n        }\n\n        // 如果不是场景切换，则按原来的逻辑处理壁纸生成\n        const enhancedPrompt = await llmService.generateResponse(userInput);\n\n        // 语音反馈处理结果\n        statusMessage.value = '正在生成壁纸...';\n        await ttsService.speak('正在为您生成壁纸，请稍候');\n\n        // 发送最终提示词给主题管理器\n        emit('wallpaper-prompt-ready', enhancedPrompt);\n\n        // 完成反馈\n        setTimeout(async () => {\n          await ttsService.speak('壁纸生成完成');\n          statusMessage.value = '';\n        }, 3000);\n      } catch (error) {\n        console.error('处理语音输入失败:', error);\n        statusMessage.value = '处理失败，请重试';\n        await ttsService.speak('处理失败，请重试');\n\n        // 降级方案：直接使用原始输入\n        emit('wallpaper-prompt-ready', userInput);\n      } finally {\n        isProcessing.value = false;\n        recognitionResult.value = '';\n      }\n    };\n    const acceptResult = async () => {\n      if (recognitionResult.value.trim()) {\n        await processVoiceResult(recognitionResult.value);\n      }\n    };\n    const rejectResult = () => {\n      recognitionResult.value = '';\n      statusMessage.value = '';\n      ttsService.speak('请重新描述');\n    };\n    const submitTextInput = async () => {\n      if (textInput.value.trim()) {\n        await processVoiceResult(textInput.value);\n        textInput.value = '';\n      }\n    };\n    const toggleTextInput = () => {\n      showTextInput.value = !showTextInput.value;\n    };\n\n    // 获取场景的中文名称\n    const getSceneName = sceneId => {\n      const sceneNames = {\n        morningCommuteFamily: '家庭出行模式',\n        morningCommuteFocus: '专注通勤模式',\n        eveningCommute: '下班通勤模式',\n        waitingMode: '等待休息模式',\n        rainyNight: '雨夜模式',\n        familyTrip: '家庭出游模式',\n        longDistance: '长途驾驶模式',\n        guestMode: '访客模式',\n        petMode: '宠物模式',\n        carWashMode: '洗车模式',\n        romanticMode: '浪漫模式',\n        chargingMode: '充电模式',\n        fatigueDetection: '疲劳检测模式',\n        userSwitch: '用户切换模式',\n        parkingMode: '泊车模式',\n        emergencyMode: '紧急模式'\n      };\n      return sceneNames[sceneId] || sceneId;\n    };\n\n    // 初始化TTS语音\n    onMounted(() => {\n      // 等待语音列表加载\n      if (ttsService.isSupported) {\n        setTimeout(() => {\n          ttsService.getVoices();\n        }, 100);\n      }\n    });\n    return {\n      isListening,\n      isProcessing,\n      isSpeaking,\n      recognitionResult,\n      textInput,\n      statusMessage,\n      showTextInput,\n      voiceState,\n      getVoiceButtonIcon,\n      toggleVoiceInteraction,\n      toggleTextInput,\n      acceptResult,\n      rejectResult,\n      submitTextInput,\n      getSceneName\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "AsrService", "TtsService", "LlmService", "name", "emits", "setup", "props", "emit", "asrService", "ttsService", "llmService", "isListening", "isProcessing", "isSpeaking", "recognitionResult", "textInput", "statusMessage", "showTextInput", "voiceState", "value", "getVoiceButtonIcon", "toggleVoiceInteraction", "stopVoiceInteraction", "startVoiceInteraction", "result", "startRecognition", "processVoiceResult", "error", "console", "speak", "stopRecognition", "stop", "userInput", "sceneResult", "detectSceneFromVoice", "sceneId", "confidence", "getSceneName", "reason", "setTimeout", "enhancedPrompt", "generateResponse", "acceptResult", "trim", "rejectResult", "submitTextInput", "toggleTextInput", "sceneNames", "morningCommuteFamily", "morningCommuteFocus", "eveningCommute", "waitingMode", "rainyNight", "familyTrip", "longDistance", "<PERSON><PERSON><PERSON>", "petMode", "carWashMode", "<PERSON><PERSON><PERSON>", "chargingMode", "fatigueDetection", "userSwitch", "parkingMode", "emergencyMode", "isSupported", "getVoices"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"voice-interaction-manager\">\r\n    <!-- 语音输入按钮 -->\r\n    <button\r\n      @click=\"toggleVoiceInteraction\"\r\n      :class=\"['voice-btn', {\r\n        listening: isListening,\r\n        processing: isProcessing,\r\n        speaking: isSpeaking\r\n      }]\"\r\n    >\r\n      <i :class=\"getVoiceButtonIcon\"></i>\r\n    </button>\r\n\r\n    <!-- 文本输入切换按钮 -->\r\n    <button\r\n      @click=\"toggleTextInput\"\r\n      class=\"text-toggle-btn\"\r\n      :title=\"showTextInput ? '隐藏文本输入' : '显示文本输入'\"\r\n    >\r\n      <i :class=\"showTextInput ? 'fas fa-keyboard' : 'fas fa-edit'\"></i>\r\n    </button>\r\n\r\n    <!-- 识别结果显示 -->\r\n    <div v-if=\"recognitionResult\" class=\"recognition-result\">\r\n      <div class=\"result-text\">{{ recognitionResult }}</div>\r\n      <div class=\"result-actions\">\r\n        <button @click=\"acceptResult\" class=\"action-btn accept\">\r\n          <i class=\"fas fa-check\"></i>\r\n        </button>\r\n        <button @click=\"rejectResult\" class=\"action-btn reject\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 处理状态显示 -->\r\n    <div v-if=\"statusMessage\" class=\"status-message\">\r\n      <i class=\"fas fa-info-circle\"></i>\r\n      {{ statusMessage }}\r\n    </div>\r\n\r\n    <!-- 文本输入备用方案 -->\r\n    <div v-if=\"showTextInput\" class=\"text-input-fallback\">\r\n      <input\r\n        v-model=\"textInput\"\r\n        @keyup.enter=\"submitTextInput\"\r\n        placeholder=\"或直接输入描述...\"\r\n        class=\"text-input-field\"\r\n      />\r\n      <button @click=\"submitTextInput\" class=\"submit-btn\">\r\n        <i class=\"fas fa-paper-plane\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport AsrService from '@/services/AsrService'\r\nimport TtsService from '@/services/TtsService'\r\nimport LlmService from '@/services/LlmService'\r\n\r\nexport default {\r\n  name: 'VoiceInteractionManager',\r\n  emits: ['wallpaper-prompt-ready', 'scene-switch-requested'],\r\n\r\n  setup(props, { emit }) {\r\n    const asrService = new AsrService()\r\n    const ttsService = new TtsService()\r\n    const llmService = new LlmService()\r\n\r\n    const isListening = ref(false)\r\n    const isProcessing = ref(false)\r\n    const isSpeaking = ref(false)\r\n    const recognitionResult = ref('')\r\n    const textInput = ref('')\r\n    const statusMessage = ref('')\r\n    const showTextInput = ref(false) // 默认隐藏文本输入框\r\n\r\n    const voiceState = computed(() => {\r\n      if (isSpeaking.value) return 'speaking'\r\n      if (isProcessing.value) return 'processing'\r\n      if (isListening.value) return 'listening'\r\n      return 'idle'\r\n    })\r\n\r\n    const getVoiceButtonIcon = computed(() => {\r\n      switch (voiceState.value) {\r\n        case 'speaking':\r\n          return 'fas fa-volume-up'\r\n        case 'processing':\r\n          return 'fas fa-spinner fa-spin'\r\n        case 'listening':\r\n          return 'fas fa-stop'\r\n        default:\r\n          return 'fas fa-microphone'\r\n      }\r\n    })\r\n\r\n    const toggleVoiceInteraction = async () => {\r\n      if (isListening.value) {\r\n        stopVoiceInteraction()\r\n      } else {\r\n        startVoiceInteraction()\r\n      }\r\n    }\r\n\r\n    const startVoiceInteraction = async () => {\r\n      if (isListening.value || isProcessing.value) return\r\n\r\n      try {\r\n        isListening.value = true\r\n        statusMessage.value = '正在聆听...'\r\n        \r\n        // 使用ASR服务进行语音识别\r\n        const result = await asrService.startRecognition()\r\n        recognitionResult.value = result\r\n        \r\n        // 自动接受结果并处理\r\n        await processVoiceResult(result)\r\n        \r\n      } catch (error) {\r\n        console.error('语音识别失败:', error)\r\n        statusMessage.value = '语音识别失败，请重试'\r\n        \r\n        // 语音反馈\r\n        await ttsService.speak('语音识别失败，请重试或使用文本输入')\r\n      } finally {\r\n        isListening.value = false\r\n        statusMessage.value = ''\r\n      }\r\n    }\r\n\r\n    const stopVoiceInteraction = () => {\r\n      if (asrService) {\r\n        asrService.stopRecognition()\r\n      }\r\n      if (ttsService) {\r\n        ttsService.stop()\r\n      }\r\n      isListening.value = false\r\n      statusMessage.value = ''\r\n    }\r\n\r\n    const processVoiceResult = async (userInput) => {\r\n      isProcessing.value = true\r\n      statusMessage.value = '正在处理...'\r\n\r\n      try {\r\n        // 首先尝试识别场景切换指令\r\n        statusMessage.value = '正在理解您的需求...'\r\n        const sceneResult = await llmService.detectSceneFromVoice(userInput)\r\n        \r\n        if (sceneResult.sceneId && sceneResult.confidence > 0.6) {\r\n          // 场景切换逻辑\r\n          statusMessage.value = `正在切换到${getSceneName(sceneResult.sceneId)}...`\r\n          await ttsService.speak(`正在为您切换到${getSceneName(sceneResult.sceneId)}`)\r\n          \r\n          // 发送场景切换请求\r\n          emit('scene-switch-requested', {\r\n            sceneId: sceneResult.sceneId,\r\n            confidence: sceneResult.confidence,\r\n            reason: sceneResult.reason\r\n          })\r\n          \r\n          // 完成反馈\r\n          setTimeout(async () => {\r\n            await ttsService.speak('场景切换完成')\r\n            statusMessage.value = ''\r\n          }, 2000)\r\n          \r\n          return\r\n        }\r\n        \r\n        // 如果不是场景切换，则按原来的逻辑处理壁纸生成\r\n        const enhancedPrompt = await llmService.generateResponse(userInput)\r\n        \r\n        // 语音反馈处理结果\r\n        statusMessage.value = '正在生成壁纸...'\r\n        await ttsService.speak('正在为您生成壁纸，请稍候')\r\n        \r\n        // 发送最终提示词给主题管理器\r\n        emit('wallpaper-prompt-ready', enhancedPrompt)\r\n        \r\n        // 完成反馈\r\n        setTimeout(async () => {\r\n          await ttsService.speak('壁纸生成完成')\r\n          statusMessage.value = ''\r\n        }, 3000)\r\n        \r\n      } catch (error) {\r\n        console.error('处理语音输入失败:', error)\r\n        statusMessage.value = '处理失败，请重试'\r\n        \r\n        await ttsService.speak('处理失败，请重试')\r\n        \r\n        // 降级方案：直接使用原始输入\r\n        emit('wallpaper-prompt-ready', userInput)\r\n      } finally {\r\n        isProcessing.value = false\r\n        recognitionResult.value = ''\r\n      }\r\n    }\r\n\r\n    const acceptResult = async () => {\r\n      if (recognitionResult.value.trim()) {\r\n        await processVoiceResult(recognitionResult.value)\r\n      }\r\n    }\r\n\r\n    const rejectResult = () => {\r\n      recognitionResult.value = ''\r\n      statusMessage.value = ''\r\n      ttsService.speak('请重新描述')\r\n    }\r\n\r\n    const submitTextInput = async () => {\r\n      if (textInput.value.trim()) {\r\n        await processVoiceResult(textInput.value)\r\n        textInput.value = ''\r\n      }\r\n    }\r\n\r\n    const toggleTextInput = () => {\r\n      showTextInput.value = !showTextInput.value\r\n    }\r\n\r\n    // 获取场景的中文名称\r\n    const getSceneName = (sceneId) => {\r\n      const sceneNames = {\r\n        morningCommuteFamily: '家庭出行模式',\r\n        morningCommuteFocus: '专注通勤模式',\r\n        eveningCommute: '下班通勤模式',\r\n        waitingMode: '等待休息模式',\r\n        rainyNight: '雨夜模式',\r\n        familyTrip: '家庭出游模式',\r\n        longDistance: '长途驾驶模式',\r\n        guestMode: '访客模式',\r\n        petMode: '宠物模式',\r\n        carWashMode: '洗车模式',\r\n        romanticMode: '浪漫模式',\r\n        chargingMode: '充电模式',\r\n        fatigueDetection: '疲劳检测模式',\r\n        userSwitch: '用户切换模式',\r\n        parkingMode: '泊车模式',\r\n        emergencyMode: '紧急模式'\r\n      }\r\n      return sceneNames[sceneId] || sceneId\r\n    }\r\n\r\n    // 初始化TTS语音\r\n    onMounted(() => {\r\n      // 等待语音列表加载\r\n      if (ttsService.isSupported) {\r\n        setTimeout(() => {\r\n          ttsService.getVoices()\r\n        }, 100)\r\n      }\r\n    })\r\n\r\n    return {\r\n      isListening,\r\n      isProcessing,\r\n      isSpeaking,\r\n      recognitionResult,\r\n      textInput,\r\n      statusMessage,\r\n      showTextInput,\r\n      voiceState,\r\n      getVoiceButtonIcon,\r\n      toggleVoiceInteraction,\r\n      toggleTextInput,\r\n      acceptResult,\r\n      rejectResult,\r\n      submitTextInput,\r\n      getSceneName\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.voice-interaction-manager {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 20px;\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 10px;\r\n  max-width: 420px;\r\n}\r\n\r\n.voice-btn {\r\n  width: 64px;\r\n  height: 64px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(12px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.voice-btn:hover {\r\n  transform: scale(1.1);\r\n  background: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.voice-btn.listening {\r\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.voice-btn.processing {\r\n  background: linear-gradient(45deg, #4834d4, #686de0);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.voice-btn.speaking {\r\n  background: linear-gradient(45deg, #00d2d3, #54a0ff);\r\n  animation: wave 2s infinite;\r\n}\r\n\r\n.text-toggle-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: none;\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  font-size: 16px;\r\n}\r\n\r\n.text-toggle-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.recognition-result {\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(12px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.result-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.result-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.accept {\r\n  background: #2ed573;\r\n  color: white;\r\n}\r\n\r\n.action-btn.reject {\r\n  background: #ff4757;\r\n  color: white;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.status-message {\r\n  padding: 12px 15px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 10px;\r\n  font-size: 13px;\r\n  color: #333;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.text-input-fallback {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.text-input-field {\r\n  flex: 1;\r\n  padding: 12px 18px;\r\n  border: none;\r\n  border-radius: 25px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  font-size: 14px;\r\n  outline: none;\r\n  backdrop-filter: blur(10px);\r\n  color: #333;\r\n}\r\n\r\n.submit-btn {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { transform: scale(1); }\r\n  25% { transform: scale(1.02); }\r\n  75% { transform: scale(1.02); }\r\n}\r\n</style>"], "mappings": "AA0DA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,OAAOC,UAAS,MAAO,uBAAsB;AAC7C,OAAOC,UAAS,MAAO,uBAAsB;AAC7C,OAAOC,UAAS,MAAO,uBAAsB;AAE7C,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAC/BC,KAAK,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;EAE3DC,KAAKA,CAACC,KAAK,EAAE;IAAEC;EAAK,CAAC,EAAE;IACrB,MAAMC,UAAS,GAAI,IAAIR,UAAU,CAAC;IAClC,MAAMS,UAAS,GAAI,IAAIR,UAAU,CAAC;IAClC,MAAMS,UAAS,GAAI,IAAIR,UAAU,CAAC;IAElC,MAAMS,WAAU,GAAId,GAAG,CAAC,KAAK;IAC7B,MAAMe,YAAW,GAAIf,GAAG,CAAC,KAAK;IAC9B,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,KAAK;IAC5B,MAAMiB,iBAAgB,GAAIjB,GAAG,CAAC,EAAE;IAChC,MAAMkB,SAAQ,GAAIlB,GAAG,CAAC,EAAE;IACxB,MAAMmB,aAAY,GAAInB,GAAG,CAAC,EAAE;IAC5B,MAAMoB,aAAY,GAAIpB,GAAG,CAAC,KAAK,GAAE;;IAEjC,MAAMqB,UAAS,GAAIpB,QAAQ,CAAC,MAAM;MAChC,IAAIe,UAAU,CAACM,KAAK,EAAE,OAAO,UAAS;MACtC,IAAIP,YAAY,CAACO,KAAK,EAAE,OAAO,YAAW;MAC1C,IAAIR,WAAW,CAACQ,KAAK,EAAE,OAAO,WAAU;MACxC,OAAO,MAAK;IACd,CAAC;IAED,MAAMC,kBAAiB,GAAItB,QAAQ,CAAC,MAAM;MACxC,QAAQoB,UAAU,CAACC,KAAK;QACtB,KAAK,UAAU;UACb,OAAO,kBAAiB;QAC1B,KAAK,YAAY;UACf,OAAO,wBAAuB;QAChC,KAAK,WAAW;UACd,OAAO,aAAY;QACrB;UACE,OAAO,mBAAkB;MAC7B;IACF,CAAC;IAED,MAAME,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,IAAIV,WAAW,CAACQ,KAAK,EAAE;QACrBG,oBAAoB,CAAC;MACvB,OAAO;QACLC,qBAAqB,CAAC;MACxB;IACF;IAEA,MAAMA,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAIZ,WAAW,CAACQ,KAAI,IAAKP,YAAY,CAACO,KAAK,EAAE;MAE7C,IAAI;QACFR,WAAW,CAACQ,KAAI,GAAI,IAAG;QACvBH,aAAa,CAACG,KAAI,GAAI,SAAQ;;QAE9B;QACA,MAAMK,MAAK,GAAI,MAAMhB,UAAU,CAACiB,gBAAgB,CAAC;QACjDX,iBAAiB,CAACK,KAAI,GAAIK,MAAK;;QAE/B;QACA,MAAME,kBAAkB,CAACF,MAAM;MAEjC,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BX,aAAa,CAACG,KAAI,GAAI,YAAW;;QAEjC;QACA,MAAMV,UAAU,CAACoB,KAAK,CAAC,mBAAmB;MAC5C,UAAU;QACRlB,WAAW,CAACQ,KAAI,GAAI,KAAI;QACxBH,aAAa,CAACG,KAAI,GAAI,EAAC;MACzB;IACF;IAEA,MAAMG,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAId,UAAU,EAAE;QACdA,UAAU,CAACsB,eAAe,CAAC;MAC7B;MACA,IAAIrB,UAAU,EAAE;QACdA,UAAU,CAACsB,IAAI,CAAC;MAClB;MACApB,WAAW,CAACQ,KAAI,GAAI,KAAI;MACxBH,aAAa,CAACG,KAAI,GAAI,EAAC;IACzB;IAEA,MAAMO,kBAAiB,GAAI,MAAOM,SAAS,IAAK;MAC9CpB,YAAY,CAACO,KAAI,GAAI,IAAG;MACxBH,aAAa,CAACG,KAAI,GAAI,SAAQ;MAE9B,IAAI;QACF;QACAH,aAAa,CAACG,KAAI,GAAI,aAAY;QAClC,MAAMc,WAAU,GAAI,MAAMvB,UAAU,CAACwB,oBAAoB,CAACF,SAAS;QAEnE,IAAIC,WAAW,CAACE,OAAM,IAAKF,WAAW,CAACG,UAAS,GAAI,GAAG,EAAE;UACvD;UACApB,aAAa,CAACG,KAAI,GAAI,QAAQkB,YAAY,CAACJ,WAAW,CAACE,OAAO,CAAC,KAAI;UACnE,MAAM1B,UAAU,CAACoB,KAAK,CAAC,UAAUQ,YAAY,CAACJ,WAAW,CAACE,OAAO,CAAC,EAAE;;UAEpE;UACA5B,IAAI,CAAC,wBAAwB,EAAE;YAC7B4B,OAAO,EAAEF,WAAW,CAACE,OAAO;YAC5BC,UAAU,EAAEH,WAAW,CAACG,UAAU;YAClCE,MAAM,EAAEL,WAAW,CAACK;UACtB,CAAC;;UAED;UACAC,UAAU,CAAC,YAAY;YACrB,MAAM9B,UAAU,CAACoB,KAAK,CAAC,QAAQ;YAC/Bb,aAAa,CAACG,KAAI,GAAI,EAAC;UACzB,CAAC,EAAE,IAAI;UAEP;QACF;;QAEA;QACA,MAAMqB,cAAa,GAAI,MAAM9B,UAAU,CAAC+B,gBAAgB,CAACT,SAAS;;QAElE;QACAhB,aAAa,CAACG,KAAI,GAAI,WAAU;QAChC,MAAMV,UAAU,CAACoB,KAAK,CAAC,cAAc;;QAErC;QACAtB,IAAI,CAAC,wBAAwB,EAAEiC,cAAc;;QAE7C;QACAD,UAAU,CAAC,YAAY;UACrB,MAAM9B,UAAU,CAACoB,KAAK,CAAC,QAAQ;UAC/Bb,aAAa,CAACG,KAAI,GAAI,EAAC;QACzB,CAAC,EAAE,IAAI;MAET,EAAE,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCX,aAAa,CAACG,KAAI,GAAI,UAAS;QAE/B,MAAMV,UAAU,CAACoB,KAAK,CAAC,UAAU;;QAEjC;QACAtB,IAAI,CAAC,wBAAwB,EAAEyB,SAAS;MAC1C,UAAU;QACRpB,YAAY,CAACO,KAAI,GAAI,KAAI;QACzBL,iBAAiB,CAACK,KAAI,GAAI,EAAC;MAC7B;IACF;IAEA,MAAMuB,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI5B,iBAAiB,CAACK,KAAK,CAACwB,IAAI,CAAC,CAAC,EAAE;QAClC,MAAMjB,kBAAkB,CAACZ,iBAAiB,CAACK,KAAK;MAClD;IACF;IAEA,MAAMyB,YAAW,GAAIA,CAAA,KAAM;MACzB9B,iBAAiB,CAACK,KAAI,GAAI,EAAC;MAC3BH,aAAa,CAACG,KAAI,GAAI,EAAC;MACvBV,UAAU,CAACoB,KAAK,CAAC,OAAO;IAC1B;IAEA,MAAMgB,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI9B,SAAS,CAACI,KAAK,CAACwB,IAAI,CAAC,CAAC,EAAE;QAC1B,MAAMjB,kBAAkB,CAACX,SAAS,CAACI,KAAK;QACxCJ,SAAS,CAACI,KAAI,GAAI,EAAC;MACrB;IACF;IAEA,MAAM2B,eAAc,GAAIA,CAAA,KAAM;MAC5B7B,aAAa,CAACE,KAAI,GAAI,CAACF,aAAa,CAACE,KAAI;IAC3C;;IAEA;IACA,MAAMkB,YAAW,GAAKF,OAAO,IAAK;MAChC,MAAMY,UAAS,GAAI;QACjBC,oBAAoB,EAAE,QAAQ;QAC9BC,mBAAmB,EAAE,QAAQ;QAC7BC,cAAc,EAAE,QAAQ;QACxBC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,MAAM;QAClBC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,QAAQ;QACtBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,MAAM;QACpBC,gBAAgB,EAAE,QAAQ;QAC1BC,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,MAAM;QACnBC,aAAa,EAAE;MACjB;MACA,OAAOhB,UAAU,CAACZ,OAAO,KAAKA,OAAM;IACtC;;IAEA;IACApC,SAAS,CAAC,MAAM;MACd;MACA,IAAIU,UAAU,CAACuD,WAAW,EAAE;QAC1BzB,UAAU,CAAC,MAAM;UACf9B,UAAU,CAACwD,SAAS,CAAC;QACvB,CAAC,EAAE,GAAG;MACR;IACF,CAAC;IAED,OAAO;MACLtD,WAAW;MACXC,YAAY;MACZC,UAAU;MACVC,iBAAiB;MACjBC,SAAS;MACTC,aAAa;MACbC,aAAa;MACbC,UAAU;MACVE,kBAAkB;MAClBC,sBAAsB;MACtByB,eAAe;MACfJ,YAAY;MACZE,YAAY;MACZC,eAAe;MACfR;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}