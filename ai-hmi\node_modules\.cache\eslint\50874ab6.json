[{"F:\\工作\\theme\\ai-hmi\\src\\main.js": "1", "F:\\工作\\theme\\ai-hmi\\src\\App.vue": "2", "F:\\工作\\theme\\ai-hmi\\src\\components\\SceneManager.vue": "3", "F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue": "4", "F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue": "5", "F:\\工作\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue": "6", "F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue": "7", "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue": "8", "F:\\工作\\theme\\ai-hmi\\src\\services\\SceneContextManager.js": "9", "F:\\工作\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js": "10", "F:\\工作\\theme\\ai-hmi\\src\\utils\\SceneManager.js": "11", "F:\\工作\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js": "12", "F:\\工作\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js": "13", "F:\\工作\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js": "14", "F:\\工作\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js": "15", "F:\\工作\\theme\\ai-hmi\\src\\services\\AsrService.js": "16", "F:\\工作\\theme\\ai-hmi\\src\\services\\LlmService.js": "17", "F:\\工作\\theme\\ai-hmi\\src\\components\\GlassCard.vue": "18", "F:\\工作\\theme\\ai-hmi\\src\\services\\TtsService.js": "19", "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue": "20", "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue": "21", "F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue": "22", "F:\\工作\\theme\\ai-hmi\\src\\components\\BaseCard.vue": "23", "F:\\工作\\theme\\ai-hmi\\src\\store\\index.js": "24", "F:\\工作\\theme\\ai-hmi\\src\\store\\modules\\layout.js": "25", "F:\\工作\\theme\\ai-hmi\\src\\store\\modules\\vpa.js": "26"}, {"size": 195, "mtime": 1754228094607, "results": "27", "hashOfConfig": "28"}, {"size": 5387, "mtime": 1754225383470, "results": "29", "hashOfConfig": "28"}, {"size": 40894, "mtime": 1754228727071, "results": "30", "hashOfConfig": "28"}, {"size": 20551, "mtime": 1753975939444, "results": "31", "hashOfConfig": "28"}, {"size": 12769, "mtime": 1753893861215, "results": "32", "hashOfConfig": "28"}, {"size": 6604, "mtime": 1753963331188, "results": "33", "hashOfConfig": "28"}, {"size": 12055, "mtime": 1753963331203, "results": "34", "hashOfConfig": "28"}, {"size": 76365, "mtime": 1753980290210, "results": "35", "hashOfConfig": "28"}, {"size": 15166, "mtime": 1753963331218, "results": "36", "hashOfConfig": "28"}, {"size": 15105, "mtime": 1753893977018, "results": "37", "hashOfConfig": "28"}, {"size": 14619, "mtime": 1753963331234, "results": "38", "hashOfConfig": "28"}, {"size": 11770, "mtime": 1753894025721, "results": "39", "hashOfConfig": "28"}, {"size": 12006, "mtime": 1753963331203, "results": "40", "hashOfConfig": "28"}, {"size": 9102, "mtime": 1753976373254, "results": "41", "hashOfConfig": "28"}, {"size": 8519, "mtime": 1753979182272, "results": "42", "hashOfConfig": "28"}, {"size": 1683, "mtime": 1753880804939, "results": "43", "hashOfConfig": "28"}, {"size": 11085, "mtime": 1753963331218, "results": "44", "hashOfConfig": "28"}, {"size": 6069, "mtime": 1753893787733, "results": "45", "hashOfConfig": "28"}, {"size": 4501, "mtime": 1753880804940, "results": "46", "hashOfConfig": "28"}, {"size": 13627, "mtime": 1754228500007, "results": "47", "hashOfConfig": "28"}, {"size": 15421, "mtime": 1754228567593, "results": "48", "hashOfConfig": "28"}, {"size": 10364, "mtime": 1754228347365, "results": "49", "hashOfConfig": "28"}, {"size": 7971, "mtime": 1754228221400, "results": "50", "hashOfConfig": "28"}, {"size": 312, "mtime": 1754228166687, "results": "51", "hashOfConfig": "28"}, {"size": 5121, "mtime": 1754228130850, "results": "52", "hashOfConfig": "28"}, {"size": 6615, "mtime": 1754228161200, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1omslgq", {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "63", "messages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "65", "messages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "67", "messages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "69", "messages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "71", "messages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "62"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "73"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\工作\\theme\\ai-hmi\\src\\main.js", [], "F:\\工作\\theme\\ai-hmi\\src\\App.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\SceneManager.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue", [], [], "F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\SceneContextManager.js", [], [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js", [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\SceneManager.js", [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\AsrService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\LlmService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\GlassCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\TtsService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue", ["108"], "F:\\工作\\theme\\ai-hmi\\src\\components\\BaseCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\store\\index.js", [], "F:\\工作\\theme\\ai-hmi\\src\\store\\modules\\layout.js", [], "F:\\工作\\theme\\ai-hmi\\src\\store\\modules\\vpa.js", [], {"ruleId": "109", "severity": 2, "message": "110", "line": 468, "column": 1, "nodeType": "111"}, "vue/no-parsing-error", "Parsing error: eof-in-comment.", "VElement"]