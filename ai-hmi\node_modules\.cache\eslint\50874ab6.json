[{"F:\\工作\\theme\\ai-hmi\\src\\main.js": "1", "F:\\工作\\theme\\ai-hmi\\src\\App.vue": "2", "F:\\工作\\theme\\ai-hmi\\src\\components\\SceneManager.vue": "3", "F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue": "4", "F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue": "5", "F:\\工作\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue": "6", "F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue": "7", "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue": "8", "F:\\工作\\theme\\ai-hmi\\src\\services\\SceneContextManager.js": "9", "F:\\工作\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js": "10", "F:\\工作\\theme\\ai-hmi\\src\\utils\\SceneManager.js": "11", "F:\\工作\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js": "12", "F:\\工作\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js": "13", "F:\\工作\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js": "14", "F:\\工作\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js": "15", "F:\\工作\\theme\\ai-hmi\\src\\services\\AsrService.js": "16", "F:\\工作\\theme\\ai-hmi\\src\\services\\LlmService.js": "17", "F:\\工作\\theme\\ai-hmi\\src\\components\\GlassCard.vue": "18", "F:\\工作\\theme\\ai-hmi\\src\\services\\TtsService.js": "19"}, {"size": 94, "mtime": 1754225383474, "results": "20", "hashOfConfig": "21"}, {"size": 5387, "mtime": 1754225383470, "results": "22", "hashOfConfig": "21"}, {"size": 38426, "mtime": 1753980013102, "results": "23", "hashOfConfig": "21"}, {"size": 20551, "mtime": 1753975939444, "results": "24", "hashOfConfig": "21"}, {"size": 12769, "mtime": 1753893861215, "results": "25", "hashOfConfig": "21"}, {"size": 6604, "mtime": 1753963331188, "results": "26", "hashOfConfig": "21"}, {"size": 12055, "mtime": 1753963331203, "results": "27", "hashOfConfig": "21"}, {"size": 76365, "mtime": 1753980290210, "results": "28", "hashOfConfig": "21"}, {"size": 15166, "mtime": 1753963331218, "results": "29", "hashOfConfig": "21"}, {"size": 15105, "mtime": 1753893977018, "results": "30", "hashOfConfig": "21"}, {"size": 14619, "mtime": 1753963331234, "results": "31", "hashOfConfig": "21"}, {"size": 11770, "mtime": 1753894025721, "results": "32", "hashOfConfig": "21"}, {"size": 12006, "mtime": 1753963331203, "results": "33", "hashOfConfig": "21"}, {"size": 9102, "mtime": 1753976373254, "results": "34", "hashOfConfig": "21"}, {"size": 8519, "mtime": 1753979182272, "results": "35", "hashOfConfig": "21"}, {"size": 1683, "mtime": 1753880804939, "results": "36", "hashOfConfig": "21"}, {"size": 11085, "mtime": 1753963331218, "results": "37", "hashOfConfig": "21"}, {"size": 6069, "mtime": 1753893787733, "results": "38", "hashOfConfig": "21"}, {"size": 4501, "mtime": 1753880804940, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1omslgq", {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "47", "messages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "49", "messages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "51", "messages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "53", "messages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "55", "messages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "57", "messages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "46"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, "F:\\工作\\theme\\ai-hmi\\src\\main.js", [], "F:\\工作\\theme\\ai-hmi\\src\\App.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\SceneManager.vue", [], [], "F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\SceneContextManager.js", [], [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js", [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\SceneManager.js", [], "F:\\工作\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\AsrService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\LlmService.js", [], "F:\\工作\\theme\\ai-hmi\\src\\components\\GlassCard.vue", [], "F:\\工作\\theme\\ai-hmi\\src\\services\\TtsService.js", []]