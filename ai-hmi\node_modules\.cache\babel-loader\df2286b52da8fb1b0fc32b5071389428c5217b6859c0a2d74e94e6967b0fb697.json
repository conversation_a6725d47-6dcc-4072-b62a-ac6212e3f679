{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '../BaseCard.vue';\nexport default {\n  name: 'MusicControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 2\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#9b59b6',\n        secondary: '#e74c3c',\n        background: 'rgba(155, 89, 182, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  emits: ['card-click', 'song-changed', 'play-state-changed'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式状态\n    const isPlaying = ref(false);\n    const isShuffled = ref(false);\n    const isRepeating = ref(false);\n    const isMuted = ref(false);\n    const volume = ref(75);\n    const currentTime = ref(0);\n    const currentSongIndex = ref(0);\n\n    // 播放列表数据\n    const playlist = ref([{\n      title: '夜空中最亮的星',\n      artist: '逃跑计划',\n      album: '世界',\n      duration: 245,\n      cover: null\n    }, {\n      title: '成都',\n      artist: '赵雷',\n      album: '无法长大',\n      duration: 327,\n      cover: null\n    }, {\n      title: '南山南',\n      artist: '马頔',\n      album: '孤岛',\n      duration: 290,\n      cover: null\n    }, {\n      title: '理想',\n      artist: '赵雷',\n      album: '赵小雷',\n      duration: 268,\n      cover: null\n    }]);\n\n    // 计算属性\n    const currentSong = computed(() => playlist.value[currentSongIndex.value]);\n    const progressPercentage = computed(() => {\n      if (currentSong.value.duration === 0) return 0;\n      return currentTime.value / currentSong.value.duration * 100;\n    });\n    const volumeIcon = computed(() => {\n      if (isMuted.value || volume.value === 0) {\n        return 'fas fa-volume-mute';\n      } else if (volume.value < 50) {\n        return 'fas fa-volume-down';\n      } else {\n        return 'fas fa-volume-up';\n      }\n    });\n\n    // 播放控制\n    let playTimer = null;\n    const togglePlay = () => {\n      isPlaying.value = !isPlaying.value;\n      if (isPlaying.value) {\n        startPlayTimer();\n      } else {\n        stopPlayTimer();\n      }\n      emit('play-state-changed', {\n        isPlaying: isPlaying.value,\n        song: currentSong.value\n      });\n    };\n    const startPlayTimer = () => {\n      playTimer = setInterval(() => {\n        currentTime.value += 1;\n\n        // 歌曲播放完毕\n        if (currentTime.value >= currentSong.value.duration) {\n          if (isRepeating.value) {\n            currentTime.value = 0;\n          } else {\n            nextSong();\n          }\n        }\n      }, 1000);\n    };\n    const stopPlayTimer = () => {\n      if (playTimer) {\n        clearInterval(playTimer);\n        playTimer = null;\n      }\n    };\n    const previousSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length);\n      } else {\n        currentSongIndex.value = currentSongIndex.value > 0 ? currentSongIndex.value - 1 : playlist.value.length - 1;\n      }\n      currentTime.value = 0;\n      emit('song-changed', currentSong.value);\n    };\n    const nextSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length);\n      } else {\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 ? currentSongIndex.value + 1 : 0;\n      }\n      currentTime.value = 0;\n      emit('song-changed', currentSong.value);\n    };\n    const playSong = index => {\n      currentSongIndex.value = index;\n      currentTime.value = 0;\n      if (!isPlaying.value) {\n        togglePlay();\n      }\n      emit('song-changed', currentSong.value);\n    };\n    const seekTo = event => {\n      const progressBar = event.currentTarget;\n      const rect = progressBar.getBoundingClientRect();\n      const clickX = event.clientX - rect.left;\n      const percentage = clickX / rect.width;\n      currentTime.value = Math.floor(percentage * currentSong.value.duration);\n    };\n    const toggleShuffle = () => {\n      isShuffled.value = !isShuffled.value;\n    };\n    const toggleRepeat = () => {\n      isRepeating.value = !isRepeating.value;\n    };\n    const toggleMute = () => {\n      isMuted.value = !isMuted.value;\n    };\n    const openFullPlaylist = () => {\n      console.log('打开完整播放列表');\n    };\n    const handleCardClick = () => {\n      emit('card-click', 'music');\n    };\n\n    // 工具函数\n    const formatTime = seconds => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      console.log('音乐控制卡片已加载');\n    });\n    onUnmounted(() => {\n      stopPlayTimer();\n    });\n    return {\n      isPlaying,\n      isShuffled,\n      isRepeating,\n      isMuted,\n      volume,\n      currentTime,\n      currentSongIndex,\n      playlist,\n      currentSong,\n      progressPercentage,\n      volumeIcon,\n      togglePlay,\n      previousSong,\n      nextSong,\n      playSong,\n      seekTo,\n      toggleShuffle,\n      toggleRepeat,\n      toggleMute,\n      openFullPlaylist,\n      handleCardClick,\n      formatTime\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "BaseCard", "name", "components", "props", "position", "type", "Object", "default", "x", "y", "theme", "String", "themeColors", "primary", "secondary", "background", "text", "emits", "setup", "emit", "isPlaying", "isShuffled", "isRepeating", "isMuted", "volume", "currentTime", "currentSongIndex", "playlist", "title", "artist", "album", "duration", "cover", "currentSong", "value", "progressPercentage", "volumeIcon", "playTimer", "togglePlay", "startPlayTimer", "stopPlayTimer", "song", "setInterval", "nextSong", "clearInterval", "previousSong", "Math", "floor", "random", "length", "playSong", "index", "seekTo", "event", "progressBar", "currentTarget", "rect", "getBoundingClientRect", "clickX", "clientX", "left", "percentage", "width", "toggleShuffle", "toggleRepeat", "toggleMute", "openFullPlaylist", "console", "log", "handleCardClick", "formatTime", "seconds", "mins", "secs", "toString", "padStart"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    card-type=\"music\"\n    size=\"large\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :title=\"'音乐控制'\"\n    :icon=\"'fas fa-music'\"\n    :clickable=\"true\"\n    :show-header=\"false\"\n    @click=\"handleCardClick\"\n    class=\"music-control-card\"\n  >\n    <div class=\"music-content\">\n      <!-- 专辑封面和歌曲信息 -->\n      <div class=\"music-info\">\n        <div class=\"album-cover\">\n          <img \n            v-if=\"currentSong.cover\"\n            :src=\"currentSong.cover\"\n            :alt=\"currentSong.title\"\n            class=\"cover-image\"\n          />\n          <div v-else class=\"cover-placeholder\">\n            <i class=\"fas fa-music\"></i>\n          </div>\n          \n          <!-- 播放状态覆盖层 -->\n          <div class=\"play-overlay\" :class=\"{ active: isPlaying }\">\n            <div class=\"sound-waves\">\n              <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"song-details\">\n          <h3 class=\"song-title\">{{ currentSong.title }}</h3>\n          <p class=\"song-artist\">{{ currentSong.artist }}</p>\n          <p class=\"song-album\">{{ currentSong.album }}</p>\n        </div>\n      </div>\n\n      <!-- 播放进度 -->\n      <div class=\"progress-section\">\n        <div class=\"time-display\">\n          <span class=\"current-time\">{{ formatTime(currentTime) }}</span>\n          <span class=\"total-time\">{{ formatTime(currentSong.duration) }}</span>\n        </div>\n        <div class=\"progress-bar\" @click=\"seekTo\">\n          <div class=\"progress-track\"></div>\n          <div \n            class=\"progress-fill\" \n            :style=\"{ width: `${progressPercentage}%` }\"\n          ></div>\n          <div \n            class=\"progress-thumb\" \n            :style=\"{ left: `${progressPercentage}%` }\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- 播放控制 -->\n      <div class=\"control-section\">\n        <div class=\"main-controls\">\n          <button @click=\"previousSong\" class=\"control-btn\">\n            <i class=\"fas fa-step-backward\"></i>\n          </button>\n          \n          <button @click=\"togglePlay\" class=\"control-btn play-btn\">\n            <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n          </button>\n          \n          <button @click=\"nextSong\" class=\"control-btn\">\n            <i class=\"fas fa-step-forward\"></i>\n          </button>\n        </div>\n        \n        <div class=\"secondary-controls\">\n          <button @click=\"toggleShuffle\" :class=\"['control-btn', { active: isShuffled }]\">\n            <i class=\"fas fa-random\"></i>\n          </button>\n          \n          <button @click=\"toggleRepeat\" :class=\"['control-btn', { active: isRepeating }]\">\n            <i class=\"fas fa-redo\"></i>\n          </button>\n          \n          <div class=\"volume-control\">\n            <button @click=\"toggleMute\" class=\"control-btn volume-btn\">\n              <i :class=\"volumeIcon\"></i>\n            </button>\n            <div class=\"volume-slider\">\n              <input \n                type=\"range\" \n                min=\"0\" \n                max=\"100\" \n                v-model=\"volume\"\n                class=\"volume-input\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 播放列表预览 -->\n      <div class=\"playlist-preview\">\n        <div class=\"playlist-header\">\n          <span>播放列表</span>\n          <button @click=\"openFullPlaylist\" class=\"playlist-btn\">\n            <i class=\"fas fa-list\"></i>\n          </button>\n        </div>\n        <div class=\"playlist-items\">\n          <div \n            v-for=\"(song, index) in playlist.slice(0, 3)\" \n            :key=\"index\"\n            :class=\"['playlist-item', { active: currentSongIndex === index }]\"\n            @click=\"playSong(index)\"\n          >\n            <div class=\"item-info\">\n              <span class=\"item-title\">{{ song.title }}</span>\n              <span class=\"item-artist\">{{ song.artist }}</span>\n            </div>\n            <div class=\"item-duration\">{{ formatTime(song.duration) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '../BaseCard.vue'\n\nexport default {\n  name: 'MusicControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 2 })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#9b59b6',\n        secondary: '#e74c3c',\n        background: 'rgba(155, 89, 182, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  \n  emits: ['card-click', 'song-changed', 'play-state-changed'],\n  \n  setup(props, { emit }) {\n    // 响应式状态\n    const isPlaying = ref(false)\n    const isShuffled = ref(false)\n    const isRepeating = ref(false)\n    const isMuted = ref(false)\n    const volume = ref(75)\n    const currentTime = ref(0)\n    const currentSongIndex = ref(0)\n    \n    // 播放列表数据\n    const playlist = ref([\n      {\n        title: '夜空中最亮的星',\n        artist: '逃跑计划',\n        album: '世界',\n        duration: 245,\n        cover: null\n      },\n      {\n        title: '成都',\n        artist: '赵雷',\n        album: '无法长大',\n        duration: 327,\n        cover: null\n      },\n      {\n        title: '南山南',\n        artist: '马頔',\n        album: '孤岛',\n        duration: 290,\n        cover: null\n      },\n      {\n        title: '理想',\n        artist: '赵雷',\n        album: '赵小雷',\n        duration: 268,\n        cover: null\n      }\n    ])\n    \n    // 计算属性\n    const currentSong = computed(() => playlist.value[currentSongIndex.value])\n    \n    const progressPercentage = computed(() => {\n      if (currentSong.value.duration === 0) return 0\n      return (currentTime.value / currentSong.value.duration) * 100\n    })\n    \n    const volumeIcon = computed(() => {\n      if (isMuted.value || volume.value === 0) {\n        return 'fas fa-volume-mute'\n      } else if (volume.value < 50) {\n        return 'fas fa-volume-down'\n      } else {\n        return 'fas fa-volume-up'\n      }\n    })\n    \n    // 播放控制\n    let playTimer = null\n    \n    const togglePlay = () => {\n      isPlaying.value = !isPlaying.value\n      \n      if (isPlaying.value) {\n        startPlayTimer()\n      } else {\n        stopPlayTimer()\n      }\n      \n      emit('play-state-changed', {\n        isPlaying: isPlaying.value,\n        song: currentSong.value\n      })\n    }\n    \n    const startPlayTimer = () => {\n      playTimer = setInterval(() => {\n        currentTime.value += 1\n        \n        // 歌曲播放完毕\n        if (currentTime.value >= currentSong.value.duration) {\n          if (isRepeating.value) {\n            currentTime.value = 0\n          } else {\n            nextSong()\n          }\n        }\n      }, 1000)\n    }\n    \n    const stopPlayTimer = () => {\n      if (playTimer) {\n        clearInterval(playTimer)\n        playTimer = null\n      }\n    }\n    \n    const previousSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\n      } else {\n        currentSongIndex.value = currentSongIndex.value > 0 \n          ? currentSongIndex.value - 1 \n          : playlist.value.length - 1\n      }\n      currentTime.value = 0\n      emit('song-changed', currentSong.value)\n    }\n    \n    const nextSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\n      } else {\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 \n          ? currentSongIndex.value + 1 \n          : 0\n      }\n      currentTime.value = 0\n      emit('song-changed', currentSong.value)\n    }\n    \n    const playSong = (index) => {\n      currentSongIndex.value = index\n      currentTime.value = 0\n      if (!isPlaying.value) {\n        togglePlay()\n      }\n      emit('song-changed', currentSong.value)\n    }\n    \n    const seekTo = (event) => {\n      const progressBar = event.currentTarget\n      const rect = progressBar.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const percentage = clickX / rect.width\n      currentTime.value = Math.floor(percentage * currentSong.value.duration)\n    }\n    \n    const toggleShuffle = () => {\n      isShuffled.value = !isShuffled.value\n    }\n    \n    const toggleRepeat = () => {\n      isRepeating.value = !isRepeating.value\n    }\n    \n    const toggleMute = () => {\n      isMuted.value = !isMuted.value\n    }\n    \n    const openFullPlaylist = () => {\n      console.log('打开完整播放列表')\n    }\n    \n    const handleCardClick = () => {\n      emit('card-click', 'music')\n    }\n    \n    // 工具函数\n    const formatTime = (seconds) => {\n      const mins = Math.floor(seconds / 60)\n      const secs = seconds % 60\n      return `${mins}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      console.log('音乐控制卡片已加载')\n    })\n    \n    onUnmounted(() => {\n      stopPlayTimer()\n    })\n    \n    return {\n      isPlaying,\n      isShuffled,\n      isRepeating,\n      isMuted,\n      volume,\n      currentTime,\n      currentSongIndex,\n      playlist,\n      currentSong,\n      progressPercentage,\n      volumeIcon,\n      togglePlay,\n      previousSong,\n      nextSong,\n      playSong,\n      seekTo,\n      toggleShuffle,\n      toggleRepeat,\n      toggleMute,\n      openFullPlaylist,\n      handleCardClick,\n      formatTime\n    }\n  }\n}\n</script>\n\n<style scoped>\n.music-control-card {\n  background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);\n}\n\n.music-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n}\n\n/* 音乐信息区域 */\n.music-info {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n.album-cover {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  border-radius: 10px;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  flex-shrink: 0;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.cover-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  color: var(--card-primary-color, #9b59b6);\n}\n\n.play-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.play-overlay.active {\n  opacity: 1;\n}\n\n.sound-waves {\n  display: flex;\n  gap: 2px;\n  align-items: flex-end;\n}\n\n.wave {\n  width: 3px;\n  height: 10px;\n  background: white;\n  border-radius: 2px;\n  animation: wave 1s infinite ease-in-out;\n}\n\n.song-details {\n  flex: 1;\n  min-width: 0;\n}\n\n.song-title {\n  margin: 0 0 5px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--card-text-color, #ffffff);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.song-artist, .song-album {\n  margin: 0;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 进度区域 */\n.progress-section {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.time-display {\n  display: flex;\n  justify-content: space-between;\n  font-size: 11px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n.progress-bar {\n  position: relative;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  cursor: pointer;\n}\n\n.progress-track {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n}\n\n.progress-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 2px;\n  transition: width 0.1s ease;\n}\n\n.progress-thumb {\n  position: absolute;\n  top: -4px;\n  width: 12px;\n  height: 12px;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 50%;\n  transform: translateX(-50%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.progress-bar:hover .progress-thumb {\n  opacity: 1;\n}\n\n/* 控制区域 */\n.control-section {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.main-controls {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n}\n\n.control-btn {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.control-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.control-btn.active {\n  background: var(--card-primary-color, #9b59b6);\n}\n\n.play-btn {\n  width: 50px;\n  height: 50px;\n  background: var(--card-primary-color, #9b59b6);\n  font-size: 18px;\n}\n\n.play-btn:hover {\n  background: var(--card-secondary-color, #e74c3c);\n}\n\n.secondary-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.volume-control {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.volume-btn {\n  width: 32px;\n  height: 32px;\n}\n\n.volume-slider {\n  width: 60px;\n}\n\n.volume-input {\n  width: 100%;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  outline: none;\n  -webkit-appearance: none;\n}\n\n.volume-input::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  width: 12px;\n  height: 12px;\n  background: var(--card-primary-color, #9b59b6);\n  border-radius: 50%;\n  cursor: pointer;\n}\n\n/* 播放列表预览 */\n.playlist-preview {\n  flex: 1;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 10px;\n  padding: 10px;\n  min-height: 0;\n}\n\n.playlist-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n.playlist-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.playlist-items {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.playlist-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.playlist-item:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.playlist-item.active {\n  background: var(--card-primary-color, #9b59b6);\n}\n\n.item-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.item-title {\n  display: block;\n  font-size: 11px;\n  color: var(--card-text-color, #ffffff);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.item-artist {\n  display: block;\n  font-size: 10px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.7;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.item-duration {\n  font-size: 10px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.7;\n}\n\n/* 动画 */\n@keyframes wave {\n  0%, 100% { height: 10px; }\n  50% { height: 20px; }\n}\n</style>\n"], "mappings": "AAoIA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,OAAOC,QAAO,MAAO,iBAAgB;AAErC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDK,WAAW,EAAE;MACXP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QACdM,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,yBAAyB;QACrCC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAEDC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,oBAAoB,CAAC;EAE3DC,KAAKA,CAACf,KAAK,EAAE;IAAEgB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIxB,GAAG,CAAC,KAAK;IAC3B,MAAMyB,UAAS,GAAIzB,GAAG,CAAC,KAAK;IAC5B,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,KAAK;IAC7B,MAAM2B,OAAM,GAAI3B,GAAG,CAAC,KAAK;IACzB,MAAM4B,MAAK,GAAI5B,GAAG,CAAC,EAAE;IACrB,MAAM6B,WAAU,GAAI7B,GAAG,CAAC,CAAC;IACzB,MAAM8B,gBAAe,GAAI9B,GAAG,CAAC,CAAC;;IAE9B;IACA,MAAM+B,QAAO,GAAI/B,GAAG,CAAC,CACnB;MACEgC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,EACD;;IAED;IACA,MAAMC,WAAU,GAAIpC,QAAQ,CAAC,MAAM8B,QAAQ,CAACO,KAAK,CAACR,gBAAgB,CAACQ,KAAK,CAAC;IAEzE,MAAMC,kBAAiB,GAAItC,QAAQ,CAAC,MAAM;MACxC,IAAIoC,WAAW,CAACC,KAAK,CAACH,QAAO,KAAM,CAAC,EAAE,OAAO;MAC7C,OAAQN,WAAW,CAACS,KAAI,GAAID,WAAW,CAACC,KAAK,CAACH,QAAQ,GAAI,GAAE;IAC9D,CAAC;IAED,MAAMK,UAAS,GAAIvC,QAAQ,CAAC,MAAM;MAChC,IAAI0B,OAAO,CAACW,KAAI,IAAKV,MAAM,CAACU,KAAI,KAAM,CAAC,EAAE;QACvC,OAAO,oBAAmB;MAC5B,OAAO,IAAIV,MAAM,CAACU,KAAI,GAAI,EAAE,EAAE;QAC5B,OAAO,oBAAmB;MAC5B,OAAO;QACL,OAAO,kBAAiB;MAC1B;IACF,CAAC;;IAED;IACA,IAAIG,SAAQ,GAAI,IAAG;IAEnB,MAAMC,UAAS,GAAIA,CAAA,KAAM;MACvBlB,SAAS,CAACc,KAAI,GAAI,CAACd,SAAS,CAACc,KAAI;MAEjC,IAAId,SAAS,CAACc,KAAK,EAAE;QACnBK,cAAc,CAAC;MACjB,OAAO;QACLC,aAAa,CAAC;MAChB;MAEArB,IAAI,CAAC,oBAAoB,EAAE;QACzBC,SAAS,EAAEA,SAAS,CAACc,KAAK;QAC1BO,IAAI,EAAER,WAAW,CAACC;MACpB,CAAC;IACH;IAEA,MAAMK,cAAa,GAAIA,CAAA,KAAM;MAC3BF,SAAQ,GAAIK,WAAW,CAAC,MAAM;QAC5BjB,WAAW,CAACS,KAAI,IAAK;;QAErB;QACA,IAAIT,WAAW,CAACS,KAAI,IAAKD,WAAW,CAACC,KAAK,CAACH,QAAQ,EAAE;UACnD,IAAIT,WAAW,CAACY,KAAK,EAAE;YACrBT,WAAW,CAACS,KAAI,GAAI;UACtB,OAAO;YACLS,QAAQ,CAAC;UACX;QACF;MACF,CAAC,EAAE,IAAI;IACT;IAEA,MAAMH,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIH,SAAS,EAAE;QACbO,aAAa,CAACP,SAAS;QACvBA,SAAQ,GAAI,IAAG;MACjB;IACF;IAEA,MAAMQ,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIxB,UAAU,CAACa,KAAK,EAAE;QACpBR,gBAAgB,CAACQ,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIrB,QAAQ,CAACO,KAAK,CAACe,MAAM;MAC3E,OAAO;QACLvB,gBAAgB,CAACQ,KAAI,GAAIR,gBAAgB,CAACQ,KAAI,GAAI,IAC9CR,gBAAgB,CAACQ,KAAI,GAAI,IACzBP,QAAQ,CAACO,KAAK,CAACe,MAAK,GAAI;MAC9B;MACAxB,WAAW,CAACS,KAAI,GAAI;MACpBf,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMS,QAAO,GAAIA,CAAA,KAAM;MACrB,IAAItB,UAAU,CAACa,KAAK,EAAE;QACpBR,gBAAgB,CAACQ,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIrB,QAAQ,CAACO,KAAK,CAACe,MAAM;MAC3E,OAAO;QACLvB,gBAAgB,CAACQ,KAAI,GAAIR,gBAAgB,CAACQ,KAAI,GAAIP,QAAQ,CAACO,KAAK,CAACe,MAAK,GAAI,IACtEvB,gBAAgB,CAACQ,KAAI,GAAI,IACzB;MACN;MACAT,WAAW,CAACS,KAAI,GAAI;MACpBf,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMgB,QAAO,GAAKC,KAAK,IAAK;MAC1BzB,gBAAgB,CAACQ,KAAI,GAAIiB,KAAI;MAC7B1B,WAAW,CAACS,KAAI,GAAI;MACpB,IAAI,CAACd,SAAS,CAACc,KAAK,EAAE;QACpBI,UAAU,CAAC;MACb;MACAnB,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMkB,MAAK,GAAKC,KAAK,IAAK;MACxB,MAAMC,WAAU,GAAID,KAAK,CAACE,aAAY;MACtC,MAAMC,IAAG,GAAIF,WAAW,CAACG,qBAAqB,CAAC;MAC/C,MAAMC,MAAK,GAAIL,KAAK,CAACM,OAAM,GAAIH,IAAI,CAACI,IAAG;MACvC,MAAMC,UAAS,GAAIH,MAAK,GAAIF,IAAI,CAACM,KAAI;MACrCrC,WAAW,CAACS,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACc,UAAS,GAAI5B,WAAW,CAACC,KAAK,CAACH,QAAQ;IACxE;IAEA,MAAMgC,aAAY,GAAIA,CAAA,KAAM;MAC1B1C,UAAU,CAACa,KAAI,GAAI,CAACb,UAAU,CAACa,KAAI;IACrC;IAEA,MAAM8B,YAAW,GAAIA,CAAA,KAAM;MACzB1C,WAAW,CAACY,KAAI,GAAI,CAACZ,WAAW,CAACY,KAAI;IACvC;IAEA,MAAM+B,UAAS,GAAIA,CAAA,KAAM;MACvB1C,OAAO,CAACW,KAAI,GAAI,CAACX,OAAO,CAACW,KAAI;IAC/B;IAEA,MAAMgC,gBAAe,GAAIA,CAAA,KAAM;MAC7BC,OAAO,CAACC,GAAG,CAAC,UAAU;IACxB;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5BlD,IAAI,CAAC,YAAY,EAAE,OAAO;IAC5B;;IAEA;IACA,MAAMmD,UAAS,GAAKC,OAAO,IAAK;MAC9B,MAAMC,IAAG,GAAI1B,IAAI,CAACC,KAAK,CAACwB,OAAM,GAAI,EAAE;MACpC,MAAME,IAAG,GAAIF,OAAM,GAAI,EAAC;MACxB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IACrD;;IAEA;IACA7E,SAAS,CAAC,MAAM;MACdqE,OAAO,CAACC,GAAG,CAAC,WAAW;IACzB,CAAC;IAEDrE,WAAW,CAAC,MAAM;MAChByC,aAAa,CAAC;IAChB,CAAC;IAED,OAAO;MACLpB,SAAS;MACTC,UAAU;MACVC,WAAW;MACXC,OAAO;MACPC,MAAM;MACNC,WAAW;MACXC,gBAAgB;MAChBC,QAAQ;MACRM,WAAW;MACXE,kBAAkB;MAClBC,UAAU;MACVE,UAAU;MACVO,YAAY;MACZF,QAAQ;MACRO,QAAQ;MACRE,MAAM;MACNW,aAAa;MACbC,YAAY;MACZC,UAAU;MACVC,gBAAgB;MAChBG,eAAe;MACfC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}