{"ast": null, "code": "import { computed, ref, nextTick, watch } from 'vue';\nimport { useVPAStore } from '@/store/modules/vpa';\nexport default {\n  name: 'VPAInteractionPanel',\n  props: {\n    // 组件尺寸：4x4, 8x9\n    size: {\n      type: String,\n      default: '4x4',\n      validator: value => ['4x4', '8x9'].includes(value)\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示情绪指示器\n    showEmotionIndicator: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['mode-change', 'interaction', 'quick-action'],\n  setup(props, {\n    emit\n  }) {\n    const vpaStore = useVPAStore();\n    const messagesContainer = ref(null);\n    const isExpanded = ref(false);\n    const imageError = ref(false);\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      };\n      return expressions[vpaStore.currentExpression] || expressions.neutral;\n    });\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      };\n      return modes[vpaStore.currentMode] || '未知模式';\n    });\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭',\n        'focus': '专注',\n        'minimal': '简约',\n        'entertainment': '娱乐'\n      };\n      return scenes[vpaStore.currentScene] || '通用';\n    });\n    const recentMessages = computed(() => {\n      // 显示最近的消息，根据面板大小限制数量\n      const maxMessages = props.size === '8x9' ? 10 : 5;\n      return vpaStore.conversationHistory.slice(-maxMessages);\n    });\n    const emotionStatus = computed(() => {\n      // 模拟情绪感知状态\n      const emotions = ['平静', '愉悦', '专注', '关注'];\n      return emotions[Math.floor(Math.random() * emotions.length)];\n    });\n    const panelStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position);\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      };\n    });\n\n    // 方法\n    const toggleExpanded = () => {\n      isExpanded.value = !isExpanded.value;\n    };\n    const switchMode = () => {\n      const modes = ['companion', 'interactive', 'restricted'];\n      const currentIndex = modes.indexOf(vpaStore.currentMode);\n      const nextMode = modes[(currentIndex + 1) % modes.length];\n      vpaStore.switchMode(nextMode);\n      emit('mode-change', nextMode);\n    };\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation();\n      } else {\n        vpaStore.startConversation();\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start');\n    };\n    const toggleSpeaking = () => {\n      // 切换播放状态的逻辑\n      if (vpaStore.isSpeaking) {\n        vpaStore.setAction('idle');\n      } else {\n        vpaStore.speak('正在为您服务');\n      }\n      emit('interaction', vpaStore.isSpeaking ? 'speaking-stop' : 'speaking-start');\n    };\n    const clearConversation = () => {\n      vpaStore.conversationHistory.splice(0);\n      emit('interaction', 'conversation-clear');\n    };\n    const quickAction = action => {\n      const actions = {\n        weather: '查询天气信息',\n        navigation: '打开导航助手',\n        music: '控制音乐播放',\n        schedule: '查看日程安排'\n      };\n      vpaStore.addMessage('user', actions[action]);\n      vpaStore.addMessage('vpa', `正在为您${actions[action]}...`);\n      emit('quick-action', action);\n      emit('interaction', `quick-action-${action}`);\n    };\n    const formatTime = timestamp => {\n      return new Date(timestamp).toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    };\n    const handleImageError = () => {\n      imageError.value = true;\n      console.warn('VPA avatar image failed to load');\n    };\n\n    // 监听消息变化，自动滚动到底部\n    watch(() => vpaStore.conversationHistory.length, () => {\n      nextTick(() => {\n        if (messagesContainer.value) {\n          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;\n        }\n      });\n    });\n    return {\n      vpaStore,\n      messagesContainer,\n      isExpanded,\n      imageError,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      recentMessages,\n      emotionStatus,\n      panelStyles,\n      toggleExpanded,\n      switchMode,\n      toggleListening,\n      toggleSpeaking,\n      clearConversation,\n      quickAction,\n      formatTime,\n      handleImageError\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "nextTick", "watch", "useVPAStore", "name", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "customStyles", "showEmotionIndicator", "Boolean", "emits", "setup", "emit", "vpaStore", "messagesContainer", "isExpanded", "imageError", "expressionEmoji", "expressions", "currentExpression", "neutral", "modeText", "modes", "currentMode", "sceneText", "scenes", "currentScene", "recentMessages", "maxMessages", "conversationHistory", "slice", "emotionStatus", "emotions", "Math", "floor", "random", "length", "panelStyles", "gridPosition", "calculateGridPosition", "toggleExpanded", "switchMode", "currentIndex", "indexOf", "nextMode", "toggleListening", "isListening", "endConversation", "startConversation", "toggleSpeaking", "isSpeaking", "setAction", "speak", "clearConversation", "splice", "quickAction", "action", "actions", "weather", "navigation", "music", "schedule", "addMessage", "formatTime", "timestamp", "Date", "toLocaleTimeString", "hour", "minute", "handleImageError", "console", "warn", "scrollTop", "scrollHeight"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPAInteractionPanel.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-interaction-panel\"\n    :class=\"[\n      `size-${size}`,\n      `mode-${vpaStore.currentMode}`,\n      { 'active': vpaStore.isActive, 'expanded': isExpanded }\n    ]\"\n    :style=\"panelStyles\"\n  >\n    <!-- 面板头部 -->\n    <div class=\"panel-header\">\n      <div class=\"vpa-avatar-section\">\n        <div class=\"avatar-container\">\n          <img \n            :src=\"`/docs/${vpaStore.avatar}`\" \n            :alt=\"`VPA ${vpaStore.currentExpression}`\"\n            class=\"avatar-image\"\n            @error=\"handleImageError\"\n          />\n          <div class=\"expression-overlay\">{{ expressionEmoji }}</div>\n          \n          <!-- 状态指示器 -->\n          <div v-if=\"vpaStore.isListening\" class=\"status-indicator listening\">\n            <div class=\"pulse-ring\"></div>\n            🎤\n          </div>\n          <div v-else-if=\"vpaStore.isSpeaking\" class=\"status-indicator speaking\">\n            <div class=\"wave-animation\"></div>\n            🔊\n          </div>\n        </div>\n        \n        <div class=\"vpa-info\">\n          <div class=\"vpa-name\">AI助手</div>\n          <div class=\"vpa-status\">\n            <span class=\"mode-badge\">{{ modeText }}</span>\n            <span class=\"scene-badge\">{{ sceneText }}</span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"panel-controls\">\n        <button \n          class=\"control-btn\"\n          @click=\"toggleExpanded\"\n          :title=\"isExpanded ? '收起面板' : '展开面板'\"\n        >\n          {{ isExpanded ? '📐' : '📏' }}\n        </button>\n        <button \n          class=\"control-btn\"\n          @click=\"switchMode\"\n          title=\"切换模式\"\n        >\n          🔄\n        </button>\n      </div>\n    </div>\n    \n    <!-- 面板主体内容 -->\n    <div class=\"panel-body\">\n      <!-- 对话区域 -->\n      <div class=\"conversation-area\">\n        <div class=\"conversation-header\">\n          <span class=\"conversation-title\">对话记录</span>\n          <button \n            class=\"clear-btn\"\n            @click=\"clearConversation\"\n            title=\"清空对话\"\n          >\n            🗑️\n          </button>\n        </div>\n        \n        <div class=\"messages-container\" ref=\"messagesContainer\">\n          <div \n            v-for=\"(message, index) in recentMessages\" \n            :key=\"index\"\n            class=\"message-item\"\n            :class=\"{ 'user-message': message.type === 'user', 'vpa-message': message.type === 'vpa' }\"\n          >\n            <div class=\"message-content\">{{ message.content }}</div>\n            <div class=\"message-time\">{{ formatTime(message.timestamp) }}</div>\n          </div>\n          \n          <div v-if=\"recentMessages.length === 0\" class=\"empty-conversation\">\n            <div class=\"empty-icon\">💬</div>\n            <div class=\"empty-text\">开始对话吧</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 交互控制区 -->\n      <div class=\"interaction-controls\">\n        <div class=\"voice-controls\">\n          <button \n            class=\"voice-btn\"\n            :class=\"{ active: vpaStore.isListening }\"\n            @click=\"toggleListening\"\n          >\n            <span class=\"btn-icon\">{{ vpaStore.isListening ? '⏹️' : '🎤' }}</span>\n            <span class=\"btn-text\">{{ vpaStore.isListening ? '停止' : '语音' }}</span>\n          </button>\n          \n          <button \n            class=\"voice-btn\"\n            :class=\"{ active: vpaStore.isSpeaking }\"\n            @click=\"toggleSpeaking\"\n          >\n            <span class=\"btn-icon\">{{ vpaStore.isSpeaking ? '🔇' : '🔊' }}</span>\n            <span class=\"btn-text\">{{ vpaStore.isSpeaking ? '静音' : '播放' }}</span>\n          </button>\n        </div>\n        \n        <div class=\"quick-actions\">\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('weather')\"\n            title=\"天气查询\"\n          >\n            🌤️\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('navigation')\"\n            title=\"导航助手\"\n          >\n            🗺️\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('music')\"\n            title=\"音乐控制\"\n          >\n            🎵\n          </button>\n          <button \n            class=\"quick-btn\"\n            @click=\"quickAction('schedule')\"\n            title=\"日程管理\"\n          >\n            📅\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 情绪感知指示器 -->\n    <div v-if=\"showEmotionIndicator\" class=\"emotion-indicator\">\n      <div class=\"emotion-label\">情绪感知</div>\n      <div class=\"emotion-status\">{{ emotionStatus }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref, nextTick, watch } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPAInteractionPanel',\n  props: {\n    // 组件尺寸：4x4, 8x9\n    size: {\n      type: String,\n      default: '4x4',\n      validator: value => ['4x4', '8x9'].includes(value)\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    // 自定义样式\n    customStyles: {\n      type: Object,\n      default: () => ({})\n    },\n    // 是否显示情绪指示器\n    showEmotionIndicator: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['mode-change', 'interaction', 'quick-action'],\n  setup(props, { emit }) {\n    const vpaStore = useVPAStore()\n    const messagesContainer = ref(null)\n    const isExpanded = ref(false)\n    const imageError = ref(false)\n\n    // 计算属性\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[vpaStore.currentExpression] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      }\n      return modes[vpaStore.currentMode] || '未知模式'\n    })\n\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭',\n        'focus': '专注',\n        'minimal': '简约',\n        'entertainment': '娱乐'\n      }\n      return scenes[vpaStore.currentScene] || '通用'\n    })\n\n    const recentMessages = computed(() => {\n      // 显示最近的消息，根据面板大小限制数量\n      const maxMessages = props.size === '8x9' ? 10 : 5\n      return vpaStore.conversationHistory.slice(-maxMessages)\n    })\n\n    const emotionStatus = computed(() => {\n      // 模拟情绪感知状态\n      const emotions = ['平静', '愉悦', '专注', '关注']\n      return emotions[Math.floor(Math.random() * emotions.length)]\n    })\n\n    const panelStyles = computed(() => {\n      const gridPosition = vpaStore.calculateGridPosition(props.size, props.position)\n      return {\n        ...gridPosition,\n        ...props.customStyles\n      }\n    })\n\n    // 方法\n    const toggleExpanded = () => {\n      isExpanded.value = !isExpanded.value\n    }\n\n    const switchMode = () => {\n      const modes = ['companion', 'interactive', 'restricted']\n      const currentIndex = modes.indexOf(vpaStore.currentMode)\n      const nextMode = modes[(currentIndex + 1) % modes.length]\n      vpaStore.switchMode(nextMode)\n      emit('mode-change', nextMode)\n    }\n\n    const toggleListening = () => {\n      if (vpaStore.isListening) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n      emit('interaction', vpaStore.isListening ? 'listening-stop' : 'listening-start')\n    }\n\n    const toggleSpeaking = () => {\n      // 切换播放状态的逻辑\n      if (vpaStore.isSpeaking) {\n        vpaStore.setAction('idle')\n      } else {\n        vpaStore.speak('正在为您服务')\n      }\n      emit('interaction', vpaStore.isSpeaking ? 'speaking-stop' : 'speaking-start')\n    }\n\n    const clearConversation = () => {\n      vpaStore.conversationHistory.splice(0)\n      emit('interaction', 'conversation-clear')\n    }\n\n    const quickAction = (action) => {\n      const actions = {\n        weather: '查询天气信息',\n        navigation: '打开导航助手',\n        music: '控制音乐播放',\n        schedule: '查看日程安排'\n      }\n      \n      vpaStore.addMessage('user', actions[action])\n      vpaStore.addMessage('vpa', `正在为您${actions[action]}...`)\n      \n      emit('quick-action', action)\n      emit('interaction', `quick-action-${action}`)\n    }\n\n    const formatTime = (timestamp) => {\n      return new Date(timestamp).toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    // 监听消息变化，自动滚动到底部\n    watch(() => vpaStore.conversationHistory.length, () => {\n      nextTick(() => {\n        if (messagesContainer.value) {\n          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight\n        }\n      })\n    })\n\n    return {\n      vpaStore,\n      messagesContainer,\n      isExpanded,\n      imageError,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      recentMessages,\n      emotionStatus,\n      panelStyles,\n      toggleExpanded,\n      switchMode,\n      toggleListening,\n      toggleSpeaking,\n      clearConversation,\n      quickAction,\n      formatTime,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-interaction-panel {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(15px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.vpa-interaction-panel.expanded {\n  transform: scale(1.02);\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);\n}\n\n/* 面板头部 */\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.vpa-avatar-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.avatar-container {\n  position: relative;\n  width: 48px;\n  height: 48px;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid rgba(74, 144, 226, 0.5);\n}\n\n.expression-overlay {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 18px;\n  height: 18px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.status-indicator {\n  position: absolute;\n  top: -6px;\n  right: -6px;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: white;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 2px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.3); }\n}\n\n.vpa-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.vpa-name {\n  color: white;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.vpa-status {\n  display: flex;\n  gap: 6px;\n}\n\n.mode-badge,\n.scene-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.scene-badge {\n  background: rgba(156, 39, 176, 0.8);\n}\n\n.panel-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n}\n\n.control-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: scale(1.1);\n}\n\n/* 面板主体 */\n.panel-body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 16px;\n  gap: 16px;\n  min-height: 0;\n}\n\n/* 对话区域 */\n.conversation-area {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.conversation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.conversation-title {\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.clear-btn {\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  font-size: 12px;\n  transition: color 0.3s ease;\n}\n\n.clear-btn:hover {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.messages-container {\n  flex: 1;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  min-height: 0;\n  max-height: 200px;\n}\n\n.message-item {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.message-content {\n  padding: 6px 10px;\n  border-radius: 12px;\n  font-size: 11px;\n  line-height: 1.4;\n  max-width: 80%;\n}\n\n.user-message .message-content {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  align-self: flex-end;\n}\n\n.vpa-message .message-content {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  align-self: flex-start;\n}\n\n.message-time {\n  font-size: 9px;\n  color: rgba(255, 255, 255, 0.5);\n  align-self: flex-end;\n}\n\n.vpa-message .message-time {\n  align-self: flex-start;\n}\n\n.empty-conversation {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  color: rgba(255, 255, 255, 0.5);\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 24px;\n  margin-bottom: 8px;\n}\n\n.empty-text {\n  font-size: 12px;\n}\n\n/* 交互控制区 */\n.interaction-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.voice-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.voice-btn {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.voice-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.voice-btn.active {\n  background: rgba(74, 144, 226, 0.8);\n  border-color: rgba(74, 144, 226, 1);\n}\n\n.btn-icon {\n  font-size: 14px;\n}\n\n.btn-text {\n  font-size: 11px;\n}\n\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 8px;\n}\n\n.quick-btn {\n  aspect-ratio: 1;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.quick-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: scale(1.05);\n}\n\n/* 情绪感知指示器 */\n.emotion-indicator {\n  position: absolute;\n  bottom: 12px;\n  right: 12px;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(10px);\n  border-radius: 8px;\n  padding: 4px 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2px;\n}\n\n.emotion-label {\n  font-size: 8px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.emotion-status {\n  font-size: 10px;\n  color: rgba(76, 175, 80, 0.9);\n  font-weight: 600;\n}\n\n/* 响应式适配 */\n@media (max-width: 768px) {\n  .panel-header {\n    padding: 10px 12px;\n  }\n  \n  .avatar-container {\n    width: 40px;\n    height: 40px;\n  }\n  \n  .vpa-name {\n    font-size: 12px;\n  }\n  \n  .panel-body {\n    padding: 12px;\n    gap: 12px;\n  }\n  \n  .voice-btn {\n    padding: 6px 8px;\n  }\n  \n  .btn-text {\n    font-size: 10px;\n  }\n  \n  .quick-btn {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": "AA6JA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAI,QAAS,KAAI;AACnD,SAASC,WAAU,QAAS,qBAAoB;AAEhD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE;IACL;IACAC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAEC,KAAI,IAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnD,CAAC;IACD;IACAE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACD;IACAC,YAAY,EAAE;MACZV,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACD;IACAS,oBAAoB,EAAE;MACpBX,IAAI,EAAEY,OAAO;MACbV,OAAO,EAAE;IACX;EACF,CAAC;EACDW,KAAK,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC;EACrDC,KAAKA,CAAChB,KAAK,EAAE;IAAEiB;EAAK,CAAC,EAAE;IACrB,MAAMC,QAAO,GAAIpB,WAAW,CAAC;IAC7B,MAAMqB,iBAAgB,GAAIxB,GAAG,CAAC,IAAI;IAClC,MAAMyB,UAAS,GAAIzB,GAAG,CAAC,KAAK;IAC5B,MAAM0B,UAAS,GAAI1B,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAM2B,eAAc,GAAI5B,QAAQ,CAAC,MAAM;MACrC,MAAM6B,WAAU,GAAI;QAClB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE;MACd;MACA,OAAOA,WAAW,CAACL,QAAQ,CAACM,iBAAiB,KAAKD,WAAW,CAACE,OAAM;IACtE,CAAC;IAED,MAAMC,QAAO,GAAIhC,QAAQ,CAAC,MAAM;MAC9B,MAAMiC,KAAI,GAAI;QACZ,WAAW,EAAE,MAAM;QACnB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE;MAChB;MACA,OAAOA,KAAK,CAACT,QAAQ,CAACU,WAAW,KAAK,MAAK;IAC7C,CAAC;IAED,MAAMC,SAAQ,GAAInC,QAAQ,CAAC,MAAM;MAC/B,MAAMoC,MAAK,GAAI;QACb,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;QACf,eAAe,EAAE;MACnB;MACA,OAAOA,MAAM,CAACZ,QAAQ,CAACa,YAAY,KAAK,IAAG;IAC7C,CAAC;IAED,MAAMC,cAAa,GAAItC,QAAQ,CAAC,MAAM;MACpC;MACA,MAAMuC,WAAU,GAAIjC,KAAK,CAACC,IAAG,KAAM,KAAI,GAAI,EAAC,GAAI;MAChD,OAAOiB,QAAQ,CAACgB,mBAAmB,CAACC,KAAK,CAAC,CAACF,WAAW;IACxD,CAAC;IAED,MAAMG,aAAY,GAAI1C,QAAQ,CAAC,MAAM;MACnC;MACA,MAAM2C,QAAO,GAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACxC,OAAOA,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIH,QAAQ,CAACI,MAAM,CAAC;IAC7D,CAAC;IAED,MAAMC,WAAU,GAAIhD,QAAQ,CAAC,MAAM;MACjC,MAAMiD,YAAW,GAAIzB,QAAQ,CAAC0B,qBAAqB,CAAC5C,KAAK,CAACC,IAAI,EAAED,KAAK,CAACQ,QAAQ;MAC9E,OAAO;QACL,GAAGmC,YAAY;QACf,GAAG3C,KAAK,CAACY;MACX;IACF,CAAC;;IAED;IACA,MAAMiC,cAAa,GAAIA,CAAA,KAAM;MAC3BzB,UAAU,CAACd,KAAI,GAAI,CAACc,UAAU,CAACd,KAAI;IACrC;IAEA,MAAMwC,UAAS,GAAIA,CAAA,KAAM;MACvB,MAAMnB,KAAI,GAAI,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY;MACvD,MAAMoB,YAAW,GAAIpB,KAAK,CAACqB,OAAO,CAAC9B,QAAQ,CAACU,WAAW;MACvD,MAAMqB,QAAO,GAAItB,KAAK,CAAC,CAACoB,YAAW,GAAI,CAAC,IAAIpB,KAAK,CAACc,MAAM;MACxDvB,QAAQ,CAAC4B,UAAU,CAACG,QAAQ;MAC5BhC,IAAI,CAAC,aAAa,EAAEgC,QAAQ;IAC9B;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAIhC,QAAQ,CAACiC,WAAW,EAAE;QACxBjC,QAAQ,CAACkC,eAAe,CAAC;MAC3B,OAAO;QACLlC,QAAQ,CAACmC,iBAAiB,CAAC;MAC7B;MACApC,IAAI,CAAC,aAAa,EAAEC,QAAQ,CAACiC,WAAU,GAAI,gBAAe,GAAI,iBAAiB;IACjF;IAEA,MAAMG,cAAa,GAAIA,CAAA,KAAM;MAC3B;MACA,IAAIpC,QAAQ,CAACqC,UAAU,EAAE;QACvBrC,QAAQ,CAACsC,SAAS,CAAC,MAAM;MAC3B,OAAO;QACLtC,QAAQ,CAACuC,KAAK,CAAC,QAAQ;MACzB;MACAxC,IAAI,CAAC,aAAa,EAAEC,QAAQ,CAACqC,UAAS,GAAI,eAAc,GAAI,gBAAgB;IAC9E;IAEA,MAAMG,iBAAgB,GAAIA,CAAA,KAAM;MAC9BxC,QAAQ,CAACgB,mBAAmB,CAACyB,MAAM,CAAC,CAAC;MACrC1C,IAAI,CAAC,aAAa,EAAE,oBAAoB;IAC1C;IAEA,MAAM2C,WAAU,GAAKC,MAAM,IAAK;MAC9B,MAAMC,OAAM,GAAI;QACdC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE;MACZ;MAEAhD,QAAQ,CAACiD,UAAU,CAAC,MAAM,EAAEL,OAAO,CAACD,MAAM,CAAC;MAC3C3C,QAAQ,CAACiD,UAAU,CAAC,KAAK,EAAE,OAAOL,OAAO,CAACD,MAAM,CAAC,KAAK;MAEtD5C,IAAI,CAAC,cAAc,EAAE4C,MAAM;MAC3B5C,IAAI,CAAC,aAAa,EAAE,gBAAgB4C,MAAM,EAAE;IAC9C;IAEA,MAAMO,UAAS,GAAKC,SAAS,IAAK;MAChC,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC;IACH;IAEA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7BrD,UAAU,CAACf,KAAI,GAAI,IAAG;MACtBqE,OAAO,CAACC,IAAI,CAAC,iCAAiC;IAChD;;IAEA;IACA/E,KAAK,CAAC,MAAMqB,QAAQ,CAACgB,mBAAmB,CAACO,MAAM,EAAE,MAAM;MACrD7C,QAAQ,CAAC,MAAM;QACb,IAAIuB,iBAAiB,CAACb,KAAK,EAAE;UAC3Ba,iBAAiB,CAACb,KAAK,CAACuE,SAAQ,GAAI1D,iBAAiB,CAACb,KAAK,CAACwE,YAAW;QACzE;MACF,CAAC;IACH,CAAC;IAED,OAAO;MACL5D,QAAQ;MACRC,iBAAiB;MACjBC,UAAU;MACVC,UAAU;MACVC,eAAe;MACfI,QAAQ;MACRG,SAAS;MACTG,cAAc;MACdI,aAAa;MACbM,WAAW;MACXG,cAAc;MACdC,UAAU;MACVI,eAAe;MACfI,cAAc;MACdI,iBAAiB;MACjBE,WAAW;MACXQ,UAAU;MACVM;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}