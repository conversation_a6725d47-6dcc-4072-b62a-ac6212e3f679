{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// VPA数字人状态管理Store\nimport { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion'); // companion, interactive, restricted\n  const avatar = ref('vpa2.gif'); // 当前使用的头像\n  const isActive = ref(true);\n  const isListening = ref(false);\n  const isSpeaking = ref(false);\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning');\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  });\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  });\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral'); // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle'); // idle, listening, speaking, thinking, gesturing\n\n  // === 对话历史 ===\n  const conversationHistory = ref([]);\n  const currentConversation = ref(null);\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    };\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    };\n  });\n  const contextualGreeting = computed(() => {\n    const {\n      timeOfDay,\n      hasChildren,\n      userMood\n    } = sceneContext.value;\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    };\n    return greetings[timeOfDay] || greetings.morning;\n  });\n\n  // === 方法 ===\n\n  // 切换VPA模式\n  const switchMode = newMode => {\n    currentMode.value = newMode;\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif';\n\n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false;\n    } else {\n      isActive.value = true;\n    }\n  };\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName;\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n\n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName);\n  };\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = sceneName => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    };\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus;\n    currentExpression.value = behavior.expression;\n    userPreferences.value.aiPersonality = behavior.personality;\n  };\n\n  // 更新用户偏好\n  const updatePreferences = preferences => {\n    userPreferences.value = {\n      ...userPreferences.value,\n      ...preferences\n    };\n  };\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    };\n    currentAction.value = 'listening';\n    isListening.value = true;\n  };\n\n  // 添加对话消息\n  const addMessage = message => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      });\n    }\n  };\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      });\n      currentConversation.value = null;\n    }\n    currentAction.value = 'idle';\n    isListening.value = false;\n    isSpeaking.value = false;\n  };\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true;\n    currentAction.value = 'speaking';\n    currentExpression.value = emotion;\n\n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false;\n      currentAction.value = 'idle';\n      currentExpression.value = 'neutral';\n    }, text.length * 100); // 根据文本长度估算说话时间\n  };\n\n  // 设置表情\n  const setExpression = expression => {\n    currentExpression.value = expression;\n  };\n\n  // 设置动作\n  const setAction = action => {\n    currentAction.value = action;\n  };\n\n  // 情绪感知\n  const detectEmotion = audioData => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired'];\n    return emotions[Math.floor(Math.random() * emotions.length)];\n  };\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    };\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n    return context;\n  };\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }));\n  };\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa');\n    if (saved) {\n      try {\n        const data = JSON.parse(saved);\n        currentMode.value = data.currentMode || 'companion';\n        userPreferences.value = {\n          ...userPreferences.value,\n          ...data.userPreferences\n        };\n        sceneContext.value = {\n          ...sceneContext.value,\n          ...data.sceneContext\n        };\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error);\n      }\n    }\n  };\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState();\n    analyzeContext();\n\n    // 设置定期保存\n    setInterval(saveVPAState, 30000); // 每30秒保存一次\n  };\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useVPAStore", "currentMode", "avatar", "isActive", "isListening", "isSpeaking", "currentScene", "sceneContext", "timeOfDay", "weather", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSchoolDay", "userMood", "passengers", "userPreferences", "theme", "cardSizes", "aiPersonality", "voiceEnabled", "language", "currentExpression", "currentAction", "conversationHistory", "currentConversation", "vpaDisplayConfig", "expressions", "expression", "value", "neutral", "mode", "contextualGreeting", "greetings", "morning", "afternoon", "evening", "night", "switchMode", "newMode", "updateScene", "scene<PERSON><PERSON>", "context", "adjustVPABehavior", "scene<PERSON><PERSON><PERSON><PERSON>", "personality", "priority", "behavior", "focus", "updatePreferences", "preferences", "startConversation", "topic", "id", "Date", "now", "startTime", "messages", "addMessage", "message", "push", "timestamp", "endConversation", "endTime", "speak", "text", "emotion", "setTimeout", "length", "setExpression", "setAction", "action", "detectEmotion", "audioData", "emotions", "Math", "floor", "random", "analyzeContext", "location", "traffic", "getHours", "saveVPAState", "localStorage", "setItem", "JSON", "stringify", "loadVPAState", "saved", "getItem", "data", "parse", "error", "console", "warn", "initializeVPA", "setInterval"], "sources": ["F:/工作/theme/ai-hmi/src/store/modules/vpa.js"], "sourcesContent": ["// VPA数字人状态管理Store\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion') // companion, interactive, restricted\n  const avatar = ref('vpa2.gif') // 当前使用的头像\n  const isActive = ref(true)\n  const isListening = ref(false)\n  const isSpeaking = ref(false)\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning')\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  })\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  })\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral') // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle') // idle, listening, speaking, thinking, gesturing\n\n  // === 对话历史 ===\n  const conversationHistory = ref([])\n  const currentConversation = ref(null)\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    }\n\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    }\n  })\n\n  const contextualGreeting = computed(() => {\n    const { timeOfDay, hasChildren, userMood } = sceneContext.value\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    }\n    \n    return greetings[timeOfDay] || greetings.morning\n  })\n\n  // === 方法 ===\n  \n  // 切换VPA模式\n  const switchMode = (newMode) => {\n    currentMode.value = newMode\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif'\n    \n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false\n    } else {\n      isActive.value = true\n    }\n  }\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName\n    sceneContext.value = { ...sceneContext.value, ...context }\n    \n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName)\n  }\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = (sceneName) => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    }\n\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus\n    currentExpression.value = behavior.expression\n    userPreferences.value.aiPersonality = behavior.personality\n  }\n\n  // 更新用户偏好\n  const updatePreferences = (preferences) => {\n    userPreferences.value = { ...userPreferences.value, ...preferences }\n  }\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    }\n    currentAction.value = 'listening'\n    isListening.value = true\n  }\n\n  // 添加对话消息\n  const addMessage = (message) => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      })\n    }\n  }\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      })\n      currentConversation.value = null\n    }\n    currentAction.value = 'idle'\n    isListening.value = false\n    isSpeaking.value = false\n  }\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true\n    currentAction.value = 'speaking'\n    currentExpression.value = emotion\n    \n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false\n      currentAction.value = 'idle'\n      currentExpression.value = 'neutral'\n    }, text.length * 100) // 根据文本长度估算说话时间\n  }\n\n  // 设置表情\n  const setExpression = (expression) => {\n    currentExpression.value = expression\n  }\n\n  // 设置动作\n  const setAction = (action) => {\n    currentAction.value = action\n  }\n\n  // 情绪感知\n  const detectEmotion = (audioData) => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired']\n    return emotions[Math.floor(Math.random() * emotions.length)]\n  }\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : \n                 new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    }\n    \n    sceneContext.value = { ...sceneContext.value, ...context }\n    return context\n  }\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }))\n  }\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa')\n    if (saved) {\n      try {\n        const data = JSON.parse(saved)\n        currentMode.value = data.currentMode || 'companion'\n        userPreferences.value = { ...userPreferences.value, ...data.userPreferences }\n        sceneContext.value = { ...sceneContext.value, ...data.sceneContext }\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error)\n      }\n    }\n  }\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState()\n    analyzeContext()\n    \n    // 设置定期保存\n    setInterval(saveVPAState, 30000) // 每30秒保存一次\n  }\n\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    \n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    \n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  }\n})\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAEnC,OAAO,MAAMC,WAAW,GAAGH,WAAW,CAAC,KAAK,EAAE,MAAM;EAClD;EACA,MAAMI,WAAW,GAAGH,GAAG,CAAC,WAAW,CAAC,EAAC;EACrC,MAAMI,MAAM,GAAGJ,GAAG,CAAC,UAAU,CAAC,EAAC;EAC/B,MAAMK,QAAQ,GAAGL,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMM,WAAW,GAAGN,GAAG,CAAC,KAAK,CAAC;EAC9B,MAAMO,UAAU,GAAGP,GAAG,CAAC,KAAK,CAAC;;EAE7B;EACA,MAAMQ,YAAY,GAAGR,GAAG,CAAC,iBAAiB,CAAC;EAC3C,MAAMS,YAAY,GAAGT,GAAG,CAAC;IACvBU,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGhB,GAAG,CAAC;IAC1BiB,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE,CAAC,CAAC;IACbC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGtB,GAAG,CAAC,SAAS,CAAC,EAAC;EACzC,MAAMuB,aAAa,GAAGvB,GAAG,CAAC,MAAM,CAAC,EAAC;;EAElC;EACA,MAAMwB,mBAAmB,GAAGxB,GAAG,CAAC,EAAE,CAAC;EACnC,MAAMyB,mBAAmB,GAAGzB,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM0B,gBAAgB,GAAGzB,QAAQ,CAAC,MAAM;IACtC,MAAM0B,WAAW,GAAG;MAClB,SAAS,EAAE,OAAO;MAClB,OAAO,EAAE,OAAO;MAChB,UAAU,EAAE,UAAU;MACtB,WAAW,EAAE,OAAO;MACpB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE;IACd,CAAC;IAED,OAAO;MACLC,UAAU,EAAED,WAAW,CAACL,iBAAiB,CAACO,KAAK,CAAC,IAAIF,WAAW,CAACG,OAAO;MACvE1B,MAAM,EAAEA,MAAM,CAACyB,KAAK;MACpBE,IAAI,EAAE5B,WAAW,CAAC0B,KAAK;MACvBxB,QAAQ,EAAEA,QAAQ,CAACwB;IACrB,CAAC;EACH,CAAC,CAAC;EAEF,MAAMG,kBAAkB,GAAG/B,QAAQ,CAAC,MAAM;IACxC,MAAM;MAAES,SAAS;MAAEE,WAAW;MAAEE;IAAS,CAAC,GAAGL,YAAY,CAACoB,KAAK;IAC/D,MAAMI,SAAS,GAAG;MAChBC,OAAO,EAAEtB,WAAW,GAAG,iBAAiB,GAAG,cAAc;MACzDuB,SAAS,EAAE,cAAc;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,KAAK,EAAE;IACT,CAAC;IAED,OAAOJ,SAAS,CAACvB,SAAS,CAAC,IAAIuB,SAAS,CAACC,OAAO;EAClD,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMI,UAAU,GAAIC,OAAO,IAAK;IAC9BpC,WAAW,CAAC0B,KAAK,GAAGU,OAAO;IAC3BnC,MAAM,CAACyB,KAAK,GAAGU,OAAO,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;;IAEhE;IACA,IAAIA,OAAO,KAAK,YAAY,EAAE;MAC5B;MACAlC,QAAQ,CAACwB,KAAK,GAAG,KAAK;IACxB,CAAC,MAAM;MACLxB,QAAQ,CAACwB,KAAK,GAAG,IAAI;IACvB;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/ClC,YAAY,CAACqB,KAAK,GAAGY,SAAS;IAC9BhC,YAAY,CAACoB,KAAK,GAAG;MAAE,GAAGpB,YAAY,CAACoB,KAAK;MAAE,GAAGa;IAAQ,CAAC;;IAE1D;IACAC,iBAAiB,CAACF,SAAS,CAAC;EAC9B,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIF,SAAS,IAAK;IACvC,MAAMG,cAAc,GAAG;MACrB,QAAQ,EAAE;QACRC,WAAW,EAAE,QAAQ;QACrBjB,UAAU,EAAE,OAAO;QACnBkB,QAAQ,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe;MACzD,CAAC;MACD,OAAO,EAAE;QACPD,WAAW,EAAE,cAAc;QAC3BjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,cAAc;MACrD,CAAC;MACD,SAAS,EAAE;QACTD,WAAW,EAAE,MAAM;QACnBjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACnC,CAAC;MACD,eAAe,EAAE;QACfD,WAAW,EAAE,UAAU;QACvBjB,UAAU,EAAE,SAAS;QACrBkB,QAAQ,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,QAAQ;MACpD;IACF,CAAC;IAED,MAAMC,QAAQ,GAAGH,cAAc,CAACH,SAAS,CAAC,IAAIG,cAAc,CAACI,KAAK;IAClE1B,iBAAiB,CAACO,KAAK,GAAGkB,QAAQ,CAACnB,UAAU;IAC7CZ,eAAe,CAACa,KAAK,CAACV,aAAa,GAAG4B,QAAQ,CAACF,WAAW;EAC5D,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IACzClC,eAAe,CAACa,KAAK,GAAG;MAAE,GAAGb,eAAe,CAACa,KAAK;MAAE,GAAGqB;IAAY,CAAC;EACtE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,GAAG,IAAI,KAAK;IAC1C3B,mBAAmB,CAACI,KAAK,GAAG;MAC1BwB,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdH,KAAK;MACLI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC;MACrBG,QAAQ,EAAE;IACZ,CAAC;IACDlC,aAAa,CAACM,KAAK,GAAG,WAAW;IACjCvB,WAAW,CAACuB,KAAK,GAAG,IAAI;EAC1B,CAAC;;EAED;EACA,MAAM6B,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAIlC,mBAAmB,CAACI,KAAK,EAAE;MAC7BJ,mBAAmB,CAACI,KAAK,CAAC4B,QAAQ,CAACG,IAAI,CAAC;QACtC,GAAGD,OAAO;QACVE,SAAS,EAAE,IAAIP,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIrC,mBAAmB,CAACI,KAAK,EAAE;MAC7BL,mBAAmB,CAACK,KAAK,CAAC+B,IAAI,CAAC;QAC7B,GAAGnC,mBAAmB,CAACI,KAAK;QAC5BkC,OAAO,EAAE,IAAIT,IAAI,CAAC;MACpB,CAAC,CAAC;MACF7B,mBAAmB,CAACI,KAAK,GAAG,IAAI;IAClC;IACAN,aAAa,CAACM,KAAK,GAAG,MAAM;IAC5BvB,WAAW,CAACuB,KAAK,GAAG,KAAK;IACzBtB,UAAU,CAACsB,KAAK,GAAG,KAAK;EAC1B,CAAC;;EAED;EACA,MAAMmC,KAAK,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,SAAS,KAAK;IAC3C3D,UAAU,CAACsB,KAAK,GAAG,IAAI;IACvBN,aAAa,CAACM,KAAK,GAAG,UAAU;IAChCP,iBAAiB,CAACO,KAAK,GAAGqC,OAAO;;IAEjC;IACAC,UAAU,CAAC,MAAM;MACf5D,UAAU,CAACsB,KAAK,GAAG,KAAK;MACxBN,aAAa,CAACM,KAAK,GAAG,MAAM;MAC5BP,iBAAiB,CAACO,KAAK,GAAG,SAAS;IACrC,CAAC,EAAEoC,IAAI,CAACG,MAAM,GAAG,GAAG,CAAC,EAAC;EACxB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIzC,UAAU,IAAK;IACpCN,iBAAiB,CAACO,KAAK,GAAGD,UAAU;EACtC,CAAC;;EAED;EACA,MAAM0C,SAAS,GAAIC,MAAM,IAAK;IAC5BhD,aAAa,CAACM,KAAK,GAAG0C,MAAM;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIC,SAAS,IAAK;IACnC;IACA;IACA,MAAMC,QAAQ,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAChE,OAAOA,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,QAAQ,CAACN,MAAM,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMpC,OAAO,GAAG;MACdqC,QAAQ,EAAE,SAAS;MACnBpE,OAAO,EAAE,SAAS;MAClBqE,OAAO,EAAE,SAAS;MAClBtE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC,CAAC2B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS,GACtC,IAAI3B,IAAI,CAAC,CAAC,CAAC2B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG;IACxD,CAAC;IAEDxE,YAAY,CAACoB,KAAK,GAAG;MAAE,GAAGpB,YAAY,CAACoB,KAAK;MAAE,GAAGa;IAAQ,CAAC;IAC1D,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC;MAChDnF,WAAW,EAAEA,WAAW,CAAC0B,KAAK;MAC9Bb,eAAe,EAAEA,eAAe,CAACa,KAAK;MACtCpB,YAAY,EAAEA,YAAY,CAACoB;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;IAChD,IAAID,KAAK,EAAE;MACT,IAAI;QACF,MAAME,IAAI,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;QAC9BrF,WAAW,CAAC0B,KAAK,GAAG6D,IAAI,CAACvF,WAAW,IAAI,WAAW;QACnDa,eAAe,CAACa,KAAK,GAAG;UAAE,GAAGb,eAAe,CAACa,KAAK;UAAE,GAAG6D,IAAI,CAAC1E;QAAgB,CAAC;QAC7EP,YAAY,CAACoB,KAAK,GAAG;UAAE,GAAGpB,YAAY,CAACoB,KAAK;UAAE,GAAG6D,IAAI,CAACjF;QAAa,CAAC;MACtE,CAAC,CAAC,OAAOmF,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,KAAK,CAAC;MAClD;IACF;EACF,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1BR,YAAY,CAAC,CAAC;IACdT,cAAc,CAAC,CAAC;;IAEhB;IACAkB,WAAW,CAACd,YAAY,EAAE,KAAK,CAAC,EAAC;EACnC,CAAC;EAED,OAAO;IACL;IACA/E,WAAW;IACXC,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZO,eAAe;IACfM,iBAAiB;IACjBC,aAAa;IACbC,mBAAmB;IACnBC,mBAAmB;IAEnB;IACAC,gBAAgB;IAChBM,kBAAkB;IAElB;IACAM,UAAU;IACVE,WAAW;IACXG,iBAAiB;IACjBM,iBAAiB;IACjBE,iBAAiB;IACjBO,UAAU;IACVI,eAAe;IACfE,KAAK;IACLK,aAAa;IACbC,SAAS;IACTE,aAAa;IACbM,cAAc;IACdI,YAAY;IACZK,YAAY;IACZQ;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}