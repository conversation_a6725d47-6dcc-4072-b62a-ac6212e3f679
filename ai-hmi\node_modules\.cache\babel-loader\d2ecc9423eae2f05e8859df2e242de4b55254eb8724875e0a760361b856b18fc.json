{"ast": null, "code": "import { computed } from 'vue';\nimport { useLayoutStore } from '@/store';\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 卡片类型\n    cardType: {\n      type: String,\n      default: 'default'\n    },\n    // 卡片尺寸\n    size: {\n      type: String,\n      default: 'medium',\n      validator: value => ['small', 'medium', 'large', 'custom'].includes(value)\n    },\n    // 自定义尺寸（当size为custom时使用）\n    customSize: {\n      type: Object,\n      default: () => ({\n        width: 4,\n        height: 2\n      })\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 1\n      })\n    },\n    // 卡片标题\n    title: {\n      type: String,\n      default: ''\n    },\n    // 卡片描述\n    description: {\n      type: String,\n      default: ''\n    },\n    // 卡片图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 主题\n    theme: {\n      type: String,\n      default: 'glass',\n      validator: value => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\n    },\n    // 主题颜色\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    // 显示配置\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    actionable: {\n      type: Boolean,\n      default: false\n    },\n    actionText: {\n      type: String,\n      default: ''\n    },\n    closable: {\n      type: Boolean,\n      default: false\n    },\n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['click', 'action', 'close'],\n  setup(props, {\n    emit\n  }) {\n    const layoutStore = useLayoutStore();\n\n    // 计算卡片样式\n    const cardStyles = computed(() => {\n      let gridStyle = {};\n\n      // 根据尺寸类型获取网格样式\n      if (props.size === 'custom') {\n        gridStyle = layoutStore.getComponentPosition('custom', props.position);\n        // 注册自定义尺寸\n        layoutStore.registerComponentSize('custom', props.customSize);\n      } else {\n        const sizeMap = {\n          small: 'cardSmall',\n          medium: 'cardMedium',\n          large: 'cardLarge'\n        };\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position);\n      }\n\n      // 主题颜色样式\n      const themeStyle = {\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\n        '--card-text-color': props.themeColors.text || '#ffffff'\n      };\n      return {\n        ...gridStyle,\n        ...themeStyle\n      };\n    });\n\n    // 主题类名\n    const themeClass = computed(() => {\n      return `theme-${props.theme}`;\n    });\n\n    // 处理点击事件\n    const handleClick = () => {\n      if (props.clickable && !props.disabled) {\n        emit('click');\n      }\n    };\n\n    // 处理操作事件\n    const handleAction = () => {\n      if (props.actionable && !props.disabled) {\n        emit('action');\n      }\n    };\n    return {\n      cardStyles,\n      themeClass,\n      handleClick,\n      handleAction\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "useLayoutStore", "name", "props", "cardType", "type", "String", "default", "size", "validator", "value", "includes", "customSize", "Object", "width", "height", "position", "x", "y", "title", "description", "icon", "theme", "themeColors", "showHeader", "Boolean", "showFooter", "clickable", "actionable", "actionText", "closable", "loading", "disabled", "emits", "setup", "emit", "layoutStore", "cardStyles", "gridStyle", "getComponentPosition", "registerComponentSize", "sizeMap", "small", "medium", "large", "themeStyle", "primary", "secondary", "background", "text", "themeClass", "handleClick", "handleAction"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\BaseCard.vue"], "sourcesContent": ["<template>\n  <div \n    :class=\"['base-card', `card-${cardType}`, `size-${size}`, themeClass]\"\n    :style=\"cardStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 卡片头部 -->\n    <div v-if=\"showHeader\" class=\"card-header\">\n      <div class=\"header-left\">\n        <i v-if=\"icon\" :class=\"icon\" class=\"card-icon\"></i>\n        <h3 v-if=\"title\" class=\"card-title\">{{ title }}</h3>\n      </div>\n      <div class=\"header-right\">\n        <slot name=\"header-actions\">\n          <button v-if=\"closable\" @click.stop=\"$emit('close')\" class=\"close-btn\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </slot>\n      </div>\n    </div>\n\n    <!-- 卡片内容 -->\n    <div class=\"card-content\" :class=\"{ 'no-header': !showHeader }\">\n      <slot>\n        <div class=\"default-content\">\n          <p v-if=\"description\">{{ description }}</p>\n        </div>\n      </slot>\n    </div>\n\n    <!-- 卡片底部 -->\n    <div v-if=\"showFooter\" class=\"card-footer\">\n      <slot name=\"footer\">\n        <div class=\"footer-actions\">\n          <button v-if=\"actionable\" @click.stop=\"handleAction\" class=\"action-btn\">\n            {{ actionText || '操作' }}\n          </button>\n        </div>\n      </slot>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <div class=\"loading-spinner\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store'\n\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 卡片类型\n    cardType: {\n      type: String,\n      default: 'default'\n    },\n    \n    // 卡片尺寸\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large', 'custom'].includes(value)\n    },\n    \n    // 自定义尺寸（当size为custom时使用）\n    customSize: {\n      type: Object,\n      default: () => ({ width: 4, height: 2 })\n    },\n    \n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 1 })\n    },\n    \n    // 卡片标题\n    title: {\n      type: String,\n      default: ''\n    },\n    \n    // 卡片描述\n    description: {\n      type: String,\n      default: ''\n    },\n    \n    // 卡片图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    \n    // 主题\n    theme: {\n      type: String,\n      default: 'glass',\n      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\n    },\n    \n    // 主题颜色\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    \n    // 显示配置\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    \n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    \n    actionable: {\n      type: Boolean,\n      default: false\n    },\n    \n    actionText: {\n      type: String,\n      default: ''\n    },\n    \n    closable: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    \n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  \n  emits: ['click', 'action', 'close'],\n  \n  setup(props, { emit }) {\n    const layoutStore = useLayoutStore()\n    \n    // 计算卡片样式\n    const cardStyles = computed(() => {\n      let gridStyle = {}\n      \n      // 根据尺寸类型获取网格样式\n      if (props.size === 'custom') {\n        gridStyle = layoutStore.getComponentPosition('custom', props.position)\n        // 注册自定义尺寸\n        layoutStore.registerComponentSize('custom', props.customSize)\n      } else {\n        const sizeMap = {\n          small: 'cardSmall',\n          medium: 'cardMedium', \n          large: 'cardLarge'\n        }\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position)\n      }\n      \n      // 主题颜色样式\n      const themeStyle = {\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\n        '--card-text-color': props.themeColors.text || '#ffffff'\n      }\n      \n      return {\n        ...gridStyle,\n        ...themeStyle\n      }\n    })\n    \n    // 主题类名\n    const themeClass = computed(() => {\n      return `theme-${props.theme}`\n    })\n    \n    // 处理点击事件\n    const handleClick = () => {\n      if (props.clickable && !props.disabled) {\n        emit('click')\n      }\n    }\n    \n    // 处理操作事件\n    const handleAction = () => {\n      if (props.actionable && !props.disabled) {\n        emit('action')\n      }\n    }\n    \n    return {\n      cardStyles,\n      themeClass,\n      handleClick,\n      handleAction\n    }\n  }\n}\n</script>\n\n<style scoped>\n.base-card {\n  position: relative;\n  border-radius: 15px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: default;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 主题样式 */\n.theme-glass {\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.theme-solid {\n  background: var(--card-primary-color, #4a90e2);\n  border: none;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.theme-minimal {\n  background: transparent;\n  border: 1px solid var(--card-primary-color, #4a90e2);\n  box-shadow: none;\n}\n\n.theme-gradient {\n  background: linear-gradient(135deg, \n    var(--card-primary-color, #4a90e2) 0%, \n    var(--card-secondary-color, #7ed321) 100%);\n  border: none;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n}\n\n/* 可点击状态 */\n.base-card.clickable {\n  cursor: pointer;\n}\n\n.base-card.clickable:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);\n}\n\n/* 禁用状态 */\n.base-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 卡片头部 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.card-icon {\n  font-size: 18px;\n  color: var(--card-primary-color, #4a90e2);\n}\n\n.card-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--card-text-color, #ffffff);\n}\n\n.close-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--card-text-color, #ffffff);\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n/* 卡片内容 */\n.card-content {\n  flex: 1;\n  padding: 20px;\n  color: var(--card-text-color, #ffffff);\n  overflow: hidden;\n}\n\n.card-content.no-header {\n  padding-top: 20px;\n}\n\n.default-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n/* 卡片底部 */\n.card-footer {\n  padding: 15px 20px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.footer-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: 1px solid var(--card-primary-color, #4a90e2);\n  background: var(--card-primary-color, #4a90e2);\n  color: white;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  background: var(--card-secondary-color, #7ed321);\n  border-color: var(--card-secondary-color, #7ed321);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(5px);\n}\n\n.loading-spinner {\n  color: var(--card-primary-color, #4a90e2);\n  font-size: 24px;\n}\n</style>\n"], "mappings": "AAmDA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,cAAa,QAAS,SAAQ;AAEvC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACL;IACAC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAC,IAAI,EAAE;MACJH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBE,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACD,KAAK;IAC7E,CAAC;IAED;IACAE,UAAU,EAAE;MACVP,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO;QAAEO,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;IACzC,CAAC;IAED;IACAC,QAAQ,EAAE;MACRX,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO;QAAEU,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IAED;IACAC,KAAK,EAAE;MACLd,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAa,WAAW,EAAE;MACXf,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAc,IAAI,EAAE;MACJhB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAe,KAAK,EAAE;MACLjB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBE,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACD,KAAK;IAChF,CAAC;IAED;IACAa,WAAW,EAAE;MACXlB,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IAED;IACAiB,UAAU,EAAE;MACVnB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDmB,UAAU,EAAE;MACVrB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAED;IACAoB,SAAS,EAAE;MACTtB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDqB,UAAU,EAAE;MACVvB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDsB,UAAU,EAAE;MACVxB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAEDuB,QAAQ,EAAE;MACRzB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAED;IACAwB,OAAO,EAAE;MACP1B,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDyB,QAAQ,EAAE;MACR3B,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX;EACF,CAAC;EAED0B,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;EAEnCC,KAAKA,CAAC/B,KAAK,EAAE;IAAEgC;EAAK,CAAC,EAAE;IACrB,MAAMC,WAAU,GAAInC,cAAc,CAAC;;IAEnC;IACA,MAAMoC,UAAS,GAAIrC,QAAQ,CAAC,MAAM;MAChC,IAAIsC,SAAQ,GAAI,CAAC;;MAEjB;MACA,IAAInC,KAAK,CAACK,IAAG,KAAM,QAAQ,EAAE;QAC3B8B,SAAQ,GAAIF,WAAW,CAACG,oBAAoB,CAAC,QAAQ,EAAEpC,KAAK,CAACa,QAAQ;QACrE;QACAoB,WAAW,CAACI,qBAAqB,CAAC,QAAQ,EAAErC,KAAK,CAACS,UAAU;MAC9D,OAAO;QACL,MAAM6B,OAAM,GAAI;UACdC,KAAK,EAAE,WAAW;UAClBC,MAAM,EAAE,YAAY;UACpBC,KAAK,EAAE;QACT;QACAN,SAAQ,GAAIF,WAAW,CAACG,oBAAoB,CAACE,OAAO,CAACtC,KAAK,CAACK,IAAI,CAAC,EAAEL,KAAK,CAACa,QAAQ;MAClF;;MAEA;MACA,MAAM6B,UAAS,GAAI;QACjB,sBAAsB,EAAE1C,KAAK,CAACoB,WAAW,CAACuB,OAAM,IAAK,SAAS;QAC9D,wBAAwB,EAAE3C,KAAK,CAACoB,WAAW,CAACwB,SAAQ,IAAK,SAAS;QAClE,mBAAmB,EAAE5C,KAAK,CAACoB,WAAW,CAACyB,UAAS,IAAK,0BAA0B;QAC/E,mBAAmB,EAAE7C,KAAK,CAACoB,WAAW,CAAC0B,IAAG,IAAK;MACjD;MAEA,OAAO;QACL,GAAGX,SAAS;QACZ,GAAGO;MACL;IACF,CAAC;;IAED;IACA,MAAMK,UAAS,GAAIlD,QAAQ,CAAC,MAAM;MAChC,OAAO,SAASG,KAAK,CAACmB,KAAK,EAAC;IAC9B,CAAC;;IAED;IACA,MAAM6B,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIhD,KAAK,CAACwB,SAAQ,IAAK,CAACxB,KAAK,CAAC6B,QAAQ,EAAE;QACtCG,IAAI,CAAC,OAAO;MACd;IACF;;IAEA;IACA,MAAMiB,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIjD,KAAK,CAACyB,UAAS,IAAK,CAACzB,KAAK,CAAC6B,QAAQ,EAAE;QACvCG,IAAI,CAAC,QAAQ;MACf;IACF;IAEA,OAAO;MACLE,UAAU;MACVa,UAAU;MACVC,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}