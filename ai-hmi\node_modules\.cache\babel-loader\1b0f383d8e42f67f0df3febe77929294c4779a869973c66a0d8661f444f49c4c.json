{"ast": null, "code": "import { ref, computed, onMounted } from 'vue';\nimport BaseCard from '../BaseCard.vue';\nexport default {\n  name: 'KidEducationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 2\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#ff6b6b',\n        secondary: '#4ecdc4',\n        background: 'rgba(255, 107, 107, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式状态\n    const selectedMode = ref('story');\n    const isPlaying = ref(false);\n    const selectedOption = ref(null);\n    const showAnswer = ref(false);\n    const gameNumber = ref(0);\n    const todayStars = ref(3);\n\n    // 当前课程数据\n    const currentLesson = ref({\n      title: '认识动物',\n      description: '学习各种动物的名称和特征',\n      icon: 'fas fa-paw',\n      progress: 65\n    });\n\n    // 学习模式\n    const learningModes = ref([{\n      id: 'story',\n      name: '故事',\n      icon: 'fas fa-book'\n    }, {\n      id: 'quiz',\n      name: '问答',\n      icon: 'fas fa-question-circle'\n    }, {\n      id: 'game',\n      name: '游戏',\n      icon: 'fas fa-gamepad'\n    }]);\n\n    // 故事内容\n    const stories = ref([{\n      title: '小兔子的冒险',\n      content: '从前有一只小兔子，它住在美丽的森林里...'\n    }, {\n      title: '勇敢的小狮子',\n      content: '在非洲大草原上，有一只勇敢的小狮子...'\n    }]);\n    const currentStoryIndex = ref(0);\n    const currentStory = computed(() => stories.value[currentStoryIndex.value]);\n\n    // 问答内容\n    const quizzes = ref([{\n      question: '小兔子最喜欢吃什么？',\n      options: ['胡萝卜', '肉', '鱼', '草'],\n      correct: 0\n    }, {\n      question: '狮子是什么动物？',\n      options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\n      correct: 1\n    }]);\n    const currentQuizIndex = ref(0);\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value]);\n\n    // 事件处理\n    const handleCardClick = () => {\n      emit('card-click', 'kidEducation');\n    };\n    const selectLearningMode = mode => {\n      selectedMode.value = mode.id;\n      emit('mode-changed', mode.id);\n\n      // 重置相关状态\n      if (mode.id === 'quiz') {\n        selectedOption.value = null;\n        showAnswer.value = false;\n      }\n    };\n    const previousStory = () => {\n      if (currentStoryIndex.value > 0) {\n        currentStoryIndex.value--;\n      }\n    };\n    const nextStory = () => {\n      if (currentStoryIndex.value < stories.value.length - 1) {\n        currentStoryIndex.value++;\n      }\n    };\n    const toggleStoryPlay = () => {\n      isPlaying.value = !isPlaying.value;\n      // 这里可以集成TTS服务来朗读故事\n    };\n    const selectQuizOption = index => {\n      if (showAnswer.value) return;\n      selectedOption.value = index;\n      showAnswer.value = true;\n\n      // 2秒后自动进入下一题\n      setTimeout(() => {\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\n          currentQuizIndex.value++;\n          selectedOption.value = null;\n          showAnswer.value = false;\n        } else {\n          // 完成所有问题\n          emit('lesson-completed', 'quiz');\n        }\n      }, 2000);\n    };\n    const gameAction = action => {\n      switch (action) {\n        case 'add':\n          gameNumber.value++;\n          break;\n        case 'subtract':\n          if (gameNumber.value > 0) {\n            gameNumber.value--;\n          }\n          break;\n        case 'reset':\n          gameNumber.value = 0;\n          break;\n      }\n    };\n    const openFullEducation = () => {\n      // 打开完整的教育界面\n      console.log('打开完整教育界面');\n    };\n\n    // 初始化\n    onMounted(() => {\n      // 可以在这里加载用户的学习进度\n      console.log('儿童教育卡片已加载');\n    });\n    return {\n      selectedMode,\n      isPlaying,\n      selectedOption,\n      showAnswer,\n      gameNumber,\n      todayStars,\n      currentLesson,\n      learningModes,\n      currentStory,\n      currentQuiz,\n      handleCardClick,\n      selectLearningMode,\n      previousStory,\n      nextStory,\n      toggleStoryPlay,\n      selectQuizOption,\n      gameAction,\n      openFullEducation\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "BaseCard", "name", "components", "props", "position", "type", "Object", "default", "x", "y", "theme", "String", "themeColors", "primary", "secondary", "background", "text", "emits", "setup", "emit", "selectedMode", "isPlaying", "selectedOption", "showAnswer", "gameNumber", "todayStars", "<PERSON><PERSON><PERSON><PERSON>", "title", "description", "icon", "progress", "learningModes", "id", "stories", "content", "currentStoryIndex", "currentStory", "value", "quizzes", "question", "options", "correct", "currentQuizIndex", "currentQuiz", "handleCardClick", "selectLearningMode", "mode", "previousStory", "nextStory", "length", "toggleStoryPlay", "selectQuizOption", "index", "setTimeout", "gameAction", "action", "openFullEducation", "console", "log"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    card-type=\"kidEducation\"\n    size=\"large\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :title=\"'儿童教育'\"\n    :icon=\"'fas fa-graduation-cap'\"\n    :clickable=\"true\"\n    :show-footer=\"true\"\n    @click=\"handleCardClick\"\n    class=\"kid-education-card\"\n  >\n    <div class=\"education-content\">\n      <!-- 当前学习内容 -->\n      <div class=\"current-lesson\">\n        <div class=\"lesson-header\">\n          <div class=\"lesson-icon\">\n            <i :class=\"currentLesson.icon\"></i>\n          </div>\n          <div class=\"lesson-info\">\n            <h4 class=\"lesson-title\">{{ currentLesson.title }}</h4>\n            <p class=\"lesson-description\">{{ currentLesson.description }}</p>\n          </div>\n        </div>\n        \n        <!-- 学习进度 -->\n        <div class=\"lesson-progress\">\n          <div class=\"progress-bar\">\n            <div \n              class=\"progress-fill\" \n              :style=\"{ width: `${currentLesson.progress}%` }\"\n            ></div>\n          </div>\n          <span class=\"progress-text\">{{ currentLesson.progress }}% 完成</span>\n        </div>\n      </div>\n\n      <!-- 学习模式选择 -->\n      <div class=\"learning-modes\">\n        <button \n          v-for=\"mode in learningModes\" \n          :key=\"mode.id\"\n          @click=\"selectLearningMode(mode)\"\n          :class=\"['mode-btn', { active: selectedMode === mode.id }]\"\n        >\n          <i :class=\"mode.icon\"></i>\n          <span>{{ mode.name }}</span>\n        </button>\n      </div>\n\n      <!-- 互动区域 -->\n      <div class=\"interaction-area\">\n        <div v-if=\"selectedMode === 'story'\" class=\"story-mode\">\n          <div class=\"story-content\">\n            <h5>{{ currentStory.title }}</h5>\n            <p>{{ currentStory.content }}</p>\n          </div>\n          <div class=\"story-controls\">\n            <button @click=\"previousStory\" class=\"story-btn\">\n              <i class=\"fas fa-step-backward\"></i>\n            </button>\n            <button @click=\"toggleStoryPlay\" class=\"story-btn play-btn\">\n              <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n            </button>\n            <button @click=\"nextStory\" class=\"story-btn\">\n              <i class=\"fas fa-step-forward\"></i>\n            </button>\n          </div>\n        </div>\n\n        <div v-else-if=\"selectedMode === 'quiz'\" class=\"quiz-mode\">\n          <div class=\"quiz-question\">\n            <h5>{{ currentQuiz.question }}</h5>\n            <div class=\"quiz-options\">\n              <button \n                v-for=\"(option, index) in currentQuiz.options\" \n                :key=\"index\"\n                @click=\"selectQuizOption(index)\"\n                :class=\"['quiz-option', { \n                  selected: selectedOption === index,\n                  correct: showAnswer && index === currentQuiz.correct,\n                  wrong: showAnswer && selectedOption === index && index !== currentQuiz.correct\n                }]\"\n              >\n                {{ option }}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div v-else-if=\"selectedMode === 'game'\" class=\"game-mode\">\n          <div class=\"game-content\">\n            <h5>数字游戏</h5>\n            <div class=\"number-game\">\n              <div class=\"game-display\">{{ gameNumber }}</div>\n              <div class=\"game-controls\">\n                <button @click=\"gameAction('add')\" class=\"game-btn\">+1</button>\n                <button @click=\"gameAction('subtract')\" class=\"game-btn\">-1</button>\n                <button @click=\"gameAction('reset')\" class=\"game-btn\">重置</button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"card-footer-content\">\n        <div class=\"achievement-info\">\n          <i class=\"fas fa-star\"></i>\n          <span>今日获得 {{ todayStars }} 颗星</span>\n        </div>\n        <button @click=\"openFullEducation\" class=\"expand-btn\">\n          <i class=\"fas fa-expand\"></i>\n          <span>展开</span>\n        </button>\n      </div>\n    </template>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport BaseCard from '../BaseCard.vue'\n\nexport default {\n  name: 'KidEducationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({ x: 1, y: 2 })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#ff6b6b',\n        secondary: '#4ecdc4',\n        background: 'rgba(255, 107, 107, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  \n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\n  \n  setup(props, { emit }) {\n    // 响应式状态\n    const selectedMode = ref('story')\n    const isPlaying = ref(false)\n    const selectedOption = ref(null)\n    const showAnswer = ref(false)\n    const gameNumber = ref(0)\n    const todayStars = ref(3)\n    \n    // 当前课程数据\n    const currentLesson = ref({\n      title: '认识动物',\n      description: '学习各种动物的名称和特征',\n      icon: 'fas fa-paw',\n      progress: 65\n    })\n    \n    // 学习模式\n    const learningModes = ref([\n      { id: 'story', name: '故事', icon: 'fas fa-book' },\n      { id: 'quiz', name: '问答', icon: 'fas fa-question-circle' },\n      { id: 'game', name: '游戏', icon: 'fas fa-gamepad' }\n    ])\n    \n    // 故事内容\n    const stories = ref([\n      {\n        title: '小兔子的冒险',\n        content: '从前有一只小兔子，它住在美丽的森林里...'\n      },\n      {\n        title: '勇敢的小狮子',\n        content: '在非洲大草原上，有一只勇敢的小狮子...'\n      }\n    ])\n    \n    const currentStoryIndex = ref(0)\n    const currentStory = computed(() => stories.value[currentStoryIndex.value])\n    \n    // 问答内容\n    const quizzes = ref([\n      {\n        question: '小兔子最喜欢吃什么？',\n        options: ['胡萝卜', '肉', '鱼', '草'],\n        correct: 0\n      },\n      {\n        question: '狮子是什么动物？',\n        options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\n        correct: 1\n      }\n    ])\n    \n    const currentQuizIndex = ref(0)\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value])\n    \n    // 事件处理\n    const handleCardClick = () => {\n      emit('card-click', 'kidEducation')\n    }\n    \n    const selectLearningMode = (mode) => {\n      selectedMode.value = mode.id\n      emit('mode-changed', mode.id)\n      \n      // 重置相关状态\n      if (mode.id === 'quiz') {\n        selectedOption.value = null\n        showAnswer.value = false\n      }\n    }\n    \n    const previousStory = () => {\n      if (currentStoryIndex.value > 0) {\n        currentStoryIndex.value--\n      }\n    }\n    \n    const nextStory = () => {\n      if (currentStoryIndex.value < stories.value.length - 1) {\n        currentStoryIndex.value++\n      }\n    }\n    \n    const toggleStoryPlay = () => {\n      isPlaying.value = !isPlaying.value\n      // 这里可以集成TTS服务来朗读故事\n    }\n    \n    const selectQuizOption = (index) => {\n      if (showAnswer.value) return\n      \n      selectedOption.value = index\n      showAnswer.value = true\n      \n      // 2秒后自动进入下一题\n      setTimeout(() => {\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\n          currentQuizIndex.value++\n          selectedOption.value = null\n          showAnswer.value = false\n        } else {\n          // 完成所有问题\n          emit('lesson-completed', 'quiz')\n        }\n      }, 2000)\n    }\n    \n    const gameAction = (action) => {\n      switch (action) {\n        case 'add':\n          gameNumber.value++\n          break\n        case 'subtract':\n          if (gameNumber.value > 0) {\n            gameNumber.value--\n          }\n          break\n        case 'reset':\n          gameNumber.value = 0\n          break\n      }\n    }\n    \n    const openFullEducation = () => {\n      // 打开完整的教育界面\n      console.log('打开完整教育界面')\n    }\n    \n    // 初始化\n    onMounted(() => {\n      // 可以在这里加载用户的学习进度\n      console.log('儿童教育卡片已加载')\n    })\n    \n    return {\n      selectedMode,\n      isPlaying,\n      selectedOption,\n      showAnswer,\n      gameNumber,\n      todayStars,\n      currentLesson,\n      learningModes,\n      currentStory,\n      currentQuiz,\n      handleCardClick,\n      selectLearningMode,\n      previousStory,\n      nextStory,\n      toggleStoryPlay,\n      selectQuizOption,\n      gameAction,\n      openFullEducation\n    }\n  }\n}\n</script>\n\n<style scoped>\n.kid-education-card {\n  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);\n}\n\n.education-content {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  height: 100%;\n}\n\n/* 当前课程 */\n.current-lesson {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 15px;\n}\n\n.lesson-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 10px;\n}\n\n.lesson-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: var(--card-primary-color, #ff6b6b);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n}\n\n.lesson-info h4 {\n  margin: 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 16px;\n}\n\n.lesson-info p {\n  margin: 5px 0 0 0;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n  font-size: 12px;\n}\n\n.lesson-progress {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.progress-bar {\n  flex: 1;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: var(--card-secondary-color, #4ecdc4);\n  border-radius: 3px;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.8;\n}\n\n/* 学习模式 */\n.learning-modes {\n  display: flex;\n  gap: 8px;\n}\n\n.mode-btn {\n  flex: 1;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  transition: all 0.3s ease;\n}\n\n.mode-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.mode-btn.active {\n  background: var(--card-primary-color, #ff6b6b);\n  border-color: var(--card-primary-color, #ff6b6b);\n}\n\n.mode-btn i {\n  font-size: 16px;\n}\n\n/* 互动区域 */\n.interaction-area {\n  flex: 1;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 10px;\n  padding: 15px;\n  min-height: 120px;\n}\n\n/* 故事模式 */\n.story-content h5 {\n  margin: 0 0 10px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n}\n\n.story-content p {\n  margin: 0;\n  color: var(--card-text-color, #ffffff);\n  opacity: 0.9;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\n.story-controls {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 15px;\n}\n\n.story-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: var(--card-primary-color, #ff6b6b);\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.story-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n\n.play-btn {\n  width: 40px;\n  height: 40px;\n}\n\n/* 问答模式 */\n.quiz-question h5 {\n  margin: 0 0 15px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n}\n\n.quiz-options {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.quiz-option {\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 6px;\n  color: var(--card-text-color, #ffffff);\n  cursor: pointer;\n  font-size: 11px;\n  transition: all 0.3s ease;\n}\n\n.quiz-option:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.quiz-option.selected {\n  background: var(--card-primary-color, #ff6b6b);\n}\n\n.quiz-option.correct {\n  background: #2ecc71;\n  border-color: #2ecc71;\n}\n\n.quiz-option.wrong {\n  background: #e74c3c;\n  border-color: #e74c3c;\n}\n\n/* 游戏模式 */\n.game-content h5 {\n  margin: 0 0 15px 0;\n  color: var(--card-text-color, #ffffff);\n  font-size: 14px;\n  text-align: center;\n}\n\n.number-game {\n  text-align: center;\n}\n\n.game-display {\n  font-size: 32px;\n  font-weight: bold;\n  color: var(--card-primary-color, #ff6b6b);\n  margin-bottom: 15px;\n}\n\n.game-controls {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n}\n\n.game-btn {\n  padding: 6px 12px;\n  background: var(--card-primary-color, #ff6b6b);\n  border: none;\n  border-radius: 6px;\n  color: white;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s ease;\n}\n\n.game-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n\n/* 卡片底部 */\n.card-footer-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.achievement-info {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-size: 12px;\n  color: var(--card-text-color, #ffffff);\n}\n\n.achievement-info i {\n  color: #f1c40f;\n}\n\n.expand-btn {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  padding: 6px 12px;\n  background: var(--card-primary-color, #ff6b6b);\n  border: none;\n  border-radius: 6px;\n  color: white;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s ease;\n}\n\n.expand-btn:hover {\n  background: var(--card-secondary-color, #4ecdc4);\n}\n</style>\n"], "mappings": "AA4HA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,OAAOC,QAAO,MAAO,iBAAgB;AAErC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDK,WAAW,EAAE;MACXP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QACdM,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,0BAA0B;QACtCC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAEDC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAC;EAEzDC,KAAKA,CAACf,KAAK,EAAE;IAAEgB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAIvB,GAAG,CAAC,OAAO;IAChC,MAAMwB,SAAQ,GAAIxB,GAAG,CAAC,KAAK;IAC3B,MAAMyB,cAAa,GAAIzB,GAAG,CAAC,IAAI;IAC/B,MAAM0B,UAAS,GAAI1B,GAAG,CAAC,KAAK;IAC5B,MAAM2B,UAAS,GAAI3B,GAAG,CAAC,CAAC;IACxB,MAAM4B,UAAS,GAAI5B,GAAG,CAAC,CAAC;;IAExB;IACA,MAAM6B,aAAY,GAAI7B,GAAG,CAAC;MACxB8B,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,cAAc;MAC3BC,IAAI,EAAE,YAAY;MAClBC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,aAAY,GAAIlC,GAAG,CAAC,CACxB;MAAEmC,EAAE,EAAE,OAAO;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAc,CAAC,EAChD;MAAEG,EAAE,EAAE,MAAM;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAyB,CAAC,EAC1D;MAAEG,EAAE,EAAE,MAAM;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAiB,EAClD;;IAED;IACA,MAAMI,OAAM,GAAIpC,GAAG,CAAC,CAClB;MACE8B,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAE;IACX,CAAC,EACD;MACEP,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAE;IACX,EACD;IAED,MAAMC,iBAAgB,GAAItC,GAAG,CAAC,CAAC;IAC/B,MAAMuC,YAAW,GAAItC,QAAQ,CAAC,MAAMmC,OAAO,CAACI,KAAK,CAACF,iBAAiB,CAACE,KAAK,CAAC;;IAE1E;IACA,MAAMC,OAAM,GAAIzC,GAAG,CAAC,CAClB;MACE0C,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BC,OAAO,EAAE;IACX,CAAC,EACD;MACEF,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;MACxCC,OAAO,EAAE;IACX,EACD;IAED,MAAMC,gBAAe,GAAI7C,GAAG,CAAC,CAAC;IAC9B,MAAM8C,WAAU,GAAI7C,QAAQ,CAAC,MAAMwC,OAAO,CAACD,KAAK,CAACK,gBAAgB,CAACL,KAAK,CAAC;;IAExE;IACA,MAAMO,eAAc,GAAIA,CAAA,KAAM;MAC5BzB,IAAI,CAAC,YAAY,EAAE,cAAc;IACnC;IAEA,MAAM0B,kBAAiB,GAAKC,IAAI,IAAK;MACnC1B,YAAY,CAACiB,KAAI,GAAIS,IAAI,CAACd,EAAC;MAC3Bb,IAAI,CAAC,cAAc,EAAE2B,IAAI,CAACd,EAAE;;MAE5B;MACA,IAAIc,IAAI,CAACd,EAAC,KAAM,MAAM,EAAE;QACtBV,cAAc,CAACe,KAAI,GAAI,IAAG;QAC1Bd,UAAU,CAACc,KAAI,GAAI,KAAI;MACzB;IACF;IAEA,MAAMU,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIZ,iBAAiB,CAACE,KAAI,GAAI,CAAC,EAAE;QAC/BF,iBAAiB,CAACE,KAAK,EAAC;MAC1B;IACF;IAEA,MAAMW,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIb,iBAAiB,CAACE,KAAI,GAAIJ,OAAO,CAACI,KAAK,CAACY,MAAK,GAAI,CAAC,EAAE;QACtDd,iBAAiB,CAACE,KAAK,EAAC;MAC1B;IACF;IAEA,MAAMa,eAAc,GAAIA,CAAA,KAAM;MAC5B7B,SAAS,CAACgB,KAAI,GAAI,CAAChB,SAAS,CAACgB,KAAI;MACjC;IACF;IAEA,MAAMc,gBAAe,GAAKC,KAAK,IAAK;MAClC,IAAI7B,UAAU,CAACc,KAAK,EAAE;MAEtBf,cAAc,CAACe,KAAI,GAAIe,KAAI;MAC3B7B,UAAU,CAACc,KAAI,GAAI,IAAG;;MAEtB;MACAgB,UAAU,CAAC,MAAM;QACf,IAAIX,gBAAgB,CAACL,KAAI,GAAIC,OAAO,CAACD,KAAK,CAACY,MAAK,GAAI,CAAC,EAAE;UACrDP,gBAAgB,CAACL,KAAK,EAAC;UACvBf,cAAc,CAACe,KAAI,GAAI,IAAG;UAC1Bd,UAAU,CAACc,KAAI,GAAI,KAAI;QACzB,OAAO;UACL;UACAlB,IAAI,CAAC,kBAAkB,EAAE,MAAM;QACjC;MACF,CAAC,EAAE,IAAI;IACT;IAEA,MAAMmC,UAAS,GAAKC,MAAM,IAAK;MAC7B,QAAQA,MAAM;QACZ,KAAK,KAAK;UACR/B,UAAU,CAACa,KAAK,EAAC;UACjB;QACF,KAAK,UAAU;UACb,IAAIb,UAAU,CAACa,KAAI,GAAI,CAAC,EAAE;YACxBb,UAAU,CAACa,KAAK,EAAC;UACnB;UACA;QACF,KAAK,OAAO;UACVb,UAAU,CAACa,KAAI,GAAI;UACnB;MACJ;IACF;IAEA,MAAMmB,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACAC,OAAO,CAACC,GAAG,CAAC,UAAU;IACxB;;IAEA;IACA3D,SAAS,CAAC,MAAM;MACd;MACA0D,OAAO,CAACC,GAAG,CAAC,WAAW;IACzB,CAAC;IAED,OAAO;MACLtC,YAAY;MACZC,SAAS;MACTC,cAAc;MACdC,UAAU;MACVC,UAAU;MACVC,UAAU;MACVC,aAAa;MACbK,aAAa;MACbK,YAAY;MACZO,WAAW;MACXC,eAAe;MACfC,kBAAkB;MAClBE,aAAa;MACbC,SAAS;MACTE,eAAe;MACfC,gBAAgB;MAChBG,UAAU;MACVE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}