{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport pinia from './store';\nconst app = createApp(App);\n\n// 使用Pinia状态管理\napp.use(pinia);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "pinia", "app", "use", "mount"], "sources": ["F:/工作/theme/ai-hmi/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport pinia from './store'\r\n\r\nconst app = createApp(App)\r\n\r\n// 使用Pinia状态管理\r\napp.use(pinia)\r\n\r\napp.mount('#app')\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAE3B,MAAMC,GAAG,GAAGH,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAE,GAAG,CAACC,GAAG,CAACF,KAAK,CAAC;AAEdC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}