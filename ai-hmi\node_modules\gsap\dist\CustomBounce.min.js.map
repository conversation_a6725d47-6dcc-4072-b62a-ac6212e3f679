{"version": 3, "file": "CustomBounce.min.js", "sources": ["../src/CustomBounce.js"], "sourcesContent": ["/*!\n * CustomBounce 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, createCustomEase,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_initCore = required => {\n\t\tgsap = _getGSAP();\n\t\tcreateCustomEase = gsap && gsap.parseEase(\"_CE\");\n\t\tif (createCustomEase) {\n\t\t\t_coreInitted = 1;\n\t\t\tgsap.parseEase(\"bounce\").config = vars => typeof(vars) === \"object\" ? _create(\"\", vars) : _create(\"bounce(\" + vars + \")\", {strength:+vars});\n\t\t} else {\n\t\t\trequired && console.warn(\"Please gsap.registerPlugin(CustomEase, CustomBounce)\");\n\t\t}\n\t},\n\t_normalizeX = a => { //scales all the x values in an array [x, y, x, y...] AND rounds them to the closest hundredth (decimal)\n\t\tlet l = a.length,\n\t\t\ts = 1 / a[l - 2],\n\t\t\trnd = 1000,\n\t\t\ti;\n\t\tfor (i = 2; i < l; i += 2) {\n\t\t\ta[i] = ~~(a[i] * s * rnd) / rnd;\n\t\t}\n\t\ta[l - 2] = 1; //in case there are any rounding errors. x should always end at 1.\n\t},\n\t_bonusValidated = 1, //<name>CustomBounce</name>\n\t_create = (id, vars) => {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore(1);\n\t\t}\n\t\tvars = vars || {};\n\t\tif (_bonusValidated) {\n\t\t\tlet max = 0.999,\n\t\t\t\tdecay = Math.min(max, vars.strength || 0.7),  // Math.min(0.999, 1 - 0.3 / (vars.strength || 1)),\n\t\t\t\tdecayX = decay,\n\t\t\t\tgap = (vars.squash || 0) / 100,\n\t\t\t\toriginalGap = gap,\n\t\t\t\tslope = 1 / 0.03,\n\t\t\t\tw = 0.2,\n\t\t\t\th = 1,\n\t\t\t\tprevX = 0.1,\n\t\t\t\tpath = [0, 0, 0.07, 0, 0.1, 1, 0.1, 1],\n\t\t\t\tsquashPath = [0, 0, 0, 0, 0.1, 0, 0.1, 0],\n\t\t\t\tcp1, cp2, x, y, i, nextX, squishMagnitude;\n\t\t\tfor (i = 0; i < 200; i++) {\n\t\t\t\tw *= decayX * ((decayX + 1) / 2);\n\t\t\t\th *= decay * decay;\n\t\t\t\tnextX = prevX + w;\n\t\t\t\tx = prevX + w * 0.49;\n\t\t\t\ty = 1 - h;\n\t\t\t\tcp1 = prevX + h / slope;\n\t\t\t\tcp2 = x + (x - cp1) * 0.8;\n\n\t\t\t\tif (gap) {\n\t\t\t\t\tprevX += gap;\n\t\t\t\t\tcp1 += gap;\n\t\t\t\t\tx += gap;\n\t\t\t\t\tcp2 += gap;\n\t\t\t\t\tnextX += gap;\n\t\t\t\t\tsquishMagnitude = gap / originalGap;\n\t\t\t\t\tsquashPath.push(\n\t\t\t\t\t\tprevX - gap, 0,\n\t\t\t\t\t\tprevX - gap, squishMagnitude,\n\t\t\t\t\t\tprevX - gap / 2, squishMagnitude, //center peak anchor\n\t\t\t\t\t\tprevX, squishMagnitude,\n\t\t\t\t\t\tprevX, 0,\n\t\t\t\t\t\tprevX, 0, //base anchor\n\t\t\t\t\t\tprevX, squishMagnitude * -0.6,\n\t\t\t\t\t\tprevX + (nextX - prevX) / 6, 0,\n\t\t\t\t\t\tnextX, 0\n\t\t\t\t\t);\n\t\t\t\t\tpath.push(prevX - gap, 1,\n\t\t\t\t\t\tprevX, 1,\n\t\t\t\t\t\tprevX, 1);\n\t\t\t\t\tgap *= decay * decay;\n\t\t\t\t}\n\n\t\t\t\tpath.push(prevX, 1,\n\t\t\t\t\tcp1, y,\n\t\t\t\t\tx, y,\n\t\t\t\t\tcp2, y,\n\t\t\t\t\tnextX, 1,\n\t\t\t\t\tnextX, 1);\n\n\t\t\t\tdecay *= 0.95;\n\t\t\t\tslope = h / (nextX - cp2);\n\t\t\t\tprevX = nextX;\n\t\t\t\tif (y > max) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (vars.endAtStart && vars.endAtStart !== \"false\") {\n\t\t\t\tx = -0.1;\n\t\t\t\tpath.unshift(x, 1, x, 1, -0.07, 0);\n\t\t\t\tif (originalGap) {\n\t\t\t\t\tgap = originalGap * 2.5; //make the initial anticipation squash longer (more realistic)\n\t\t\t\t\tx -= gap;\n\t\t\t\t\tpath.unshift(x, 1, x, 1, x, 1);\n\t\t\t\t\tsquashPath.splice(0, 6);\n\t\t\t\t\tsquashPath.unshift(x, 0, x, 0, x, 1, x + gap / 2, 1, x + gap, 1, x + gap, 0, x + gap, 0, x + gap, -0.6, x + gap + 0.033, 0);\n\t\t\t\t\tfor (i = 0; i < squashPath.length; i+=2) {\n\t\t\t\t\t\tsquashPath[i] -= x;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (i = 0; i < path.length; i+=2) {\n\t\t\t\t\tpath[i] -= x;\n\t\t\t\t\tpath[i+1] = 1 - path[i+1];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (gap) {\n\t\t\t\t_normalizeX(squashPath);\n\t\t\t\tsquashPath[2] = \"C\" + squashPath[2];\n\t\t\t\tcreateCustomEase(vars.squashID || (id + \"-squash\"), \"M\" + squashPath.join(\",\"));\n\t\t\t}\n\t\t\t_normalizeX(path);\n\t\t\tpath[2] = \"C\" + path[2];\n\t\t\treturn createCustomEase(id, \"M\" + path.join(\",\"));\n\t\t}\n\t};\n\nexport class CustomBounce {\n\n\tconstructor(id, vars) {\n\t\tthis.ease = _create(id, vars);\n\t}\n\n\tstatic create(id, vars) {\n\t\treturn _create(id, vars);\n\t}\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n}\n\n_getGSAP() && gsap.registerPlugin(CustomBounce);\n\nCustomBounce.version = \"3.13.0\";\n\nexport { CustomBounce as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_initCore", "required", "createCustomEase", "parseEase", "_coreInitted", "config", "vars", "_create", "strength", "console", "warn", "_normalizeX", "a", "i", "l", "length", "s", "id", "cp1", "cp2", "x", "y", "nextX", "squishMagnitude", "decay", "Math", "min", "decayX", "gap", "squash", "originalGap", "slope", "w", "h", "prevX", "path", "squashPath", "push", "endAtStart", "unshift", "splice", "squashID", "join", "CustomBounce", "create", "register", "core", "ease", "version"], "mappings": ";;;;;;;;;6MAWY,SAAXA,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAC/F,SAAZG,EAAYC,GACXJ,EAAOD,KACPM,EAAmBL,GAAQA,EAAKM,UAAU,SAEzCC,EAAe,EACfP,EAAKM,UAAU,UAAUE,OAAS,SAAAC,SAAyB,iBAAVA,EAAqBC,EAAQ,GAAID,GAAQC,EAAQ,UAAYD,EAAO,IAAK,CAACE,UAAUF,MAErIL,GAAYQ,QAAQC,KAAK,wDAGb,SAAdC,EAAcC,OAIZC,EAHGC,EAAIF,EAAEG,OACTC,EAAI,EAAIJ,EAAEE,EAAI,OAGVD,EAAI,EAAGA,EAAIC,EAAGD,GAAK,EACvBD,EAAEC,MAAQD,EAAEC,GAAKG,EAHX,KAAA,IAKPJ,EAAEE,EAAI,GAAK,MApBTjB,EAAMO,EAAcF,EAuBvBK,EAAU,SAAVA,QAAWU,EAAIX,GACTF,GACJJ,EAAU,GAEXM,EAAOA,GAAQ,OAabY,EAAKC,EAAKC,EAAGC,EAAGR,EAAGS,EAAOC,EAV1BC,EAAQC,KAAKC,IADJ,KACapB,EAAKE,UAAY,IACvCmB,EAASH,EACTI,GAAOtB,EAAKuB,QAAU,GAAK,IAC3BC,EAAcF,EACdG,EAAQ,EAAI,IACZC,EAAI,GACJC,EAAI,EACJC,EAAQ,GACRC,EAAO,CAAC,EAAG,EAAG,IAAM,EAAG,GAAK,EAAG,GAAK,GACpCC,EAAa,CAAC,EAAG,EAAG,EAAG,EAAG,GAAK,EAAG,GAAK,OAEnCvB,EAAI,EAAGA,EAAI,MAGfS,EAAQY,GAFRF,GAAKL,IAAWA,EAAS,GAAK,IAI9BN,EAAI,GAHJY,GAAKT,EAAQA,GAKbL,GAHAC,EAAIc,EAAY,IAAJF,GAGU,IAAXZ,GADXF,EAAMgB,EAAQD,EAAIF,IAGdH,IACHM,GAASN,EACTV,GAAOU,EACPR,GAAKQ,EACLT,GAAOS,EACPN,GAASM,EACTL,EAAkBK,EAAME,EACxBM,EAAWC,KACVH,EAAQN,EAAK,EACbM,EAAQN,EAAKL,EACbW,EAAQN,EAAM,EAAGL,EACjBW,EAAOX,EACPW,EAAO,EACPA,EAAO,EACPA,GAA0B,GAAnBX,EACPW,GAASZ,EAAQY,GAAS,EAAG,EAC7BZ,EAAO,GAERa,EAAKE,KAAKH,EAAQN,EAAK,EACtBM,EAAO,EACPA,EAAO,GACRN,GAAOJ,EAAQA,GAGhBW,EAAKE,KAAKH,EAAO,EAChBhB,EAAKG,EACLD,EAAGC,EACHF,EAAKE,EACLC,EAAO,EACPA,EAAO,GAERE,GAAS,IACTO,EAAQE,GAAKX,EAAQH,GACrBe,EAAQZ,IAtDC,KAuDLD,IA3CgBR,QAgDjBP,EAAKgC,YAAkC,UAApBhC,EAAKgC,WAAwB,IACnDlB,GAAK,GACLe,EAAKI,QAAQnB,EAAG,EAAGA,EAAG,GAAI,IAAM,GAC5BU,MAEHV,GADAQ,EAAoB,IAAdE,EAENK,EAAKI,QAAQnB,EAAG,EAAGA,EAAG,EAAGA,EAAG,GAC5BgB,EAAWI,OAAO,EAAG,GACrBJ,EAAWG,QAAQnB,EAAG,EAAGA,EAAG,EAAGA,EAAG,EAAGA,EAAIQ,EAAM,EAAG,EAAGR,EAAIQ,EAAK,EAAGR,EAAIQ,EAAK,EAAGR,EAAIQ,EAAK,EAAGR,EAAIQ,GAAM,GAAKR,EAAIQ,EAAM,KAAO,GACpHf,EAAI,EAAGA,EAAIuB,EAAWrB,OAAQF,GAAG,EACrCuB,EAAWvB,IAAMO,MAGdP,EAAI,EAAGA,EAAIsB,EAAKpB,OAAQF,GAAG,EAC/BsB,EAAKtB,IAAMO,EACXe,EAAKtB,EAAE,GAAK,EAAIsB,EAAKtB,EAAE,UAGrBe,IACHjB,EAAYyB,GACZA,EAAW,GAAK,IAAMA,EAAW,GACjClC,EAAiBI,EAAKmC,UAAaxB,EAAK,UAAY,IAAMmB,EAAWM,KAAK,OAE3E/B,EAAYwB,GACZA,EAAK,GAAK,IAAMA,EAAK,GACdjC,EAAiBe,EAAI,IAAMkB,EAAKO,KAAK,OAIlCC,gBAMLC,OAAP,gBAAc3B,EAAIX,UACVC,EAAQU,EAAIX,iBAGbuC,SAAP,kBAAgBC,GACfjD,EAAOiD,EACP9C,yCAVWiB,EAAIX,QACVyC,KAAOxC,EAAQU,EAAIX,GAc1BV,KAAcC,EAAKE,eAAe4C,GAElCA,EAAaK,QAAU"}