{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nclass LlmService {\n  constructor() {\n    // 从配置文件加载LLM设置\n    // this.config = {\n    //   model: \"glm-4-flash\",\n    //   base_url: \"https://open.bigmodel.cn/api/paas/v4/\",\n    //   api_key: process.env.VUE_APP_LLM_API_KEY || \"d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW\"\n    // }\n    this.config = {\n      model: \"Qwen3-30B-A3B-Instruct-2507-FP8\",\n      base_url: \"http://************:8197/v1/\",\n      api_key: 1\n    };\n  }\n  async generateResponse(userInput) {\n    try {\n      // 构建对话上下文\n      const messages = [{\n        role: \"system\",\n        content: `\"\"\"\n          # 角色\n你是一个运行在车载终端上的智能体，名为车载壁纸提示词精灵。你具备强大的理解能力，能深入剖析用户描述的车载相关场景，如上班路上、送孩子上学等，精准洞察用户意图，并充分共情用户的情绪，从而生成用于生成车载壁纸的文生图提示词。\n\n## 技能\n### 技能 1: 理解意图与共情情绪\n1. 全面且细致地分析用户描述的场景内容，抽丝剥茧提炼出核心意图。\n2. 敏锐捕捉描述中透露的用户情绪状态，包括但不限于轻松、焦虑、愉悦、平静等。\n\n### 技能 2: 生成文生图提示词\n1. 依据理解的意图和共情到的情绪，融合多种温馨元素以及各类图像风格（不限于动漫风格，如写实、梦幻、简约等），生成契合要求的文生图提示词。\n2. 提示词要精准、详实，清晰描述画面主体、色彩氛围、元素构成等，以引导生成出适配车载终端作为壁纸的优质图像。\n\n## 限制\n- 严格围绕用户描述的车载相关场景展开意图理解、情绪共情和提示词生成工作，坚决不处理与车载终端场景无关的内容。\n- 生成的提示词必须符合设定要求，确保内容合理、有效。\n- 输出内容应简洁扼要，重点突出提示词本身。  \n- 仅输出提示词，不输出任何其他额外信息。\n\"\"\"`\n      }, {\n        role: \"user\",\n        content: userInput\n      }];\n      const response = await fetch(`${this.config.base_url}chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.config.api_key}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: this.config.model,\n          messages: messages,\n          temperature: 0.7,\n          max_tokens: 500\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`LLM API调用失败: ${response.status}`);\n      }\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('LLM调用失败:', error);\n      // 返回增强后的用户输入作为降级方案\n      return this.enhancePrompt(userInput);\n    }\n  }\n  enhancePrompt(userInput) {\n    // 通用壁纸提示词增强\n    const wallpaperEnhancements = [\"高清画质，精美细节\", \"专业摄影构图，艺术感强烈\", \"色彩丰富，层次分明\", \"光影效果出色，视觉冲击力强\"];\n    const randomEnhancement = wallpaperEnhancements[Math.floor(Math.random() * wallpaperEnhancements.length)];\n    return `${randomEnhancement}，${userInput}，(最佳画质, 4k:1.2)`;\n  }\n\n  // 获取对话历史\n  getConversationHistory() {\n    const history = localStorage.getItem('llm_conversation_history');\n    return history ? JSON.parse(history) : [];\n  }\n\n  // 保存对话历史\n  saveToConversationHistory(userInput, response) {\n    const history = this.getConversationHistory();\n    history.unshift({\n      userInput,\n      response,\n      timestamp: new Date().toISOString()\n    });\n\n    // 只保留最近20条记录\n    const trimmedHistory = history.slice(0, 20);\n    localStorage.setItem('llm_conversation_history', JSON.stringify(trimmedHistory));\n  }\n\n  // 清除对话历史\n  clearConversationHistory() {\n    localStorage.removeItem('llm_conversation_history');\n  }\n\n  // 批量处理多个输入\n  async processMultipleInputs(inputs) {\n    const results = [];\n    for (const input of inputs) {\n      try {\n        const response = await this.generateResponse(input);\n        results.push({\n          input,\n          response,\n          success: true\n        });\n      } catch (error) {\n        console.error(`处理输入失败: ${input}`, error);\n        results.push({\n          input,\n          response: this.enhancePrompt(input),\n          success: false,\n          error: error.message\n        });\n      }\n    }\n    return results;\n  }\n\n  // 获取模型信息\n  getModelInfo() {\n    return {\n      model: this.config.model,\n      base_url: this.config.base_url,\n      is_configured: !!this.config.api_key && this.config.api_key !== ''\n    };\n  }\n\n  // 测试API连接\n  async testConnection() {\n    try {\n      const testResponse = await this.generateResponse('测试');\n      return {\n        success: true,\n        message: 'LLM服务连接正常',\n        response: testResponse\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'LLM服务连接失败',\n        error: error.message\n      };\n    }\n  }\n\n  // 获取使用统计\n  getUsageStatistics() {\n    const history = this.getConversationHistory();\n    return {\n      totalRequests: history.length,\n      todayRequests: history.filter(item => {\n        const today = new Date().toDateString();\n        return new Date(item.timestamp).toDateString() === today;\n      }).length,\n      successRate: history.filter(item => item.success !== false).length / history.length * 100\n    };\n  }\n\n  // 场景识别方法 - 根据用户语音指令判断要切换的场景\n  async detectSceneFromVoice(userInput) {\n    const sceneDetectionPrompt = `\n你是一个智能车载场景识别助手。根据用户的语音指令，判断他们想要切换到哪个场景。\n\n可用的场景列表：\n1. morningCommuteFamily - 家庭出行模式（送孩子上学）\n2. morningCommuteFocus - 专注通勤模式（独自上班）\n3. eveningCommute - 下班通勤模式\n4. waitingMode - 等待休息模式（驻车娱乐）\n5. rainyNight - 雨夜模式\n6. familyTrip - 家庭出游模式\n7. longDistance - 长途驾驶模式\n8. guestMode - 访客模式\n9. petMode - 宠物模式\n10. carWashMode - 洗车模式\n11. romanticMode - 浪漫模式\n12. chargingMode - 充电模式\n13. fatigueDetection - 疲劳检测模式\n14. userSwitch - 用户切换模式\n15. parkingMode - 泊车模式\n16. emergencyMode - 紧急模式\n\n场景别名和关键词：\n- 家庭出行、送孩子、送毛毛、上学 -> morningCommuteFamily\n- 上班、通勤、去公司、独自 -> morningCommuteFocus\n- 下班、回家、晚上 -> eveningCommute\n- 等待、摸鱼、休息、停车 -> waitingMode\n- 雨夜、下雨、夜晚、雨天 -> rainyNight\n- 出游、周末、旅行、公园 -> familyTrip\n- 长途、高速、高速驾驶 -> longDistance\n- 访客、代驾、客人 -> guestMode\n- 宠物、动物、猫狗 -> petMode\n- 洗车、洗车模式 -> carWashMode\n- 浪漫、约会、二人世界 -> romanticMode\n- 充电、充电站、电量 -> chargingMode\n- 疲劳、困了、累了 -> fatigueDetection\n- 用户、切换用户、换人 -> userSwitch\n- 泊车、停车、找车位 -> parkingMode\n- 紧急、救援、事故 -> emergencyMode\n\n用户指令: \"${userInput}\"\n\n请返回JSON格式的响应：\n{\n  \"sceneId\": \"场景ID\",\n  \"confidence\": 0.9,\n  \"reason\": \"判断理由\"\n}\n\n如果用户指令不匹配任何场景，返回：\n{\n  \"sceneId\": null,\n  \"confidence\": 0,\n  \"reason\": \"无法识别场景\"\n}\n\n只返回JSON，不要返回其他文本。\n`;\n    try {\n      const messages = [{\n        role: \"system\",\n        content: \"你是一个智能车载场景识别助手，专门分析用户指令并返回对应的场景ID。\"\n      }, {\n        role: \"user\",\n        content: sceneDetectionPrompt\n      }];\n      const response = await fetch(`${this.config.base_url}chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.config.api_key}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: this.config.model,\n          messages: messages,\n          temperature: 0.3,\n          // 降低温度，提高准确性\n          max_tokens: 200\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`场景识别API调用失败: ${response.status}`);\n      }\n      const data = await response.json();\n      const result = JSON.parse(data.choices[0].message.content.trim());\n\n      // 验证返回结果格式\n      if (result && typeof result === 'object' && 'sceneId' in result) {\n        return result;\n      } else {\n        return {\n          sceneId: null,\n          confidence: 0,\n          reason: \"返回格式错误\"\n        };\n      }\n    } catch (error) {\n      console.error('场景识别失败:', error);\n      // 如果LLM调用失败，使用关键词匹配作为降级方案\n      return this.fallbackSceneDetection(userInput);\n    }\n  }\n\n  // 降级场景检测 - 基于关键词匹配\n  fallbackSceneDetection(userInput) {\n    const keywords = {\n      morningCommuteFamily: ['家庭', '孩子', '毛毛', '上学', '送孩子'],\n      morningCommuteFocus: ['上班', '通勤', '公司', '独自', '去公司'],\n      eveningCommute: ['下班', '回家', '晚上', '下班回家'],\n      waitingMode: ['等待', '摸鱼', '休息', '停车', '驻车'],\n      rainyNight: ['雨夜', '下雨', '夜晚', '雨天', '下雨天'],\n      familyTrip: ['出游', '周末', '旅行', '公园', '家庭出游'],\n      longDistance: ['长途', '高速', '高速驾驶', '长途驾驶'],\n      guestMode: ['访客', '代驾', '客人', '访客模式'],\n      petMode: ['宠物', '动物', '猫狗', '宠物模式'],\n      carWashMode: ['洗车', '洗车模式'],\n      romanticMode: ['浪漫', '约会', '二人世界', '浪漫模式'],\n      chargingMode: ['充电', '充电站', '电量', '充电模式'],\n      fatigueDetection: ['疲劳', '困了', '累了', '疲劳驾驶'],\n      userSwitch: ['用户', '切换用户', '换人', '用户切换'],\n      parkingMode: ['泊车', '停车', '找车位', '泊车模式'],\n      emergencyMode: ['紧急', '救援', '事故', '紧急情况']\n    };\n    const input = userInput.toLowerCase();\n    for (const [sceneId, words] of Object.entries(keywords)) {\n      if (words.some(word => input.includes(word))) {\n        return {\n          sceneId: sceneId,\n          confidence: 0.7,\n          reason: `关键词匹配: ${words.find(word => input.includes(word))}`\n        };\n      }\n    }\n    return {\n      sceneId: null,\n      confidence: 0,\n      reason: \"未匹配到关键词\"\n    };\n  }\n}\nexport default LlmService;", "map": {"version": 3, "names": ["LlmService", "constructor", "config", "model", "base_url", "api_key", "generateResponse", "userInput", "messages", "role", "content", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "error", "console", "enhancePrompt", "wallpaperEnhancements", "randomEnhancement", "Math", "floor", "random", "length", "getConversationHistory", "history", "localStorage", "getItem", "parse", "saveToConversationHistory", "unshift", "timestamp", "Date", "toISOString", "trimmedHistory", "slice", "setItem", "clearConversationHistory", "removeItem", "processMultipleInputs", "inputs", "results", "input", "push", "success", "getModelInfo", "is_configured", "testConnection", "testResponse", "getUsageStatistics", "totalRequests", "todayRequests", "filter", "item", "today", "toDateString", "successRate", "detectSceneFromVoice", "sceneDetectionPrompt", "result", "trim", "sceneId", "confidence", "reason", "fallbackSceneDetection", "keywords", "morningCommuteFamily", "morningCommuteFocus", "eveningCommute", "waitingMode", "rainyNight", "familyTrip", "longDistance", "<PERSON><PERSON><PERSON>", "petMode", "carWashMode", "<PERSON><PERSON><PERSON>", "chargingMode", "fatigueDetection", "userSwitch", "parkingMode", "emergencyMode", "toLowerCase", "words", "Object", "entries", "some", "word", "includes", "find"], "sources": ["F:/工作/theme/ai-hmi/src/services/LlmService.js"], "sourcesContent": ["class LlmService {\r\n  constructor() {\r\n    // 从配置文件加载LLM设置\r\n    // this.config = {\r\n    //   model: \"glm-4-flash\",\r\n    //   base_url: \"https://open.bigmodel.cn/api/paas/v4/\",\r\n    //   api_key: process.env.VUE_APP_LLM_API_KEY || \"d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW\"\r\n    // }\r\n    this.config = {\r\n      model: \"Qwen3-30B-A3B-Instruct-2507-FP8\",\r\n      base_url: \"http://************:8197/v1/\",\r\n      api_key: 1\r\n    }\r\n  }\r\n\r\n  async generateResponse(userInput) {\r\n    try {\r\n      // 构建对话上下文\r\n      const messages = [\r\n        {\r\n          role: \"system\",\r\n          content: `\"\"\"\r\n          # 角色\r\n你是一个运行在车载终端上的智能体，名为车载壁纸提示词精灵。你具备强大的理解能力，能深入剖析用户描述的车载相关场景，如上班路上、送孩子上学等，精准洞察用户意图，并充分共情用户的情绪，从而生成用于生成车载壁纸的文生图提示词。\r\n\r\n## 技能\r\n### 技能 1: 理解意图与共情情绪\r\n1. 全面且细致地分析用户描述的场景内容，抽丝剥茧提炼出核心意图。\r\n2. 敏锐捕捉描述中透露的用户情绪状态，包括但不限于轻松、焦虑、愉悦、平静等。\r\n\r\n### 技能 2: 生成文生图提示词\r\n1. 依据理解的意图和共情到的情绪，融合多种温馨元素以及各类图像风格（不限于动漫风格，如写实、梦幻、简约等），生成契合要求的文生图提示词。\r\n2. 提示词要精准、详实，清晰描述画面主体、色彩氛围、元素构成等，以引导生成出适配车载终端作为壁纸的优质图像。\r\n\r\n## 限制\r\n- 严格围绕用户描述的车载相关场景展开意图理解、情绪共情和提示词生成工作，坚决不处理与车载终端场景无关的内容。\r\n- 生成的提示词必须符合设定要求，确保内容合理、有效。\r\n- 输出内容应简洁扼要，重点突出提示词本身。  \r\n- 仅输出提示词，不输出任何其他额外信息。\r\n\"\"\"`\r\n        },\r\n        {\r\n          role: \"user\", \r\n          content: userInput\r\n        }\r\n      ]\r\n\r\n      const response = await fetch(`${this.config.base_url}chat/completions`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.config.api_key}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          model: this.config.model,\r\n          messages: messages,\r\n          temperature: 0.7,\r\n          max_tokens: 500\r\n        })\r\n      })\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`LLM API调用失败: ${response.status}`)\r\n      }\r\n\r\n      const data = await response.json()\r\n      return data.choices[0].message.content\r\n\r\n    } catch (error) {\r\n      console.error('LLM调用失败:', error)\r\n      // 返回增强后的用户输入作为降级方案\r\n      return this.enhancePrompt(userInput)\r\n    }\r\n  }\r\n\r\n  enhancePrompt(userInput) {\r\n    // 通用壁纸提示词增强\r\n    const wallpaperEnhancements = [\r\n      \"高清画质，精美细节\",\r\n      \"专业摄影构图，艺术感强烈\",\r\n      \"色彩丰富，层次分明\",\r\n      \"光影效果出色，视觉冲击力强\"\r\n    ]\r\n\r\n    const randomEnhancement = wallpaperEnhancements[\r\n      Math.floor(Math.random() * wallpaperEnhancements.length)\r\n    ]\r\n\r\n    return `${randomEnhancement}，${userInput}，(最佳画质, 4k:1.2)`\r\n  }\r\n\r\n  // 获取对话历史\r\n  getConversationHistory() {\r\n    const history = localStorage.getItem('llm_conversation_history')\r\n    return history ? JSON.parse(history) : []\r\n  }\r\n\r\n  // 保存对话历史\r\n  saveToConversationHistory(userInput, response) {\r\n    const history = this.getConversationHistory()\r\n    history.unshift({\r\n      userInput,\r\n      response,\r\n      timestamp: new Date().toISOString()\r\n    })\r\n\r\n    // 只保留最近20条记录\r\n    const trimmedHistory = history.slice(0, 20)\r\n    localStorage.setItem('llm_conversation_history', JSON.stringify(trimmedHistory))\r\n  }\r\n\r\n  // 清除对话历史\r\n  clearConversationHistory() {\r\n    localStorage.removeItem('llm_conversation_history')\r\n  }\r\n\r\n  // 批量处理多个输入\r\n  async processMultipleInputs(inputs) {\r\n    const results = []\r\n    \r\n    for (const input of inputs) {\r\n      try {\r\n        const response = await this.generateResponse(input)\r\n        results.push({ input, response, success: true })\r\n      } catch (error) {\r\n        console.error(`处理输入失败: ${input}`, error)\r\n        results.push({ \r\n          input, \r\n          response: this.enhancePrompt(input), \r\n          success: false,\r\n          error: error.message\r\n        })\r\n      }\r\n    }\r\n    \r\n    return results\r\n  }\r\n\r\n  // 获取模型信息\r\n  getModelInfo() {\r\n    return {\r\n      model: this.config.model,\r\n      base_url: this.config.base_url,\r\n      is_configured: !!this.config.api_key && this.config.api_key !== ''\r\n    }\r\n  }\r\n\r\n  // 测试API连接\r\n  async testConnection() {\r\n    try {\r\n      const testResponse = await this.generateResponse('测试')\r\n      return {\r\n        success: true,\r\n        message: 'LLM服务连接正常',\r\n        response: testResponse\r\n      }\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: 'LLM服务连接失败',\r\n        error: error.message\r\n      }\r\n    }\r\n  }\r\n\r\n  // 获取使用统计\r\n  getUsageStatistics() {\r\n    const history = this.getConversationHistory()\r\n    return {\r\n      totalRequests: history.length,\r\n      todayRequests: history.filter(item => {\r\n        const today = new Date().toDateString()\r\n        return new Date(item.timestamp).toDateString() === today\r\n      }).length,\r\n      successRate: history.filter(item => item.success !== false).length / history.length * 100\r\n    }\r\n  }\r\n\r\n  // 场景识别方法 - 根据用户语音指令判断要切换的场景\r\n  async detectSceneFromVoice(userInput) {\r\n    const sceneDetectionPrompt = `\r\n你是一个智能车载场景识别助手。根据用户的语音指令，判断他们想要切换到哪个场景。\r\n\r\n可用的场景列表：\r\n1. morningCommuteFamily - 家庭出行模式（送孩子上学）\r\n2. morningCommuteFocus - 专注通勤模式（独自上班）\r\n3. eveningCommute - 下班通勤模式\r\n4. waitingMode - 等待休息模式（驻车娱乐）\r\n5. rainyNight - 雨夜模式\r\n6. familyTrip - 家庭出游模式\r\n7. longDistance - 长途驾驶模式\r\n8. guestMode - 访客模式\r\n9. petMode - 宠物模式\r\n10. carWashMode - 洗车模式\r\n11. romanticMode - 浪漫模式\r\n12. chargingMode - 充电模式\r\n13. fatigueDetection - 疲劳检测模式\r\n14. userSwitch - 用户切换模式\r\n15. parkingMode - 泊车模式\r\n16. emergencyMode - 紧急模式\r\n\r\n场景别名和关键词：\r\n- 家庭出行、送孩子、送毛毛、上学 -> morningCommuteFamily\r\n- 上班、通勤、去公司、独自 -> morningCommuteFocus\r\n- 下班、回家、晚上 -> eveningCommute\r\n- 等待、摸鱼、休息、停车 -> waitingMode\r\n- 雨夜、下雨、夜晚、雨天 -> rainyNight\r\n- 出游、周末、旅行、公园 -> familyTrip\r\n- 长途、高速、高速驾驶 -> longDistance\r\n- 访客、代驾、客人 -> guestMode\r\n- 宠物、动物、猫狗 -> petMode\r\n- 洗车、洗车模式 -> carWashMode\r\n- 浪漫、约会、二人世界 -> romanticMode\r\n- 充电、充电站、电量 -> chargingMode\r\n- 疲劳、困了、累了 -> fatigueDetection\r\n- 用户、切换用户、换人 -> userSwitch\r\n- 泊车、停车、找车位 -> parkingMode\r\n- 紧急、救援、事故 -> emergencyMode\r\n\r\n用户指令: \"${userInput}\"\r\n\r\n请返回JSON格式的响应：\r\n{\r\n  \"sceneId\": \"场景ID\",\r\n  \"confidence\": 0.9,\r\n  \"reason\": \"判断理由\"\r\n}\r\n\r\n如果用户指令不匹配任何场景，返回：\r\n{\r\n  \"sceneId\": null,\r\n  \"confidence\": 0,\r\n  \"reason\": \"无法识别场景\"\r\n}\r\n\r\n只返回JSON，不要返回其他文本。\r\n`\r\n\r\n    try {\r\n      const messages = [\r\n        {\r\n          role: \"system\",\r\n          content: \"你是一个智能车载场景识别助手，专门分析用户指令并返回对应的场景ID。\"\r\n        },\r\n        {\r\n          role: \"user\",\r\n          content: sceneDetectionPrompt\r\n        }\r\n      ]\r\n\r\n      const response = await fetch(`${this.config.base_url}chat/completions`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.config.api_key}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          model: this.config.model,\r\n          messages: messages,\r\n          temperature: 0.3, // 降低温度，提高准确性\r\n          max_tokens: 200\r\n        })\r\n      })\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`场景识别API调用失败: ${response.status}`)\r\n      }\r\n\r\n      const data = await response.json()\r\n      const result = JSON.parse(data.choices[0].message.content.trim())\r\n      \r\n      // 验证返回结果格式\r\n      if (result && typeof result === 'object' && 'sceneId' in result) {\r\n        return result\r\n      } else {\r\n        return { sceneId: null, confidence: 0, reason: \"返回格式错误\" }\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('场景识别失败:', error)\r\n      // 如果LLM调用失败，使用关键词匹配作为降级方案\r\n      return this.fallbackSceneDetection(userInput)\r\n    }\r\n  }\r\n\r\n  // 降级场景检测 - 基于关键词匹配\r\n  fallbackSceneDetection(userInput) {\r\n    const keywords = {\r\n      morningCommuteFamily: ['家庭', '孩子', '毛毛', '上学', '送孩子'],\r\n      morningCommuteFocus: ['上班', '通勤', '公司', '独自', '去公司'],\r\n      eveningCommute: ['下班', '回家', '晚上', '下班回家'],\r\n      waitingMode: ['等待', '摸鱼', '休息', '停车', '驻车'],\r\n      rainyNight: ['雨夜', '下雨', '夜晚', '雨天', '下雨天'],\r\n      familyTrip: ['出游', '周末', '旅行', '公园', '家庭出游'],\r\n      longDistance: ['长途', '高速', '高速驾驶', '长途驾驶'],\r\n      guestMode: ['访客', '代驾', '客人', '访客模式'],\r\n      petMode: ['宠物', '动物', '猫狗', '宠物模式'],\r\n      carWashMode: ['洗车', '洗车模式'],\r\n      romanticMode: ['浪漫', '约会', '二人世界', '浪漫模式'],\r\n      chargingMode: ['充电', '充电站', '电量', '充电模式'],\r\n      fatigueDetection: ['疲劳', '困了', '累了', '疲劳驾驶'],\r\n      userSwitch: ['用户', '切换用户', '换人', '用户切换'],\r\n      parkingMode: ['泊车', '停车', '找车位', '泊车模式'],\r\n      emergencyMode: ['紧急', '救援', '事故', '紧急情况']\r\n    }\r\n\r\n    const input = userInput.toLowerCase()\r\n    \r\n    for (const [sceneId, words] of Object.entries(keywords)) {\r\n      if (words.some(word => input.includes(word))) {\r\n        return {\r\n          sceneId: sceneId,\r\n          confidence: 0.7,\r\n          reason: `关键词匹配: ${words.find(word => input.includes(word))}`\r\n        }\r\n      }\r\n    }\r\n\r\n    return { sceneId: null, confidence: 0, reason: \"未匹配到关键词\" }\r\n  }\r\n}\r\n\r\nexport default LlmService"], "mappings": ";;;;;AAAA,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG;MACZC,KAAK,EAAE,iCAAiC;MACxCC,QAAQ,EAAE,8BAA8B;MACxCC,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMC,gBAAgBA,CAACC,SAAS,EAAE;IAChC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,CACf;QACEC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,CAAC,EACD;QACED,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEH;MACX,CAAC,CACF;MAED,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACV,MAAM,CAACE,QAAQ,kBAAkB,EAAE;QACtES,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACZ,MAAM,CAACG,OAAO,EAAE;UAChD,cAAc,EAAE;QAClB,CAAC;QACDU,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBd,KAAK,EAAE,IAAI,CAACD,MAAM,CAACC,KAAK;UACxBK,QAAQ,EAAEA,QAAQ;UAClBU,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACR,QAAQ,CAACS,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBV,QAAQ,CAACW,MAAM,EAAE,CAAC;MACpD;MAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAChB,OAAO;IAExC,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC;MACA,OAAO,IAAI,CAACE,aAAa,CAACtB,SAAS,CAAC;IACtC;EACF;EAEAsB,aAAaA,CAACtB,SAAS,EAAE;IACvB;IACA,MAAMuB,qBAAqB,GAAG,CAC5B,WAAW,EACX,cAAc,EACd,WAAW,EACX,eAAe,CAChB;IAED,MAAMC,iBAAiB,GAAGD,qBAAqB,CAC7CE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,qBAAqB,CAACK,MAAM,CAAC,CACzD;IAED,OAAO,GAAGJ,iBAAiB,IAAIxB,SAAS,iBAAiB;EAC3D;;EAEA;EACA6B,sBAAsBA,CAAA,EAAG;IACvB,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;IAChE,OAAOF,OAAO,GAAGrB,IAAI,CAACwB,KAAK,CAACH,OAAO,CAAC,GAAG,EAAE;EAC3C;;EAEA;EACAI,yBAAyBA,CAAClC,SAAS,EAAEI,QAAQ,EAAE;IAC7C,MAAM0B,OAAO,GAAG,IAAI,CAACD,sBAAsB,CAAC,CAAC;IAC7CC,OAAO,CAACK,OAAO,CAAC;MACdnC,SAAS;MACTI,QAAQ;MACRgC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;;IAEF;IACA,MAAMC,cAAc,GAAGT,OAAO,CAACU,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3CT,YAAY,CAACU,OAAO,CAAC,0BAA0B,EAAEhC,IAAI,CAACC,SAAS,CAAC6B,cAAc,CAAC,CAAC;EAClF;;EAEA;EACAG,wBAAwBA,CAAA,EAAG;IACzBX,YAAY,CAACY,UAAU,CAAC,0BAA0B,CAAC;EACrD;;EAEA;EACA,MAAMC,qBAAqBA,CAACC,MAAM,EAAE;IAClC,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MAC1B,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAACgD,KAAK,CAAC;QACnDD,OAAO,CAACE,IAAI,CAAC;UAAED,KAAK;UAAE3C,QAAQ;UAAE6C,OAAO,EAAE;QAAK,CAAC,CAAC;MAClD,CAAC,CAAC,OAAO7B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW2B,KAAK,EAAE,EAAE3B,KAAK,CAAC;QACxC0B,OAAO,CAACE,IAAI,CAAC;UACXD,KAAK;UACL3C,QAAQ,EAAE,IAAI,CAACkB,aAAa,CAACyB,KAAK,CAAC;UACnCE,OAAO,EAAE,KAAK;UACd7B,KAAK,EAAEA,KAAK,CAACD;QACf,CAAC,CAAC;MACJ;IACF;IAEA,OAAO2B,OAAO;EAChB;;EAEA;EACAI,YAAYA,CAAA,EAAG;IACb,OAAO;MACLtD,KAAK,EAAE,IAAI,CAACD,MAAM,CAACC,KAAK;MACxBC,QAAQ,EAAE,IAAI,CAACF,MAAM,CAACE,QAAQ;MAC9BsD,aAAa,EAAE,CAAC,CAAC,IAAI,CAACxD,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACG,OAAO,KAAK;IAClE,CAAC;EACH;;EAEA;EACA,MAAMsD,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMC,YAAY,GAAG,MAAM,IAAI,CAACtD,gBAAgB,CAAC,IAAI,CAAC;MACtD,OAAO;QACLkD,OAAO,EAAE,IAAI;QACb9B,OAAO,EAAE,WAAW;QACpBf,QAAQ,EAAEiD;MACZ,CAAC;IACH,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,OAAO;QACL6B,OAAO,EAAE,KAAK;QACd9B,OAAO,EAAE,WAAW;QACpBC,KAAK,EAAEA,KAAK,CAACD;MACf,CAAC;IACH;EACF;;EAEA;EACAmC,kBAAkBA,CAAA,EAAG;IACnB,MAAMxB,OAAO,GAAG,IAAI,CAACD,sBAAsB,CAAC,CAAC;IAC7C,OAAO;MACL0B,aAAa,EAAEzB,OAAO,CAACF,MAAM;MAC7B4B,aAAa,EAAE1B,OAAO,CAAC2B,MAAM,CAACC,IAAI,IAAI;QACpC,MAAMC,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC,CAACuB,YAAY,CAAC,CAAC;QACvC,OAAO,IAAIvB,IAAI,CAACqB,IAAI,CAACtB,SAAS,CAAC,CAACwB,YAAY,CAAC,CAAC,KAAKD,KAAK;MAC1D,CAAC,CAAC,CAAC/B,MAAM;MACTiC,WAAW,EAAE/B,OAAO,CAAC2B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACT,OAAO,KAAK,KAAK,CAAC,CAACrB,MAAM,GAAGE,OAAO,CAACF,MAAM,GAAG;IACxF,CAAC;EACH;;EAEA;EACA,MAAMkC,oBAAoBA,CAAC9D,SAAS,EAAE;IACpC,MAAM+D,oBAAoB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/D,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;IAEG,IAAI;MACF,MAAMC,QAAQ,GAAG,CACf;QACEC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE;MACX,CAAC,EACD;QACED,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE4D;MACX,CAAC,CACF;MAED,MAAM3D,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACV,MAAM,CAACE,QAAQ,kBAAkB,EAAE;QACtES,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACZ,MAAM,CAACG,OAAO,EAAE;UAChD,cAAc,EAAE;QAClB,CAAC;QACDU,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBd,KAAK,EAAE,IAAI,CAACD,MAAM,CAACC,KAAK;UACxBK,QAAQ,EAAEA,QAAQ;UAClBU,WAAW,EAAE,GAAG;UAAE;UAClBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACR,QAAQ,CAACS,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBV,QAAQ,CAACW,MAAM,EAAE,CAAC;MACpD;MAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;MAClC,MAAM+C,MAAM,GAAGvD,IAAI,CAACwB,KAAK,CAACjB,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAChB,OAAO,CAAC8D,IAAI,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAID,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,SAAS,IAAIA,MAAM,EAAE;QAC/D,OAAOA,MAAM;MACf,CAAC,MAAM;QACL,OAAO;UAAEE,OAAO,EAAE,IAAI;UAAEC,UAAU,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAS,CAAC;MAC3D;IAEF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B;MACA,OAAO,IAAI,CAACiD,sBAAsB,CAACrE,SAAS,CAAC;IAC/C;EACF;;EAEA;EACAqE,sBAAsBA,CAACrE,SAAS,EAAE;IAChC,MAAMsE,QAAQ,GAAG;MACfC,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;MACrDC,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;MACpDC,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MAC1CC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3CC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;MAC3CC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MAC5CC,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MAC1CC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MACrCC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MACnCC,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;MAC3BC,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MAC1CC,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;MACzCC,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MAC5CC,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;MACxCC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;MACxCC,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IAC1C,CAAC;IAED,MAAMvC,KAAK,GAAG/C,SAAS,CAACuF,WAAW,CAAC,CAAC;IAErC,KAAK,MAAM,CAACrB,OAAO,EAAEsB,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACpB,QAAQ,CAAC,EAAE;MACvD,IAAIkB,KAAK,CAACG,IAAI,CAACC,IAAI,IAAI7C,KAAK,CAAC8C,QAAQ,CAACD,IAAI,CAAC,CAAC,EAAE;QAC5C,OAAO;UACL1B,OAAO,EAAEA,OAAO;UAChBC,UAAU,EAAE,GAAG;UACfC,MAAM,EAAE,UAAUoB,KAAK,CAACM,IAAI,CAACF,IAAI,IAAI7C,KAAK,CAAC8C,QAAQ,CAACD,IAAI,CAAC,CAAC;QAC5D,CAAC;MACH;IACF;IAEA,OAAO;MAAE1B,OAAO,EAAE,IAAI;MAAEC,UAAU,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAU,CAAC;EAC5D;AACF;AAEA,eAAe3E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}