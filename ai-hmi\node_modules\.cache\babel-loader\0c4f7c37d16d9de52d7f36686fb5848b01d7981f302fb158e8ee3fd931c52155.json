{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\n/**\r\n * 动态壁纸服务\r\n * 提供智能的动态壁纸生成功能，支持文件上传和URL两种模式\r\n */\nclass DynamicWallpaperService {\n  constructor() {\n    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000';\n    this.currentPromptId = null;\n    this.isGenerating = false;\n  }\n\n  /**\r\n   * 生成动态壁纸（智能选择接口）\r\n   * @param {Object} options - 生成选项\r\n   * @param {File} options.imageFile - 图片文件（优先）\r\n   * @param {string} options.imageUrl - Kolors生成的图片URL（备用）\r\n   * @param {string} options.taskId - 任务ID\r\n   * @param {Function} options.onProgress - 进度回调\r\n   * @returns {Promise<Object>} 生成结果\r\n   */\n  async generateDynamicWallpaper(options) {\n    const {\n      imageFile,\n      imageUrl,\n      taskId,\n      onProgress\n    } = options;\n\n    // 优先使用文件上传接口\n    if (imageFile) {\n      console.log('使用文件上传模式生成动态壁纸');\n      return await this.generateFromFile(imageFile, taskId, onProgress);\n    }\n\n    // 备用URL接口\n    if (imageUrl) {\n      console.log('使用URL模式生成动态壁纸');\n      return await this.generateFromUrl(imageUrl, taskId, onProgress);\n    }\n    throw new Error('必须提供imageFile或imageUrl参数');\n  }\n\n  /**\r\n   * 通过文件上传生成动态壁纸（推荐方式）\r\n   */\n  async generateFromFile(imageFile, taskId, onProgress) {\n    try {\n      this.isGenerating = true;\n      this.currentPromptId = null;\n\n      // 进度回调\n      if (onProgress) {\n        onProgress({\n          percentage: 10,\n          message: '正在上传图片文件...',\n          status: 'uploading'\n        });\n      }\n\n      // 创建表单数据\n      const formData = new FormData();\n      formData.append('file', imageFile);\n      formData.append('task_id', taskId);\n\n      // 调用文件上传接口\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/dynamic-wallpaper`, {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`HTTP ${response.status}: ${errorText}`);\n      }\n      const result = await response.json();\n      this.currentPromptId = result.prompt_id;\n\n      // 进度回调\n      if (onProgress) {\n        onProgress({\n          percentage: 30,\n          message: '文件上传成功，正在生成动态效果...',\n          status: 'processing',\n          promptId: result.prompt_id\n        });\n      }\n\n      // 模拟进度更新（实际项目中可以通过WebSocket获取真实进度）\n      await this.simulateProgress(onProgress, 30, 100);\n      return {\n        promptId: result.prompt_id,\n        url: result.video_url,\n        taskId: result.task_id,\n        type: 'video'\n      };\n    } catch (error) {\n      console.error('文件上传模式生成失败:', error);\n      throw new Error(`文件上传生成失败: ${error.message}`);\n    } finally {\n      this.isGenerating = false;\n    }\n  }\n\n  /**\r\n   * 通过URL生成动态壁纸（备用方式）\r\n   */\n  async generateFromUrl(imageUrl, taskId, onProgress) {\n    try {\n      this.isGenerating = true;\n      this.currentPromptId = null;\n\n      // 进度回调\n      if (onProgress) {\n        onProgress({\n          percentage: 10,\n          message: '正在下载图片...',\n          status: 'downloading'\n        });\n      }\n\n      // 调用URL接口\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/generate-from-url`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          image_url: imageUrl,\n          task_id: taskId\n        })\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`HTTP ${response.status}: ${errorText}`);\n      }\n      const result = await response.json();\n      this.currentPromptId = result.prompt_id;\n\n      // 进度回调\n      if (onProgress) {\n        onProgress({\n          percentage: 30,\n          message: '图片下载成功，正在生成动态效果...',\n          status: 'processing',\n          promptId: result.prompt_id\n        });\n      }\n\n      // 模拟进度更新\n      await this.simulateProgress(onProgress, 30, 100);\n      return {\n        promptId: result.prompt_id,\n        url: result.video_url,\n        taskId: result.task_id,\n        sourceImageUrl: result.source_image_url,\n        type: 'video'\n      };\n    } catch (error) {\n      console.error('URL模式生成失败:', error);\n      throw new Error(`URL生成失败: ${error.message}`);\n    } finally {\n      this.isGenerating = false;\n    }\n  }\n\n  /**\r\n   * 模拟进度更新（实际项目中应该通过WebSocket获取真实进度）\r\n   */\n  async simulateProgress(onProgress, startPercentage, endPercentage) {\n    if (!onProgress) return;\n    const steps = [{\n      percentage: 40,\n      message: '正在处理图片...'\n    }, {\n      percentage: 60,\n      message: '正在生成动态效果...'\n    }, {\n      percentage: 80,\n      message: '正在渲染视频...'\n    }, {\n      percentage: 95,\n      message: '正在优化输出...'\n    }];\n    for (const step of steps) {\n      if (step.percentage <= endPercentage) {\n        await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟延迟\n        onProgress({\n          percentage: step.percentage,\n          message: step.message,\n          status: 'processing',\n          promptId: this.currentPromptId\n        });\n      }\n    }\n  }\n\n  /**\r\n   * 检查生成状态\r\n   */\n  async checkGenerationStatus(promptId) {\n    try {\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/status/${promptId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('检查生成状态失败:', error);\n      return {\n        status: 'error',\n        error: error.message\n      };\n    }\n  }\n\n  /**\r\n   * 取消生成任务\r\n   */\n  async cancelGeneration(promptId) {\n    try {\n      if (!promptId) {\n        promptId = this.currentPromptId;\n      }\n      if (!promptId) {\n        console.warn('没有可取消的任务');\n        return;\n      }\n\n      // 注意：ComfyUI可能不支持取消任务，这里只是重置本地状态\n      this.isGenerating = false;\n      this.currentPromptId = null;\n      console.log(`已取消生成任务: ${promptId}`);\n\n      // 如果后端支持取消接口，可以在这里调用\n      // const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/cancel/${promptId}`, {\n      //   method: 'POST'\n      // })\n    } catch (error) {\n      console.error('取消生成任务失败:', error);\n    }\n  }\n\n  /**\r\n   * 获取当前生成状态\r\n   */\n  getGenerationStatus() {\n    return {\n      isGenerating: this.isGenerating,\n      currentPromptId: this.currentPromptId\n    };\n  }\n\n  /**\r\n   * 生成任务ID\r\n   */\n  generateTaskId() {\n    return `dynamic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  /**\r\n   * 验证图片URL\r\n   */\n  validateImageUrl(imageUrl) {\n    if (!imageUrl || typeof imageUrl !== 'string') {\n      return false;\n    }\n    try {\n      const url = new URL(imageUrl);\n      return url.protocol === 'http:' || url.protocol === 'https:';\n    } catch {\n      return false;\n    }\n  }\n\n  /**\r\n   * 验证图片文件\r\n   */\n  validateImageFile(imageFile) {\n    if (!imageFile || !(imageFile instanceof File)) {\n      return {\n        valid: false,\n        error: '无效的文件对象'\n      };\n    }\n\n    // 检查文件类型\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(imageFile.type)) {\n      return {\n        valid: false,\n        error: '不支持的文件格式，请使用 JPG、PNG 或 WebP 格式'\n      };\n    }\n\n    // 检查文件大小（10MB限制）\n    const maxSize = 10 * 1024 * 1024;\n    if (imageFile.size > maxSize) {\n      return {\n        valid: false,\n        error: '文件过大，请选择小于10MB的图片'\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n}\nexport default DynamicWallpaperService;", "map": {"version": 3, "names": ["DynamicWallpaperService", "constructor", "apiBaseUrl", "process", "env", "VUE_APP_API_BASE_URL", "currentPromptId", "isGenerating", "generateDynamicWallpaper", "options", "imageFile", "imageUrl", "taskId", "onProgress", "console", "log", "generateFromFile", "generateFromUrl", "Error", "percentage", "message", "status", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorText", "text", "result", "json", "prompt_id", "promptId", "simulateProgress", "url", "video_url", "task_id", "type", "error", "headers", "JSON", "stringify", "image_url", "sourceImageUrl", "source_image_url", "startPercentage", "endPercentage", "steps", "step", "Promise", "resolve", "setTimeout", "checkGenerationStatus", "cancelGeneration", "warn", "getGenerationStatus", "generateTaskId", "Date", "now", "Math", "random", "toString", "substr", "validateImageUrl", "URL", "protocol", "validateImageFile", "File", "valid", "allowedTypes", "includes", "maxSize", "size"], "sources": ["F:/工作/theme/ai-hmi/src/services/DynamicWallpaperService.js"], "sourcesContent": ["/**\r\n * 动态壁纸服务\r\n * 提供智能的动态壁纸生成功能，支持文件上传和URL两种模式\r\n */\r\nclass DynamicWallpaperService {\r\n  constructor() {\r\n    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'\r\n    this.currentPromptId = null\r\n    this.isGenerating = false\r\n  }\r\n\r\n  /**\r\n   * 生成动态壁纸（智能选择接口）\r\n   * @param {Object} options - 生成选项\r\n   * @param {File} options.imageFile - 图片文件（优先）\r\n   * @param {string} options.imageUrl - Kolors生成的图片URL（备用）\r\n   * @param {string} options.taskId - 任务ID\r\n   * @param {Function} options.onProgress - 进度回调\r\n   * @returns {Promise<Object>} 生成结果\r\n   */\r\n  async generateDynamicWallpaper(options) {\r\n    const { imageFile, imageUrl, taskId, onProgress } = options\r\n    \r\n    // 优先使用文件上传接口\r\n    if (imageFile) {\r\n      console.log('使用文件上传模式生成动态壁纸')\r\n      return await this.generateFromFile(imageFile, taskId, onProgress)\r\n    }\r\n    \r\n    // 备用URL接口\r\n    if (imageUrl) {\r\n      console.log('使用URL模式生成动态壁纸')\r\n      return await this.generateFromUrl(imageUrl, taskId, onProgress)\r\n    }\r\n    \r\n    throw new Error('必须提供imageFile或imageUrl参数')\r\n  }\r\n\r\n  /**\r\n   * 通过文件上传生成动态壁纸（推荐方式）\r\n   */\r\n  async generateFromFile(imageFile, taskId, onProgress) {\r\n    try {\r\n      this.isGenerating = true\r\n      this.currentPromptId = null\r\n      \r\n      // 进度回调\r\n      if (onProgress) {\r\n        onProgress({\r\n          percentage: 10,\r\n          message: '正在上传图片文件...',\r\n          status: 'uploading'\r\n        })\r\n      }\r\n      \r\n      // 创建表单数据\r\n      const formData = new FormData()\r\n      formData.append('file', imageFile)\r\n      formData.append('task_id', taskId)\r\n      \r\n      // 调用文件上传接口\r\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/dynamic-wallpaper`, {\r\n        method: 'POST',\r\n        body: formData\r\n      })\r\n      \r\n      if (!response.ok) {\r\n        const errorText = await response.text()\r\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\r\n      }\r\n      \r\n      const result = await response.json()\r\n      this.currentPromptId = result.prompt_id\r\n      \r\n      // 进度回调\r\n      if (onProgress) {\r\n        onProgress({\r\n          percentage: 30,\r\n          message: '文件上传成功，正在生成动态效果...',\r\n          status: 'processing',\r\n          promptId: result.prompt_id\r\n        })\r\n      }\r\n      \r\n      // 模拟进度更新（实际项目中可以通过WebSocket获取真实进度）\r\n      await this.simulateProgress(onProgress, 30, 100)\r\n      \r\n      return {\r\n        promptId: result.prompt_id,\r\n        url: result.video_url,\r\n        taskId: result.task_id,\r\n        type: 'video'\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('文件上传模式生成失败:', error)\r\n      throw new Error(`文件上传生成失败: ${error.message}`)\r\n    } finally {\r\n      this.isGenerating = false\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 通过URL生成动态壁纸（备用方式）\r\n   */\r\n  async generateFromUrl(imageUrl, taskId, onProgress) {\r\n    try {\r\n      this.isGenerating = true\r\n      this.currentPromptId = null\r\n      \r\n      // 进度回调\r\n      if (onProgress) {\r\n        onProgress({\r\n          percentage: 10,\r\n          message: '正在下载图片...',\r\n          status: 'downloading'\r\n        })\r\n      }\r\n      \r\n      // 调用URL接口\r\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/generate-from-url`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          image_url: imageUrl,\r\n          task_id: taskId\r\n        })\r\n      })\r\n      \r\n      if (!response.ok) {\r\n        const errorText = await response.text()\r\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\r\n      }\r\n      \r\n      const result = await response.json()\r\n      this.currentPromptId = result.prompt_id\r\n      \r\n      // 进度回调\r\n      if (onProgress) {\r\n        onProgress({\r\n          percentage: 30,\r\n          message: '图片下载成功，正在生成动态效果...',\r\n          status: 'processing',\r\n          promptId: result.prompt_id\r\n        })\r\n      }\r\n      \r\n      // 模拟进度更新\r\n      await this.simulateProgress(onProgress, 30, 100)\r\n      \r\n      return {\r\n        promptId: result.prompt_id,\r\n        url: result.video_url,\r\n        taskId: result.task_id,\r\n        sourceImageUrl: result.source_image_url,\r\n        type: 'video'\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('URL模式生成失败:', error)\r\n      throw new Error(`URL生成失败: ${error.message}`)\r\n    } finally {\r\n      this.isGenerating = false\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 模拟进度更新（实际项目中应该通过WebSocket获取真实进度）\r\n   */\r\n  async simulateProgress(onProgress, startPercentage, endPercentage) {\r\n    if (!onProgress) return\r\n    \r\n    const steps = [\r\n      { percentage: 40, message: '正在处理图片...' },\r\n      { percentage: 60, message: '正在生成动态效果...' },\r\n      { percentage: 80, message: '正在渲染视频...' },\r\n      { percentage: 95, message: '正在优化输出...' }\r\n    ]\r\n    \r\n    for (const step of steps) {\r\n      if (step.percentage <= endPercentage) {\r\n        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟延迟\r\n        onProgress({\r\n          percentage: step.percentage,\r\n          message: step.message,\r\n          status: 'processing',\r\n          promptId: this.currentPromptId\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 检查生成状态\r\n   */\r\n  async checkGenerationStatus(promptId) {\r\n    try {\r\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/status/${promptId}`)\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP ${response.status}`)\r\n      }\r\n      return await response.json()\r\n    } catch (error) {\r\n      console.error('检查生成状态失败:', error)\r\n      return { status: 'error', error: error.message }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 取消生成任务\r\n   */\r\n  async cancelGeneration(promptId) {\r\n    try {\r\n      if (!promptId) {\r\n        promptId = this.currentPromptId\r\n      }\r\n      \r\n      if (!promptId) {\r\n        console.warn('没有可取消的任务')\r\n        return\r\n      }\r\n      \r\n      // 注意：ComfyUI可能不支持取消任务，这里只是重置本地状态\r\n      this.isGenerating = false\r\n      this.currentPromptId = null\r\n      \r\n      console.log(`已取消生成任务: ${promptId}`)\r\n      \r\n      // 如果后端支持取消接口，可以在这里调用\r\n      // const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/cancel/${promptId}`, {\r\n      //   method: 'POST'\r\n      // })\r\n      \r\n    } catch (error) {\r\n      console.error('取消生成任务失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取当前生成状态\r\n   */\r\n  getGenerationStatus() {\r\n    return {\r\n      isGenerating: this.isGenerating,\r\n      currentPromptId: this.currentPromptId\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成任务ID\r\n   */\r\n  generateTaskId() {\r\n    return `dynamic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\r\n  }\r\n\r\n  /**\r\n   * 验证图片URL\r\n   */\r\n  validateImageUrl(imageUrl) {\r\n    if (!imageUrl || typeof imageUrl !== 'string') {\r\n      return false\r\n    }\r\n    \r\n    try {\r\n      const url = new URL(imageUrl)\r\n      return url.protocol === 'http:' || url.protocol === 'https:'\r\n    } catch {\r\n      return false\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 验证图片文件\r\n   */\r\n  validateImageFile(imageFile) {\r\n    if (!imageFile || !(imageFile instanceof File)) {\r\n      return { valid: false, error: '无效的文件对象' }\r\n    }\r\n    \r\n    // 检查文件类型\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\r\n    if (!allowedTypes.includes(imageFile.type)) {\r\n      return { valid: false, error: '不支持的文件格式，请使用 JPG、PNG 或 WebP 格式' }\r\n    }\r\n    \r\n    // 检查文件大小（10MB限制）\r\n    const maxSize = 10 * 1024 * 1024\r\n    if (imageFile.size > maxSize) {\r\n      return { valid: false, error: '文件过大，请选择小于10MB的图片' }\r\n    }\r\n    \r\n    return { valid: true }\r\n  }\r\n}\r\n\r\nexport default DynamicWallpaperService\r\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA,MAAMA,uBAAuB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB;IAC7E,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,YAAY,GAAG,KAAK;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,wBAAwBA,CAACC,OAAO,EAAE;IACtC,MAAM;MAAEC,SAAS;MAAEC,QAAQ;MAAEC,MAAM;MAAEC;IAAW,CAAC,GAAGJ,OAAO;;IAE3D;IACA,IAAIC,SAAS,EAAE;MACbI,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,OAAO,MAAM,IAAI,CAACC,gBAAgB,CAACN,SAAS,EAAEE,MAAM,EAAEC,UAAU,CAAC;IACnE;;IAEA;IACA,IAAIF,QAAQ,EAAE;MACZG,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,OAAO,MAAM,IAAI,CAACE,eAAe,CAACN,QAAQ,EAAEC,MAAM,EAAEC,UAAU,CAAC;IACjE;IAEA,MAAM,IAAIK,KAAK,CAAC,0BAA0B,CAAC;EAC7C;;EAEA;AACF;AACA;EACE,MAAMF,gBAAgBA,CAACN,SAAS,EAAEE,MAAM,EAAEC,UAAU,EAAE;IACpD,IAAI;MACF,IAAI,CAACN,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,eAAe,GAAG,IAAI;;MAE3B;MACA,IAAIO,UAAU,EAAE;QACdA,UAAU,CAAC;UACTM,UAAU,EAAE,EAAE;UACdC,OAAO,EAAE,aAAa;UACtBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEd,SAAS,CAAC;MAClCY,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEZ,MAAM,CAAC;;MAElC;MACA,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACxB,UAAU,6CAA6C,EAAE;QAC5FyB,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIb,KAAK,CAAC,QAAQO,QAAQ,CAACJ,MAAM,KAAKS,SAAS,EAAE,CAAC;MAC1D;MAEA,MAAME,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,IAAI,CAAC3B,eAAe,GAAG0B,MAAM,CAACE,SAAS;;MAEvC;MACA,IAAIrB,UAAU,EAAE;QACdA,UAAU,CAAC;UACTM,UAAU,EAAE,EAAE;UACdC,OAAO,EAAE,oBAAoB;UAC7BC,MAAM,EAAE,YAAY;UACpBc,QAAQ,EAAEH,MAAM,CAACE;QACnB,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM,IAAI,CAACE,gBAAgB,CAACvB,UAAU,EAAE,EAAE,EAAE,GAAG,CAAC;MAEhD,OAAO;QACLsB,QAAQ,EAAEH,MAAM,CAACE,SAAS;QAC1BG,GAAG,EAAEL,MAAM,CAACM,SAAS;QACrB1B,MAAM,EAAEoB,MAAM,CAACO,OAAO;QACtBC,IAAI,EAAE;MACR,CAAC;IAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAM,IAAIvB,KAAK,CAAC,aAAauB,KAAK,CAACrB,OAAO,EAAE,CAAC;IAC/C,CAAC,SAAS;MACR,IAAI,CAACb,YAAY,GAAG,KAAK;IAC3B;EACF;;EAEA;AACF;AACA;EACE,MAAMU,eAAeA,CAACN,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAClD,IAAI;MACF,IAAI,CAACN,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,eAAe,GAAG,IAAI;;MAE3B;MACA,IAAIO,UAAU,EAAE;QACdA,UAAU,CAAC;UACTM,UAAU,EAAE,EAAE;UACdC,OAAO,EAAE,WAAW;UACpBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACxB,UAAU,6CAA6C,EAAE;QAC5FyB,MAAM,EAAE,MAAM;QACde,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDd,IAAI,EAAEe,IAAI,CAACC,SAAS,CAAC;UACnBC,SAAS,EAAElC,QAAQ;UACnB4B,OAAO,EAAE3B;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACa,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIb,KAAK,CAAC,QAAQO,QAAQ,CAACJ,MAAM,KAAKS,SAAS,EAAE,CAAC;MAC1D;MAEA,MAAME,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,IAAI,CAAC3B,eAAe,GAAG0B,MAAM,CAACE,SAAS;;MAEvC;MACA,IAAIrB,UAAU,EAAE;QACdA,UAAU,CAAC;UACTM,UAAU,EAAE,EAAE;UACdC,OAAO,EAAE,oBAAoB;UAC7BC,MAAM,EAAE,YAAY;UACpBc,QAAQ,EAAEH,MAAM,CAACE;QACnB,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM,IAAI,CAACE,gBAAgB,CAACvB,UAAU,EAAE,EAAE,EAAE,GAAG,CAAC;MAEhD,OAAO;QACLsB,QAAQ,EAAEH,MAAM,CAACE,SAAS;QAC1BG,GAAG,EAAEL,MAAM,CAACM,SAAS;QACrB1B,MAAM,EAAEoB,MAAM,CAACO,OAAO;QACtBO,cAAc,EAAEd,MAAM,CAACe,gBAAgB;QACvCP,IAAI,EAAE;MACR,CAAC;IAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAM,IAAIvB,KAAK,CAAC,YAAYuB,KAAK,CAACrB,OAAO,EAAE,CAAC;IAC9C,CAAC,SAAS;MACR,IAAI,CAACb,YAAY,GAAG,KAAK;IAC3B;EACF;;EAEA;AACF;AACA;EACE,MAAM6B,gBAAgBA,CAACvB,UAAU,EAAEmC,eAAe,EAAEC,aAAa,EAAE;IACjE,IAAI,CAACpC,UAAU,EAAE;IAEjB,MAAMqC,KAAK,GAAG,CACZ;MAAE/B,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAY,CAAC,EACxC;MAAED,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAc,CAAC,EAC1C;MAAED,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAY,CAAC,EACxC;MAAED,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAY,CAAC,CACzC;IAED,KAAK,MAAM+B,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIC,IAAI,CAAChC,UAAU,IAAI8B,aAAa,EAAE;QACpC,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC,EAAC;QACxDxC,UAAU,CAAC;UACTM,UAAU,EAAEgC,IAAI,CAAChC,UAAU;UAC3BC,OAAO,EAAE+B,IAAI,CAAC/B,OAAO;UACrBC,MAAM,EAAE,YAAY;UACpBc,QAAQ,EAAE,IAAI,CAAC7B;QACjB,CAAC,CAAC;MACJ;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMiD,qBAAqBA,CAACpB,QAAQ,EAAE;IACpC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACxB,UAAU,oCAAoCiC,QAAQ,EAAE,CAAC;MAC9F,IAAI,CAACV,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIX,KAAK,CAAC,QAAQO,QAAQ,CAACJ,MAAM,EAAE,CAAC;MAC5C;MACA,OAAO,MAAMI,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO;QAAEpB,MAAM,EAAE,OAAO;QAAEoB,KAAK,EAAEA,KAAK,CAACrB;MAAQ,CAAC;IAClD;EACF;;EAEA;AACF;AACA;EACE,MAAMoC,gBAAgBA,CAACrB,QAAQ,EAAE;IAC/B,IAAI;MACF,IAAI,CAACA,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAI,CAAC7B,eAAe;MACjC;MAEA,IAAI,CAAC6B,QAAQ,EAAE;QACbrB,OAAO,CAAC2C,IAAI,CAAC,UAAU,CAAC;QACxB;MACF;;MAEA;MACA,IAAI,CAAClD,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,eAAe,GAAG,IAAI;MAE3BQ,OAAO,CAACC,GAAG,CAAC,YAAYoB,QAAQ,EAAE,CAAC;;MAEnC;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;EACEiB,mBAAmBA,CAAA,EAAG;IACpB,OAAO;MACLnD,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BD,eAAe,EAAE,IAAI,CAACA;IACxB,CAAC;EACH;;EAEA;AACF;AACA;EACEqD,cAAcA,CAAA,EAAG;IACf,OAAO,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC3E;;EAEA;AACF;AACA;EACEC,gBAAgBA,CAACvD,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC7C,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAM0B,GAAG,GAAG,IAAI8B,GAAG,CAACxD,QAAQ,CAAC;MAC7B,OAAO0B,GAAG,CAAC+B,QAAQ,KAAK,OAAO,IAAI/B,GAAG,CAAC+B,QAAQ,KAAK,QAAQ;IAC9D,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACEC,iBAAiBA,CAAC3D,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,IAAI,EAAEA,SAAS,YAAY4D,IAAI,CAAC,EAAE;MAC9C,OAAO;QAAEC,KAAK,EAAE,KAAK;QAAE9B,KAAK,EAAE;MAAU,CAAC;IAC3C;;IAEA;IACA,MAAM+B,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAAC/D,SAAS,CAAC8B,IAAI,CAAC,EAAE;MAC1C,OAAO;QAAE+B,KAAK,EAAE,KAAK;QAAE9B,KAAK,EAAE;MAAiC,CAAC;IAClE;;IAEA;IACA,MAAMiC,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IAChC,IAAIhE,SAAS,CAACiE,IAAI,GAAGD,OAAO,EAAE;MAC5B,OAAO;QAAEH,KAAK,EAAE,KAAK;QAAE9B,KAAK,EAAE;MAAoB,CAAC;IACrD;IAEA,OAAO;MAAE8B,KAAK,EAAE;IAAK,CAAC;EACxB;AACF;AAEA,eAAevE,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}