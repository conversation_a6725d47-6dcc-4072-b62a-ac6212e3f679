{"ast": null, "code": "import { computed } from 'vue';\nimport { useLayoutStore } from '@/store/modules/layout';\nexport default {\n  name: 'GridSystem16x9',\n  props: {\n    mode: {\n      type: String,\n      default: 'standard',\n      // standard, compact, expanded\n      validator: value => ['standard', 'compact', 'expanded'].includes(value)\n    },\n    showGridLines: {\n      type: Boolean,\n      default: false\n    },\n    customGap: {\n      type: [Number, String],\n      default: null\n    }\n  },\n  setup(props) {\n    const layoutStore = useLayoutStore();\n    const gridColumns = computed(() => layoutStore.gridColumns);\n    const gridRows = computed(() => layoutStore.gridRows);\n    const gridGap = computed(() => {\n      if (props.customGap) {\n        return typeof props.customGap === 'number' ? `${props.customGap}px` : props.customGap;\n      }\n      return `${layoutStore.gridGap}px`;\n    });\n    const gridStyles = computed(() => {\n      const baseStyles = {\n        '--grid-columns': gridColumns.value,\n        '--grid-rows': gridRows.value,\n        '--grid-gap': gridGap.value,\n        display: 'grid',\n        gridTemplateColumns: `repeat(${gridColumns.value}, 1fr)`,\n        gridTemplateRows: `repeat(${gridRows.value}, 1fr)`,\n        gap: gridGap.value,\n        width: '100%',\n        height: '100%'\n      };\n\n      // 根据模式调整样式\n      switch (props.mode) {\n        case 'compact':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 0.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 0.5)`\n          };\n        case 'expanded':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 1.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 1.5)`\n          };\n        default:\n          return baseStyles;\n      }\n    });\n    const gridInfo = computed(() => ({\n      columns: gridColumns.value,\n      rows: gridRows.value,\n      gap: gridGap.value,\n      mode: props.mode,\n      cellSize: layoutStore.gridCellSize\n    }));\n\n    // 网格位置计算工具\n    const getGridPosition = (column, row, colspan = 1, rowspan = 1) => {\n      return {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`\n      };\n    };\n\n    // 检查位置是否被占用\n    const isPositionOccupied = (column, row, colspan = 1, rowspan = 1, excludeId = null) => {\n      const components = layoutStore.visibleComponents;\n      for (const component of components) {\n        if (excludeId && component.id === excludeId) continue;\n        const {\n          column: cCol,\n          row: cRow,\n          colspan: cColspan,\n          rowspan: cRowspan\n        } = component.position;\n\n        // 检查是否有重叠\n        const horizontalOverlap = column < cCol + cColspan && column + colspan > cCol;\n        const verticalOverlap = row < cRow + cRowspan && row + rowspan > cRow;\n        if (horizontalOverlap && verticalOverlap) {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    // 查找可用位置\n    const findAvailablePosition = (colspan = 1, rowspan = 1) => {\n      for (let row = 1; row <= gridRows.value - rowspan + 1; row++) {\n        for (let column = 1; column <= gridColumns.value - colspan + 1; column++) {\n          if (!isPositionOccupied(column, row, colspan, rowspan)) {\n            return {\n              column,\n              row\n            };\n          }\n        }\n      }\n      return null;\n    };\n    return {\n      gridColumns,\n      gridRows,\n      gridStyles,\n      gridInfo,\n      getGridPosition,\n      isPositionOccupied,\n      findAvailablePosition\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "useLayoutStore", "name", "props", "mode", "type", "String", "default", "validator", "value", "includes", "showGridLines", "Boolean", "customGap", "Number", "setup", "layoutStore", "gridColumns", "gridRows", "gridGap", "gridStyles", "baseStyles", "display", "gridTemplateColumns", "gridTemplateRows", "gap", "width", "height", "gridInfo", "columns", "rows", "cellSize", "gridCellSize", "getGridPosition", "column", "row", "colspan", "rowspan", "gridColumn", "gridRow", "isPositionOccupied", "excludeId", "components", "visibleComponents", "component", "id", "cCol", "cRow", "cColspan", "cRowspan", "position", "horizontalOverlap", "verticalOverlap", "findAvailablePosition"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\layout\\GridSystem16x9.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"grid-system-16x9\"\n    :style=\"gridStyles\"\n    :class=\"[\n      `grid-mode-${mode}`,\n      { 'show-grid-lines': showGridLines }\n    ]\"\n  >\n    <!-- 网格线显示 (调试用) -->\n    <div v-if=\"showGridLines\" class=\"grid-lines\">\n      <div \n        v-for=\"i in gridColumns\" \n        :key=\"`col-${i}`\" \n        class=\"grid-line-vertical\"\n        :style=\"{ gridColumn: i }\"\n      ></div>\n      <div \n        v-for=\"i in gridRows\" \n        :key=\"`row-${i}`\" \n        class=\"grid-line-horizontal\"\n        :style=\"{ gridRow: i }\"\n      ></div>\n    </div>\n\n    <!-- 网格内容 -->\n    <slot :grid=\"gridInfo\"></slot>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useLayoutStore } from '@/store/modules/layout'\n\nexport default {\n  name: 'GridSystem16x9',\n  props: {\n    mode: {\n      type: String,\n      default: 'standard', // standard, compact, expanded\n      validator: value => ['standard', 'compact', 'expanded'].includes(value)\n    },\n    showGridLines: {\n      type: Boolean,\n      default: false\n    },\n    customGap: {\n      type: [Number, String],\n      default: null\n    }\n  },\n  setup(props) {\n    const layoutStore = useLayoutStore()\n\n    const gridColumns = computed(() => layoutStore.gridColumns)\n    const gridRows = computed(() => layoutStore.gridRows)\n    \n    const gridGap = computed(() => {\n      if (props.customGap) {\n        return typeof props.customGap === 'number' ? `${props.customGap}px` : props.customGap\n      }\n      return `${layoutStore.gridGap}px`\n    })\n\n    const gridStyles = computed(() => {\n      const baseStyles = {\n        '--grid-columns': gridColumns.value,\n        '--grid-rows': gridRows.value,\n        '--grid-gap': gridGap.value,\n        display: 'grid',\n        gridTemplateColumns: `repeat(${gridColumns.value}, 1fr)`,\n        gridTemplateRows: `repeat(${gridRows.value}, 1fr)`,\n        gap: gridGap.value,\n        width: '100%',\n        height: '100%'\n      }\n\n      // 根据模式调整样式\n      switch (props.mode) {\n        case 'compact':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 0.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 0.5)`\n          }\n        case 'expanded':\n          return {\n            ...baseStyles,\n            gap: `calc(${gridGap.value} * 1.5)`,\n            '--grid-gap': `calc(${gridGap.value} * 1.5)`\n          }\n        default:\n          return baseStyles\n      }\n    })\n\n    const gridInfo = computed(() => ({\n      columns: gridColumns.value,\n      rows: gridRows.value,\n      gap: gridGap.value,\n      mode: props.mode,\n      cellSize: layoutStore.gridCellSize\n    }))\n\n    // 网格位置计算工具\n    const getGridPosition = (column, row, colspan = 1, rowspan = 1) => {\n      return {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`\n      }\n    }\n\n    // 检查位置是否被占用\n    const isPositionOccupied = (column, row, colspan = 1, rowspan = 1, excludeId = null) => {\n      const components = layoutStore.visibleComponents\n      \n      for (const component of components) {\n        if (excludeId && component.id === excludeId) continue\n        \n        const { column: cCol, row: cRow, colspan: cColspan, rowspan: cRowspan } = component.position\n        \n        // 检查是否有重叠\n        const horizontalOverlap = column < cCol + cColspan && column + colspan > cCol\n        const verticalOverlap = row < cRow + cRowspan && row + rowspan > cRow\n        \n        if (horizontalOverlap && verticalOverlap) {\n          return true\n        }\n      }\n      \n      return false\n    }\n\n    // 查找可用位置\n    const findAvailablePosition = (colspan = 1, rowspan = 1) => {\n      for (let row = 1; row <= gridRows.value - rowspan + 1; row++) {\n        for (let column = 1; column <= gridColumns.value - colspan + 1; column++) {\n          if (!isPositionOccupied(column, row, colspan, rowspan)) {\n            return { column, row }\n          }\n        }\n      }\n      return null\n    }\n\n    return {\n      gridColumns,\n      gridRows,\n      gridStyles,\n      gridInfo,\n      getGridPosition,\n      isPositionOccupied,\n      findAvailablePosition\n    }\n  }\n}\n</script>\n\n<style scoped>\n.grid-system-16x9 {\n  position: relative;\n  box-sizing: border-box;\n}\n\n/* 网格线显示 - 简化实现 */\n.grid-lines {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 10000;\n\n  /* 使用背景图案创建网格线 */\n  background-image:\n    /* 垂直线 */\n    repeating-linear-gradient(\n      to right,\n      transparent 0,\n      transparent calc(100% / var(--grid-columns) - 1px),\n      #FFD700 calc(100% / var(--grid-columns) - 1px),\n      #FFD700 calc(100% / var(--grid-columns))\n    ),\n    /* 水平线 */\n    repeating-linear-gradient(\n      to bottom,\n      transparent 0,\n      transparent calc(100% / var(--grid-rows) - 1px),\n      #FFD700 calc(100% / var(--grid-rows) - 1px),\n      #FFD700 calc(100% / var(--grid-rows))\n    );\n}\n\n.grid-line-vertical,\n.grid-line-horizontal {\n  /* 移除单独的网格线元素，使用背景图案替代 */\n  display: none;\n}\n\n/* 网格模式样式 */\n.grid-mode-standard {\n  /* 标准模式样式 */\n}\n\n.grid-mode-compact {\n  /* 紧凑模式样式 */\n}\n\n.grid-mode-expanded {\n  /* 扩展模式样式 */\n}\n\n/* 调试模式 - 简化网格线显示 */\n.show-grid-lines {\n  position: relative;\n}\n\n/* 网格线覆盖层 */\n.show-grid-lines::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 10000;\n\n  /* 高对比度网格线 */\n  background-image:\n    /* 垂直线 */\n    repeating-linear-gradient(\n      to right,\n      transparent 0,\n      transparent calc(100% / var(--grid-columns) - 2px),\n      #FFD700 calc(100% / var(--grid-columns) - 2px),\n      #FFD700 calc(100% / var(--grid-columns))\n    ),\n    /* 水平线 */\n    repeating-linear-gradient(\n      to bottom,\n      transparent 0,\n      transparent calc(100% / var(--grid-rows) - 2px),\n      #FFD700 calc(100% / var(--grid-rows) - 2px),\n      #FFD700 calc(100% / var(--grid-rows))\n    );\n\n  /* 添加边框突出显示 */\n  border: 2px solid #FFD700;\n  box-shadow:\n    inset 0 0 0 1px rgba(255, 215, 0, 0.3),\n    0 0 10px rgba(255, 215, 0, 0.5);\n}\n\n/* 响应式调整 */\n@media (max-width: 1024px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.75);\n  }\n}\n\n@media (max-width: 768px) {\n  .grid-system-16x9 {\n    --grid-gap: calc(var(--grid-gap) * 0.5);\n  }\n}\n</style>\n"], "mappings": "AA+BA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,cAAa,QAAS,wBAAuB;AAEtD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,UAAU;MAAE;MACrBC,SAAS,EAAEC,KAAI,IAAK,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACD,KAAK;IACxE,CAAC;IACDE,aAAa,EAAE;MACbN,IAAI,EAAEO,OAAO;MACbL,OAAO,EAAE;IACX,CAAC;IACDM,SAAS,EAAE;MACTR,IAAI,EAAE,CAACS,MAAM,EAAER,MAAM,CAAC;MACtBC,OAAO,EAAE;IACX;EACF,CAAC;EACDQ,KAAKA,CAACZ,KAAK,EAAE;IACX,MAAMa,WAAU,GAAIf,cAAc,CAAC;IAEnC,MAAMgB,WAAU,GAAIjB,QAAQ,CAAC,MAAMgB,WAAW,CAACC,WAAW;IAC1D,MAAMC,QAAO,GAAIlB,QAAQ,CAAC,MAAMgB,WAAW,CAACE,QAAQ;IAEpD,MAAMC,OAAM,GAAInB,QAAQ,CAAC,MAAM;MAC7B,IAAIG,KAAK,CAACU,SAAS,EAAE;QACnB,OAAO,OAAOV,KAAK,CAACU,SAAQ,KAAM,QAAO,GAAI,GAAGV,KAAK,CAACU,SAAS,IAAG,GAAIV,KAAK,CAACU,SAAQ;MACtF;MACA,OAAO,GAAGG,WAAW,CAACG,OAAO,IAAG;IAClC,CAAC;IAED,MAAMC,UAAS,GAAIpB,QAAQ,CAAC,MAAM;MAChC,MAAMqB,UAAS,GAAI;QACjB,gBAAgB,EAAEJ,WAAW,CAACR,KAAK;QACnC,aAAa,EAAES,QAAQ,CAACT,KAAK;QAC7B,YAAY,EAAEU,OAAO,CAACV,KAAK;QAC3Ba,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE,UAAUN,WAAW,CAACR,KAAK,QAAQ;QACxDe,gBAAgB,EAAE,UAAUN,QAAQ,CAACT,KAAK,QAAQ;QAClDgB,GAAG,EAAEN,OAAO,CAACV,KAAK;QAClBiB,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV;;MAEA;MACA,QAAQxB,KAAK,CAACC,IAAI;QAChB,KAAK,SAAS;UACZ,OAAO;YACL,GAAGiB,UAAU;YACbI,GAAG,EAAE,QAAQN,OAAO,CAACV,KAAK,SAAS;YACnC,YAAY,EAAE,QAAQU,OAAO,CAACV,KAAK;UACrC;QACF,KAAK,UAAU;UACb,OAAO;YACL,GAAGY,UAAU;YACbI,GAAG,EAAE,QAAQN,OAAO,CAACV,KAAK,SAAS;YACnC,YAAY,EAAE,QAAQU,OAAO,CAACV,KAAK;UACrC;QACF;UACE,OAAOY,UAAS;MACpB;IACF,CAAC;IAED,MAAMO,QAAO,GAAI5B,QAAQ,CAAC,OAAO;MAC/B6B,OAAO,EAAEZ,WAAW,CAACR,KAAK;MAC1BqB,IAAI,EAAEZ,QAAQ,CAACT,KAAK;MACpBgB,GAAG,EAAEN,OAAO,CAACV,KAAK;MAClBL,IAAI,EAAED,KAAK,CAACC,IAAI;MAChB2B,QAAQ,EAAEf,WAAW,CAACgB;IACxB,CAAC,CAAC;;IAEF;IACA,MAAMC,eAAc,GAAIA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAM,GAAI,CAAC,EAAEC,OAAM,GAAI,CAAC,KAAK;MACjE,OAAO;QACLC,UAAU,EAAE,GAAGJ,MAAM,WAAWE,OAAO,EAAE;QACzCG,OAAO,EAAE,GAAGJ,GAAG,WAAWE,OAAO;MACnC;IACF;;IAEA;IACA,MAAMG,kBAAiB,GAAIA,CAACN,MAAM,EAAEC,GAAG,EAAEC,OAAM,GAAI,CAAC,EAAEC,OAAM,GAAI,CAAC,EAAEI,SAAQ,GAAI,IAAI,KAAK;MACtF,MAAMC,UAAS,GAAI1B,WAAW,CAAC2B,iBAAgB;MAE/C,KAAK,MAAMC,SAAQ,IAAKF,UAAU,EAAE;QAClC,IAAID,SAAQ,IAAKG,SAAS,CAACC,EAAC,KAAMJ,SAAS,EAAE;QAE7C,MAAM;UAAEP,MAAM,EAAEY,IAAI;UAAEX,GAAG,EAAEY,IAAI;UAAEX,OAAO,EAAEY,QAAQ;UAAEX,OAAO,EAAEY;QAAS,IAAIL,SAAS,CAACM,QAAO;;QAE3F;QACA,MAAMC,iBAAgB,GAAIjB,MAAK,GAAIY,IAAG,GAAIE,QAAO,IAAKd,MAAK,GAAIE,OAAM,GAAIU,IAAG;QAC5E,MAAMM,eAAc,GAAIjB,GAAE,GAAIY,IAAG,GAAIE,QAAO,IAAKd,GAAE,GAAIE,OAAM,GAAIU,IAAG;QAEpE,IAAII,iBAAgB,IAAKC,eAAe,EAAE;UACxC,OAAO,IAAG;QACZ;MACF;MAEA,OAAO,KAAI;IACb;;IAEA;IACA,MAAMC,qBAAoB,GAAIA,CAACjB,OAAM,GAAI,CAAC,EAAEC,OAAM,GAAI,CAAC,KAAK;MAC1D,KAAK,IAAIF,GAAE,GAAI,CAAC,EAAEA,GAAE,IAAKjB,QAAQ,CAACT,KAAI,GAAI4B,OAAM,GAAI,CAAC,EAAEF,GAAG,EAAE,EAAE;QAC5D,KAAK,IAAID,MAAK,GAAI,CAAC,EAAEA,MAAK,IAAKjB,WAAW,CAACR,KAAI,GAAI2B,OAAM,GAAI,CAAC,EAAEF,MAAM,EAAE,EAAE;UACxE,IAAI,CAACM,kBAAkB,CAACN,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,CAAC,EAAE;YACtD,OAAO;cAAEH,MAAM;cAAEC;YAAI;UACvB;QACF;MACF;MACA,OAAO,IAAG;IACZ;IAEA,OAAO;MACLlB,WAAW;MACXC,QAAQ;MACRE,UAAU;MACVQ,QAAQ;MACRK,eAAe;MACfO,kBAAkB;MAClBa;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}