{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nclass TtsService {\n  constructor() {\n    // 使用系统自带的TTS服务\n    this.synthesis = window.speechSynthesis;\n    this.isSupported = 'speechSynthesis' in window;\n    this.currentUtterance = null;\n    this.retryCount = 0;\n    this.maxRetries = 3;\n\n    // 等待语音列表加载完成\n    this.waitForVoices();\n  }\n\n  // 等待语音列表加载完成\n  waitForVoices() {\n    if (this.synthesis.getVoices().length === 0) {\n      // 在Chrome中，语音列表是异步加载的\n      this.synthesis.onvoiceschanged = () => {\n        console.log('语音列表加载完成，共', this.synthesis.getVoices().length, '个语音');\n      };\n    }\n  }\n  async speak(text, options = {}) {\n    if (!this.isSupported) {\n      console.warn('浏览器不支持语音合成');\n      return;\n    }\n\n    // 停止当前播放\n    this.synthesis.cancel();\n    this.currentUtterance = null;\n    this.retryCount = 0;\n    return this.speakWithRetry(text, options);\n  }\n  async speakWithRetry(text, options = {}) {\n    const utterance = new SpeechSynthesisUtterance(text);\n\n    // 设置语音参数\n    utterance.lang = options.lang || 'zh-CN';\n    utterance.rate = options.rate || 1.0;\n    utterance.pitch = options.pitch || 1.0;\n    utterance.volume = options.volume || 1.0;\n\n    // 选择中文语音\n    const voices = this.synthesis.getVoices();\n    const chineseVoice = voices.find(voice => voice.lang.includes('zh') || voice.name.includes('Chinese'));\n    if (chineseVoice) {\n      utterance.voice = chineseVoice;\n      console.log('使用中文语音:', chineseVoice.name);\n    } else {\n      console.warn('未找到中文语音，使用默认语音');\n    }\n    this.currentUtterance = utterance;\n    return new Promise((resolve, reject) => {\n      utterance.onend = () => {\n        this.currentUtterance = null;\n        this.retryCount = 0;\n        resolve();\n      };\n      utterance.onerror = async event => {\n        console.error('语音合成错误:', event.error);\n        this.currentUtterance = null;\n\n        // 处理中断错误，尝试重试\n        if (event.error === 'interrupted' && this.retryCount < this.maxRetries) {\n          this.retryCount++;\n          console.log(`语音合成被中断，第${this.retryCount}次重试...`);\n\n          // 延迟后重试\n          setTimeout(async () => {\n            try {\n              await this.speakWithRetry(text, options);\n              resolve();\n            } catch (error) {\n              reject(error);\n            }\n          }, 500 * this.retryCount); // 递增延迟\n        } else {\n          reject(new Error(`语音合成失败: ${event.error}`));\n        }\n      };\n      try {\n        this.synthesis.speak(utterance);\n      } catch (error) {\n        console.error('语音合成异常:', error);\n        reject(new Error(`语音合成失败: ${error.message}`));\n      }\n    });\n  }\n  stop() {\n    if (this.synthesis) {\n      this.synthesis.cancel();\n      this.currentUtterance = null;\n      this.retryCount = 0;\n    }\n  }\n  getVoices() {\n    return this.synthesis.getVoices();\n  }\n\n  // 获取可用的中文语音\n  getChineseVoices() {\n    const voices = this.getVoices();\n    return voices.filter(voice => voice.lang.includes('zh') || voice.name.includes('Chinese') || voice.name.includes('中文'));\n  }\n\n  // 设置默认语音\n  setDefaultVoice(voiceName) {\n    const voices = this.getVoices();\n    const voice = voices.find(v => v.name === voiceName);\n    if (voice) {\n      this.defaultVoice = voice;\n    }\n  }\n\n  // 检查是否正在播放\n  get isSpeaking() {\n    return this.synthesis.speaking;\n  }\n\n  // 检查是否暂停\n  get isPaused() {\n    return this.synthesis.paused;\n  }\n\n  // 暂停播放\n  pause() {\n    if (this.synthesis) {\n      this.synthesis.pause();\n    }\n  }\n\n  // 恢复播放\n  resume() {\n    if (this.synthesis) {\n      this.synthesis.resume();\n    }\n  }\n\n  // 批量播放文本\n  async speakTexts(texts, options = {}) {\n    for (const text of texts) {\n      await this.speak(text, options);\n    }\n  }\n\n  // 获取语音状态\n  getStatus() {\n    return {\n      isSupported: this.isSupported,\n      isSpeaking: this.isSpeaking,\n      isPaused: this.isPaused,\n      voices: this.getVoices().length,\n      chineseVoices: this.getChineseVoices().length\n    };\n  }\n}\nexport default TtsService;", "map": {"version": 3, "names": ["TtsService", "constructor", "synthesis", "window", "speechSynthesis", "isSupported", "currentUtterance", "retryCount", "maxRetries", "waitForVoices", "getVoices", "length", "onvoiceschanged", "console", "log", "speak", "text", "options", "warn", "cancel", "speakWithRetry", "utterance", "SpeechSynthesisUtterance", "lang", "rate", "pitch", "volume", "voices", "chineseVoice", "find", "voice", "includes", "name", "Promise", "resolve", "reject", "onend", "onerror", "event", "error", "setTimeout", "Error", "message", "stop", "getChineseVoices", "filter", "setDefaultVoice", "voiceName", "v", "defaultVoice", "isSpeaking", "speaking", "isPaused", "paused", "pause", "resume", "speakTexts", "texts", "getStatus", "chineseVoices"], "sources": ["F:/工作/theme/ai-hmi/src/services/TtsService.js"], "sourcesContent": ["class TtsService {\r\n  constructor() {\r\n    // 使用系统自带的TTS服务\r\n    this.synthesis = window.speechSynthesis\r\n    this.isSupported = 'speechSynthesis' in window\r\n    this.currentUtterance = null\r\n    this.retryCount = 0\r\n    this.maxRetries = 3\r\n    \r\n    // 等待语音列表加载完成\r\n    this.waitForVoices()\r\n  }\r\n\r\n  // 等待语音列表加载完成\r\n  waitForVoices() {\r\n    if (this.synthesis.getVoices().length === 0) {\r\n      // 在Chrome中，语音列表是异步加载的\r\n      this.synthesis.onvoiceschanged = () => {\r\n        console.log('语音列表加载完成，共', this.synthesis.getVoices().length, '个语音')\r\n      }\r\n    }\r\n  }\r\n\r\n  async speak(text, options = {}) {\r\n    if (!this.isSupported) {\r\n      console.warn('浏览器不支持语音合成')\r\n      return\r\n    }\r\n\r\n    // 停止当前播放\r\n    this.synthesis.cancel()\r\n    this.currentUtterance = null\r\n    this.retryCount = 0\r\n\r\n    return this.speakWithRetry(text, options)\r\n  }\r\n\r\n  async speakWithRetry(text, options = {}) {\r\n    const utterance = new SpeechSynthesisUtterance(text)\r\n    \r\n    // 设置语音参数\r\n    utterance.lang = options.lang || 'zh-CN'\r\n    utterance.rate = options.rate || 1.0\r\n    utterance.pitch = options.pitch || 1.0\r\n    utterance.volume = options.volume || 1.0\r\n\r\n    // 选择中文语音\r\n    const voices = this.synthesis.getVoices()\r\n    const chineseVoice = voices.find(voice => \r\n      voice.lang.includes('zh') || voice.name.includes('Chinese')\r\n    )\r\n    \r\n    if (chineseVoice) {\r\n      utterance.voice = chineseVoice\r\n      console.log('使用中文语音:', chineseVoice.name)\r\n    } else {\r\n      console.warn('未找到中文语音，使用默认语音')\r\n    }\r\n\r\n    this.currentUtterance = utterance\r\n\r\n    return new Promise((resolve, reject) => {\r\n      utterance.onend = () => {\r\n        this.currentUtterance = null\r\n        this.retryCount = 0\r\n        resolve()\r\n      }\r\n\r\n      utterance.onerror = async (event) => {\r\n        console.error('语音合成错误:', event.error)\r\n        this.currentUtterance = null\r\n        \r\n        // 处理中断错误，尝试重试\r\n        if (event.error === 'interrupted' && this.retryCount < this.maxRetries) {\r\n          this.retryCount++\r\n          console.log(`语音合成被中断，第${this.retryCount}次重试...`)\r\n          \r\n          // 延迟后重试\r\n          setTimeout(async () => {\r\n            try {\r\n              await this.speakWithRetry(text, options)\r\n              resolve()\r\n            } catch (error) {\r\n              reject(error)\r\n            }\r\n          }, 500 * this.retryCount) // 递增延迟\r\n        } else {\r\n          reject(new Error(`语音合成失败: ${event.error}`))\r\n        }\r\n      }\r\n\r\n      try {\r\n        this.synthesis.speak(utterance)\r\n      } catch (error) {\r\n        console.error('语音合成异常:', error)\r\n        reject(new Error(`语音合成失败: ${error.message}`))\r\n      }\r\n    })\r\n  }\r\n\r\n  stop() {\r\n    if (this.synthesis) {\r\n      this.synthesis.cancel()\r\n      this.currentUtterance = null\r\n      this.retryCount = 0\r\n    }\r\n  }\r\n\r\n  getVoices() {\r\n    return this.synthesis.getVoices()\r\n  }\r\n\r\n  // 获取可用的中文语音\r\n  getChineseVoices() {\r\n    const voices = this.getVoices()\r\n    return voices.filter(voice => \r\n      voice.lang.includes('zh') || \r\n      voice.name.includes('Chinese') ||\r\n      voice.name.includes('中文')\r\n    )\r\n  }\r\n\r\n  // 设置默认语音\r\n  setDefaultVoice(voiceName) {\r\n    const voices = this.getVoices()\r\n    const voice = voices.find(v => v.name === voiceName)\r\n    if (voice) {\r\n      this.defaultVoice = voice\r\n    }\r\n  }\r\n\r\n  // 检查是否正在播放\r\n  get isSpeaking() {\r\n    return this.synthesis.speaking\r\n  }\r\n\r\n  // 检查是否暂停\r\n  get isPaused() {\r\n    return this.synthesis.paused\r\n  }\r\n\r\n  // 暂停播放\r\n  pause() {\r\n    if (this.synthesis) {\r\n      this.synthesis.pause()\r\n    }\r\n  }\r\n\r\n  // 恢复播放\r\n  resume() {\r\n    if (this.synthesis) {\r\n      this.synthesis.resume()\r\n    }\r\n  }\r\n\r\n  // 批量播放文本\r\n  async speakTexts(texts, options = {}) {\r\n    for (const text of texts) {\r\n      await this.speak(text, options)\r\n    }\r\n  }\r\n\r\n  // 获取语音状态\r\n  getStatus() {\r\n    return {\r\n      isSupported: this.isSupported,\r\n      isSpeaking: this.isSpeaking,\r\n      isPaused: this.isPaused,\r\n      voices: this.getVoices().length,\r\n      chineseVoices: this.getChineseVoices().length\r\n    }\r\n  }\r\n}\r\n\r\nexport default TtsService"], "mappings": ";;;AAAA,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,eAAe;IACvC,IAAI,CAACC,WAAW,GAAG,iBAAiB,IAAIF,MAAM;IAC9C,IAAI,CAACG,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;;IAEnB;IACA,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;EACAA,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACP,SAAS,CAACQ,SAAS,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3C;MACA,IAAI,CAACT,SAAS,CAACU,eAAe,GAAG,MAAM;QACrCC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACZ,SAAS,CAACQ,SAAS,CAAC,CAAC,CAACC,MAAM,EAAE,KAAK,CAAC;MACrE,CAAC;IACH;EACF;EAEA,MAAMI,KAAKA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACZ,WAAW,EAAE;MACrBQ,OAAO,CAACK,IAAI,CAAC,YAAY,CAAC;MAC1B;IACF;;IAEA;IACA,IAAI,CAAChB,SAAS,CAACiB,MAAM,CAAC,CAAC;IACvB,IAAI,CAACb,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;IAEnB,OAAO,IAAI,CAACa,cAAc,CAACJ,IAAI,EAAEC,OAAO,CAAC;EAC3C;EAEA,MAAMG,cAAcA,CAACJ,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,MAAMI,SAAS,GAAG,IAAIC,wBAAwB,CAACN,IAAI,CAAC;;IAEpD;IACAK,SAAS,CAACE,IAAI,GAAGN,OAAO,CAACM,IAAI,IAAI,OAAO;IACxCF,SAAS,CAACG,IAAI,GAAGP,OAAO,CAACO,IAAI,IAAI,GAAG;IACpCH,SAAS,CAACI,KAAK,GAAGR,OAAO,CAACQ,KAAK,IAAI,GAAG;IACtCJ,SAAS,CAACK,MAAM,GAAGT,OAAO,CAACS,MAAM,IAAI,GAAG;;IAExC;IACA,MAAMC,MAAM,GAAG,IAAI,CAACzB,SAAS,CAACQ,SAAS,CAAC,CAAC;IACzC,MAAMkB,YAAY,GAAGD,MAAM,CAACE,IAAI,CAACC,KAAK,IACpCA,KAAK,CAACP,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,IAAID,KAAK,CAACE,IAAI,CAACD,QAAQ,CAAC,SAAS,CAC5D,CAAC;IAED,IAAIH,YAAY,EAAE;MAChBP,SAAS,CAACS,KAAK,GAAGF,YAAY;MAC9Bf,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEc,YAAY,CAACI,IAAI,CAAC;IAC3C,CAAC,MAAM;MACLnB,OAAO,CAACK,IAAI,CAAC,gBAAgB,CAAC;IAChC;IAEA,IAAI,CAACZ,gBAAgB,GAAGe,SAAS;IAEjC,OAAO,IAAIY,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtCd,SAAS,CAACe,KAAK,GAAG,MAAM;QACtB,IAAI,CAAC9B,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;QACnB2B,OAAO,CAAC,CAAC;MACX,CAAC;MAEDb,SAAS,CAACgB,OAAO,GAAG,MAAOC,KAAK,IAAK;QACnCzB,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAED,KAAK,CAACC,KAAK,CAAC;QACrC,IAAI,CAACjC,gBAAgB,GAAG,IAAI;;QAE5B;QACA,IAAIgC,KAAK,CAACC,KAAK,KAAK,aAAa,IAAI,IAAI,CAAChC,UAAU,GAAG,IAAI,CAACC,UAAU,EAAE;UACtE,IAAI,CAACD,UAAU,EAAE;UACjBM,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACP,UAAU,QAAQ,CAAC;;UAEhD;UACAiC,UAAU,CAAC,YAAY;YACrB,IAAI;cACF,MAAM,IAAI,CAACpB,cAAc,CAACJ,IAAI,EAAEC,OAAO,CAAC;cACxCiB,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,OAAOK,KAAK,EAAE;cACdJ,MAAM,CAACI,KAAK,CAAC;YACf;UACF,CAAC,EAAE,GAAG,GAAG,IAAI,CAAChC,UAAU,CAAC,EAAC;QAC5B,CAAC,MAAM;UACL4B,MAAM,CAAC,IAAIM,KAAK,CAAC,WAAWH,KAAK,CAACC,KAAK,EAAE,CAAC,CAAC;QAC7C;MACF,CAAC;MAED,IAAI;QACF,IAAI,CAACrC,SAAS,CAACa,KAAK,CAACM,SAAS,CAAC;MACjC,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BJ,MAAM,CAAC,IAAIM,KAAK,CAAC,WAAWF,KAAK,CAACG,OAAO,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAC,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACzC,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACiB,MAAM,CAAC,CAAC;MACvB,IAAI,CAACb,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;IACrB;EACF;EAEAG,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,SAAS,CAACQ,SAAS,CAAC,CAAC;EACnC;;EAEA;EACAkC,gBAAgBA,CAAA,EAAG;IACjB,MAAMjB,MAAM,GAAG,IAAI,CAACjB,SAAS,CAAC,CAAC;IAC/B,OAAOiB,MAAM,CAACkB,MAAM,CAACf,KAAK,IACxBA,KAAK,CAACP,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,IACzBD,KAAK,CAACE,IAAI,CAACD,QAAQ,CAAC,SAAS,CAAC,IAC9BD,KAAK,CAACE,IAAI,CAACD,QAAQ,CAAC,IAAI,CAC1B,CAAC;EACH;;EAEA;EACAe,eAAeA,CAACC,SAAS,EAAE;IACzB,MAAMpB,MAAM,GAAG,IAAI,CAACjB,SAAS,CAAC,CAAC;IAC/B,MAAMoB,KAAK,GAAGH,MAAM,CAACE,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAAChB,IAAI,KAAKe,SAAS,CAAC;IACpD,IAAIjB,KAAK,EAAE;MACT,IAAI,CAACmB,YAAY,GAAGnB,KAAK;IAC3B;EACF;;EAEA;EACA,IAAIoB,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChD,SAAS,CAACiD,QAAQ;EAChC;;EAEA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClD,SAAS,CAACmD,MAAM;EAC9B;;EAEA;EACAC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACpD,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACoD,KAAK,CAAC,CAAC;IACxB;EACF;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACrD,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACqD,MAAM,CAAC,CAAC;IACzB;EACF;;EAEA;EACA,MAAMC,UAAUA,CAACC,KAAK,EAAExC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,KAAK,MAAMD,IAAI,IAAIyC,KAAK,EAAE;MACxB,MAAM,IAAI,CAAC1C,KAAK,CAACC,IAAI,EAAEC,OAAO,CAAC;IACjC;EACF;;EAEA;EACAyC,SAASA,CAAA,EAAG;IACV,OAAO;MACLrD,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B6C,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBzB,MAAM,EAAE,IAAI,CAACjB,SAAS,CAAC,CAAC,CAACC,MAAM;MAC/BgD,aAAa,EAAE,IAAI,CAACf,gBAAgB,CAAC,CAAC,CAACjC;IACzC,CAAC;EACH;AACF;AAEA,eAAeX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}