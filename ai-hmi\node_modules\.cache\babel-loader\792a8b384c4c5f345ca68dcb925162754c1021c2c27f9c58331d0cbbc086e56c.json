{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport LlmService from './LlmService';\nclass ImageGenerationService {\n  constructor() {\n    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000';\n  }\n  async generateWallpaper(prompt, taskId = null) {\n    if (!taskId) {\n      taskId = `wallpaper_${Date.now()}`;\n    }\n    try {\n      // 使用LLM增强提示词\n      const llmService = new LlmService();\n      const enhancedPrompt = await llmService.generateResponse(prompt);\n\n      // 调用简化版的文生图接口（用于AI-HMI测试）\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/kolors-simple/text-to-image`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          prompt: enhancedPrompt,\n          task_id: taskId\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n\n      // 下载并保存图片到本地\n      const localImagePath = await this.saveImageToLocal(data.image_url, taskId);\n      const result = {\n        imageUrl: data.image_url,\n        // 保存Kolors生成的图片URL\n        imagePath: localImagePath,\n        // 保存本地图片路径\n        taskId: taskId,\n        prompt: enhancedPrompt\n      };\n\n      // 保存当前壁纸路径到全局状态，供动态壁纸生成使用\n      this.saveCurrentWallpaperPath(localImagePath, data.image_url);\n      return result;\n    } catch (error) {\n      console.error('壁纸生成失败:', error);\n      return this.getFallbackWallpaper();\n    }\n  }\n  getFallbackWallpaper() {\n    return {\n      imageUrl: '/images/default-glass-wallpaper.svg',\n      taskId: 'fallback',\n      prompt: 'default glassmorphism'\n    };\n  }\n\n  // 检查图片是否加载成功\n  async checkImageLoad(imageUrl) {\n    return new Promise(resolve => {\n      const img = new Image();\n      img.onload = () => resolve(true);\n      img.onerror = () => resolve(false);\n      img.src = imageUrl;\n    });\n  }\n\n  // 预加载图片\n  async preloadImage(imageUrl) {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      img.onload = () => resolve(img);\n      img.onerror = () => reject(new Error('图片加载失败'));\n      img.src = imageUrl;\n    });\n  }\n\n  // 批量生成壁纸\n  async generateMultipleWallpapers(prompts, options = {}) {\n    const {\n      concurrent = 3\n    } = options;\n    const results = [];\n\n    // 分批处理以避免过多的并发请求\n    for (let i = 0; i < prompts.length; i += concurrent) {\n      const batch = prompts.slice(i, i + concurrent);\n      const batchPromises = batch.map(async (prompt, index) => {\n        const taskId = `wallpaper_${Date.now()}_${i + index}`;\n        return this.generateWallpaper(prompt, taskId);\n      });\n      const batchResults = await Promise.allSettled(batchPromises);\n      results.push(...batchResults.map(result => result.status === 'fulfilled' ? result.value : null).filter(Boolean));\n    }\n    return results;\n  }\n\n  // 获取生成历史\n  getGenerationHistory() {\n    const history = localStorage.getItem('wallpaper_generation_history');\n    return history ? JSON.parse(history) : [];\n  }\n\n  // 保存生成历史\n  saveToHistory(wallpaperData) {\n    const history = this.getGenerationHistory();\n    history.unshift({\n      ...wallpaperData,\n      timestamp: new Date().toISOString()\n    });\n\n    // 只保留最近50条记录\n    const trimmedHistory = history.slice(0, 50);\n    localStorage.setItem('wallpaper_generation_history', JSON.stringify(trimmedHistory));\n  }\n\n  // 清除历史记录\n  clearHistory() {\n    localStorage.removeItem('wallpaper_generation_history');\n  }\n\n  // 获取统计信息\n  getStatistics() {\n    const history = this.getGenerationHistory();\n    return {\n      totalGenerated: history.length,\n      todayGenerated: history.filter(item => {\n        const today = new Date().toDateString();\n        return new Date(item.timestamp).toDateString() === today;\n      }).length,\n      successRate: history.filter(item => item.imageUrl && !item.imageUrl.includes('default')).length / history.length * 100\n    };\n  }\n\n  // 保存图片到本地\n  async saveImageToLocal(imageUrl, taskId) {\n    try {\n      console.log('开始保存图片到本地:', imageUrl);\n\n      // 下载图片\n      const response = await fetch(imageUrl);\n      if (!response.ok) {\n        throw new Error(`下载图片失败: ${response.status}`);\n      }\n      const blob = await response.blob();\n\n      // 生成本地文件名\n      const fileName = `wallpaper_${taskId || Date.now()}.jpg`;\n      const localPath = `/images/${fileName}`;\n\n      // 创建本地URL并保存到public/images目录\n      const file = new File([blob], fileName, {\n        type: blob.type\n      });\n      await this.saveFileToPublicImages(file, fileName);\n      console.log('图片已保存到本地:', localPath);\n      return localPath;\n    } catch (error) {\n      console.error('保存图片到本地失败:', error);\n      return null;\n    }\n  }\n\n  // 保存文件到public/images目录\n  async saveFileToPublicImages(file, fileName) {\n    try {\n      // 使用FormData上传文件到后端保存\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('fileName', fileName);\n      const response = await fetch(`${process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'}/api/save-image`, {\n        method: 'POST',\n        body: formData\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log('文件已保存到服务器:', result.path);\n        return result.path;\n      } else {\n        // 如果后端保存失败，回退到浏览器下载方式\n        console.warn('后端保存失败，使用浏览器下载方式');\n        const url = URL.createObjectURL(file);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        console.log('文件已通过下载保存:', fileName);\n        return `/images/${fileName}`;\n      }\n    } catch (error) {\n      console.error('保存文件失败:', error);\n      // 回退到浏览器下载方式\n      try {\n        const url = URL.createObjectURL(file);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        console.log('文件已通过下载保存:', fileName);\n        return `/images/${fileName}`;\n      } catch (downloadError) {\n        console.error('下载保存也失败:', downloadError);\n        throw error;\n      }\n    }\n  }\n\n  // 保存当前壁纸路径\n  saveCurrentWallpaperPath(imagePath, imageUrl) {\n    try {\n      const wallpaperInfo = {\n        imagePath: imagePath,\n        imageUrl: imageUrl,\n        timestamp: new Date().toISOString()\n      };\n\n      // 保存到localStorage\n      localStorage.setItem('currentWallpaperInfo', JSON.stringify(wallpaperInfo));\n      console.log('当前壁纸信息已保存:', wallpaperInfo);\n    } catch (error) {\n      console.error('保存壁纸路径失败:', error);\n    }\n  }\n\n  // 获取当前壁纸路径\n  getCurrentWallpaperPath() {\n    try {\n      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo');\n      if (wallpaperInfo) {\n        const info = JSON.parse(wallpaperInfo);\n        return info.imagePath;\n      }\n      return null;\n    } catch (error) {\n      console.error('获取壁纸路径失败:', error);\n      return null;\n    }\n  }\n\n  // 获取当前壁纸信息\n  getCurrentWallpaperInfo() {\n    try {\n      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo');\n      if (wallpaperInfo) {\n        return JSON.parse(wallpaperInfo);\n      }\n      return null;\n    } catch (error) {\n      console.error('获取壁纸信息失败:', error);\n      return null;\n    }\n  }\n\n  // 从本地路径创建File对象\n  async loadFileFromPath(filePath) {\n    try {\n      // 通过fetch获取本地文件并转换为File对象\n      const response = await fetch(filePath);\n      if (!response.ok) {\n        throw new Error(`无法加载文件: ${response.status}`);\n      }\n      const blob = await response.blob();\n      const fileName = filePath.split('/').pop();\n      const file = new File([blob], fileName, {\n        type: blob.type\n      });\n      console.log('成功从路径加载文件:', filePath);\n      return file;\n    } catch (error) {\n      console.error('从路径加载文件失败:', error);\n      return null;\n    }\n  }\n}\nexport default ImageGenerationService;", "map": {"version": 3, "names": ["LlmService", "ImageGenerationService", "constructor", "apiBaseUrl", "process", "env", "VUE_APP_API_BASE_URL", "generateWallpaper", "prompt", "taskId", "Date", "now", "llmService", "enhancedPrompt", "generateResponse", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "task_id", "ok", "Error", "status", "data", "json", "localImagePath", "saveImageToLocal", "image_url", "result", "imageUrl", "imagePath", "saveCurrentWallpaperPath", "error", "console", "getFallbackWallpaper", "checkImageLoad", "Promise", "resolve", "img", "Image", "onload", "onerror", "src", "preloadImage", "reject", "generateMultipleWallpapers", "prompts", "options", "concurrent", "results", "i", "length", "batch", "slice", "batchPromises", "map", "index", "batchResults", "allSettled", "push", "value", "filter", "Boolean", "getGenerationHistory", "history", "localStorage", "getItem", "parse", "saveToHistory", "wallpaperData", "unshift", "timestamp", "toISOString", "trimmedHistory", "setItem", "clearHistory", "removeItem", "getStatistics", "totalGenerated", "todayGenerated", "item", "today", "toDateString", "successRate", "includes", "log", "blob", "fileName", "localPath", "file", "File", "type", "saveFileToPublicImages", "formData", "FormData", "append", "path", "warn", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "style", "display", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "downloadError", "wallpaperInfo", "getCurrentWallpaperPath", "info", "getCurrentWallpaperInfo", "loadFileFromPath", "filePath", "split", "pop"], "sources": ["F:/工作/theme/ai-hmi/src/services/ImageGenerationService.js"], "sourcesContent": ["import LlmService from './LlmService'\r\n\r\nclass ImageGenerationService {\r\n  constructor() {\r\n    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'\r\n  }\r\n\r\n  async generateWallpaper(prompt, taskId = null) {\r\n    if (!taskId) {\r\n      taskId = `wallpaper_${Date.now()}`\r\n    }\r\n\r\n    try {\r\n      // 使用LLM增强提示词\r\n      const llmService = new LlmService()\r\n      const enhancedPrompt = await llmService.generateResponse(prompt)\r\n      \r\n      // 调用简化版的文生图接口（用于AI-HMI测试）\r\n      const response = await fetch(`${this.apiBaseUrl}/api/v1/kolors-simple/text-to-image`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          prompt: enhancedPrompt, \r\n          task_id: taskId\r\n        })\r\n      })\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`)\r\n      }\r\n\r\n      const data = await response.json()\r\n\r\n      // 下载并保存图片到本地\r\n      const localImagePath = await this.saveImageToLocal(data.image_url, taskId)\r\n\r\n      const result = {\r\n        imageUrl: data.image_url, // 保存Kolors生成的图片URL\r\n        imagePath: localImagePath, // 保存本地图片路径\r\n        taskId: taskId,\r\n        prompt: enhancedPrompt\r\n      }\r\n\r\n      // 保存当前壁纸路径到全局状态，供动态壁纸生成使用\r\n      this.saveCurrentWallpaperPath(localImagePath, data.image_url)\r\n\r\n      return result\r\n    } catch (error) {\r\n      console.error('壁纸生成失败:', error)\r\n      return this.getFallbackWallpaper()\r\n    }\r\n  }\r\n\r\n  getFallbackWallpaper() {\r\n    return {\r\n      imageUrl: '/images/default-glass-wallpaper.svg',\r\n      taskId: 'fallback',\r\n      prompt: 'default glassmorphism'\r\n    }\r\n  }\r\n\r\n  // 检查图片是否加载成功\r\n  async checkImageLoad(imageUrl) {\r\n    return new Promise((resolve) => {\r\n      const img = new Image()\r\n      img.onload = () => resolve(true)\r\n      img.onerror = () => resolve(false)\r\n      img.src = imageUrl\r\n    })\r\n  }\r\n\r\n  // 预加载图片\r\n  async preloadImage(imageUrl) {\r\n    return new Promise((resolve, reject) => {\r\n      const img = new Image()\r\n      img.onload = () => resolve(img)\r\n      img.onerror = () => reject(new Error('图片加载失败'))\r\n      img.src = imageUrl\r\n    })\r\n  }\r\n\r\n  // 批量生成壁纸\r\n  async generateMultipleWallpapers(prompts, options = {}) {\r\n    const { concurrent = 3 } = options\r\n    const results = []\r\n    \r\n    // 分批处理以避免过多的并发请求\r\n    for (let i = 0; i < prompts.length; i += concurrent) {\r\n      const batch = prompts.slice(i, i + concurrent)\r\n      const batchPromises = batch.map(async (prompt, index) => {\r\n        const taskId = `wallpaper_${Date.now()}_${i + index}`\r\n        return this.generateWallpaper(prompt, taskId)\r\n      })\r\n      \r\n      const batchResults = await Promise.allSettled(batchPromises)\r\n      results.push(...batchResults.map(result => \r\n        result.status === 'fulfilled' ? result.value : null\r\n      ).filter(Boolean))\r\n    }\r\n    \r\n    return results\r\n  }\r\n\r\n  // 获取生成历史\r\n  getGenerationHistory() {\r\n    const history = localStorage.getItem('wallpaper_generation_history')\r\n    return history ? JSON.parse(history) : []\r\n  }\r\n\r\n  // 保存生成历史\r\n  saveToHistory(wallpaperData) {\r\n    const history = this.getGenerationHistory()\r\n    history.unshift({\r\n      ...wallpaperData,\r\n      timestamp: new Date().toISOString()\r\n    })\r\n    \r\n    // 只保留最近50条记录\r\n    const trimmedHistory = history.slice(0, 50)\r\n    localStorage.setItem('wallpaper_generation_history', JSON.stringify(trimmedHistory))\r\n  }\r\n\r\n  // 清除历史记录\r\n  clearHistory() {\r\n    localStorage.removeItem('wallpaper_generation_history')\r\n  }\r\n\r\n  // 获取统计信息\r\n  getStatistics() {\r\n    const history = this.getGenerationHistory()\r\n    return {\r\n      totalGenerated: history.length,\r\n      todayGenerated: history.filter(item => {\r\n        const today = new Date().toDateString()\r\n        return new Date(item.timestamp).toDateString() === today\r\n      }).length,\r\n      successRate: history.filter(item => item.imageUrl && !item.imageUrl.includes('default')).length / history.length * 100\r\n    }\r\n  }\r\n\r\n  // 保存图片到本地\r\n  async saveImageToLocal(imageUrl, taskId) {\r\n    try {\r\n      console.log('开始保存图片到本地:', imageUrl)\r\n\r\n      // 下载图片\r\n      const response = await fetch(imageUrl)\r\n      if (!response.ok) {\r\n        throw new Error(`下载图片失败: ${response.status}`)\r\n      }\r\n\r\n      const blob = await response.blob()\r\n\r\n      // 生成本地文件名\r\n      const fileName = `wallpaper_${taskId || Date.now()}.jpg`\r\n      const localPath = `/images/${fileName}`\r\n\r\n      // 创建本地URL并保存到public/images目录\r\n      const file = new File([blob], fileName, { type: blob.type })\r\n      await this.saveFileToPublicImages(file, fileName)\r\n\r\n      console.log('图片已保存到本地:', localPath)\r\n      return localPath\r\n    } catch (error) {\r\n      console.error('保存图片到本地失败:', error)\r\n      return null\r\n    }\r\n  }\r\n\r\n  // 保存文件到public/images目录\r\n  async saveFileToPublicImages(file, fileName) {\r\n    try {\r\n      // 使用FormData上传文件到后端保存\r\n      const formData = new FormData()\r\n      formData.append('file', file)\r\n      formData.append('fileName', fileName)\r\n\r\n      const response = await fetch(`${process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'}/api/save-image`, {\r\n        method: 'POST',\r\n        body: formData\r\n      })\r\n\r\n      if (response.ok) {\r\n        const result = await response.json()\r\n        console.log('文件已保存到服务器:', result.path)\r\n        return result.path\r\n      } else {\r\n        // 如果后端保存失败，回退到浏览器下载方式\r\n        console.warn('后端保存失败，使用浏览器下载方式')\r\n        const url = URL.createObjectURL(file)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = fileName\r\n        link.style.display = 'none'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        URL.revokeObjectURL(url)\r\n\r\n        console.log('文件已通过下载保存:', fileName)\r\n        return `/images/${fileName}`\r\n      }\r\n    } catch (error) {\r\n      console.error('保存文件失败:', error)\r\n      // 回退到浏览器下载方式\r\n      try {\r\n        const url = URL.createObjectURL(file)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = fileName\r\n        link.style.display = 'none'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        URL.revokeObjectURL(url)\r\n\r\n        console.log('文件已通过下载保存:', fileName)\r\n        return `/images/${fileName}`\r\n      } catch (downloadError) {\r\n        console.error('下载保存也失败:', downloadError)\r\n        throw error\r\n      }\r\n    }\r\n  }\r\n\r\n  // 保存当前壁纸路径\r\n  saveCurrentWallpaperPath(imagePath, imageUrl) {\r\n    try {\r\n      const wallpaperInfo = {\r\n        imagePath: imagePath,\r\n        imageUrl: imageUrl,\r\n        timestamp: new Date().toISOString()\r\n      }\r\n\r\n      // 保存到localStorage\r\n      localStorage.setItem('currentWallpaperInfo', JSON.stringify(wallpaperInfo))\r\n\r\n      console.log('当前壁纸信息已保存:', wallpaperInfo)\r\n    } catch (error) {\r\n      console.error('保存壁纸路径失败:', error)\r\n    }\r\n  }\r\n\r\n  // 获取当前壁纸路径\r\n  getCurrentWallpaperPath() {\r\n    try {\r\n      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo')\r\n      if (wallpaperInfo) {\r\n        const info = JSON.parse(wallpaperInfo)\r\n        return info.imagePath\r\n      }\r\n      return null\r\n    } catch (error) {\r\n      console.error('获取壁纸路径失败:', error)\r\n      return null\r\n    }\r\n  }\r\n\r\n  // 获取当前壁纸信息\r\n  getCurrentWallpaperInfo() {\r\n    try {\r\n      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo')\r\n      if (wallpaperInfo) {\r\n        return JSON.parse(wallpaperInfo)\r\n      }\r\n      return null\r\n    } catch (error) {\r\n      console.error('获取壁纸信息失败:', error)\r\n      return null\r\n    }\r\n  }\r\n\r\n  // 从本地路径创建File对象\r\n  async loadFileFromPath(filePath) {\r\n    try {\r\n      // 通过fetch获取本地文件并转换为File对象\r\n      const response = await fetch(filePath)\r\n      if (!response.ok) {\r\n        throw new Error(`无法加载文件: ${response.status}`)\r\n      }\r\n\r\n      const blob = await response.blob()\r\n      const fileName = filePath.split('/').pop()\r\n      const file = new File([blob], fileName, { type: blob.type })\r\n\r\n      console.log('成功从路径加载文件:', filePath)\r\n      return file\r\n    } catch (error) {\r\n      console.error('从路径加载文件失败:', error)\r\n      return null\r\n    }\r\n  }\r\n}\r\n\r\nexport default ImageGenerationService"], "mappings": ";;;;;;;AAAA,OAAOA,UAAU,MAAM,cAAc;AAErC,MAAMC,sBAAsB,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB;EAC/E;EAEA,MAAMC,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,GAAG,IAAI,EAAE;IAC7C,IAAI,CAACA,MAAM,EAAE;MACXA,MAAM,GAAG,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IACpC;IAEA,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,IAAIZ,UAAU,CAAC,CAAC;MACnC,MAAMa,cAAc,GAAG,MAAMD,UAAU,CAACE,gBAAgB,CAACN,MAAM,CAAC;;MAEhE;MACA,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACb,UAAU,qCAAqC,EAAE;QACpFc,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBb,MAAM,EAAEK,cAAc;UACtBS,OAAO,EAAEb;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACM,QAAQ,CAACQ,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBT,QAAQ,CAACU,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMC,cAAc,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACH,IAAI,CAACI,SAAS,EAAErB,MAAM,CAAC;MAE1E,MAAMsB,MAAM,GAAG;QACbC,QAAQ,EAAEN,IAAI,CAACI,SAAS;QAAE;QAC1BG,SAAS,EAAEL,cAAc;QAAE;QAC3BnB,MAAM,EAAEA,MAAM;QACdD,MAAM,EAAEK;MACV,CAAC;;MAED;MACA,IAAI,CAACqB,wBAAwB,CAACN,cAAc,EAAEF,IAAI,CAACI,SAAS,CAAC;MAE7D,OAAOC,MAAM;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,OAAO,IAAI,CAACE,oBAAoB,CAAC,CAAC;IACpC;EACF;EAEAA,oBAAoBA,CAAA,EAAG;IACrB,OAAO;MACLL,QAAQ,EAAE,qCAAqC;MAC/CvB,MAAM,EAAE,UAAU;MAClBD,MAAM,EAAE;IACV,CAAC;EACH;;EAEA;EACA,MAAM8B,cAAcA,CAACN,QAAQ,EAAE;IAC7B,OAAO,IAAIO,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAMH,OAAO,CAAC,IAAI,CAAC;MAChCC,GAAG,CAACG,OAAO,GAAG,MAAMJ,OAAO,CAAC,KAAK,CAAC;MAClCC,GAAG,CAACI,GAAG,GAAGb,QAAQ;IACpB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMc,YAAYA,CAACd,QAAQ,EAAE;IAC3B,OAAO,IAAIO,OAAO,CAAC,CAACC,OAAO,EAAEO,MAAM,KAAK;MACtC,MAAMN,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAMH,OAAO,CAACC,GAAG,CAAC;MAC/BA,GAAG,CAACG,OAAO,GAAG,MAAMG,MAAM,CAAC,IAAIvB,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC/CiB,GAAG,CAACI,GAAG,GAAGb,QAAQ;IACpB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMgB,0BAA0BA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtD,MAAM;MAAEC,UAAU,GAAG;IAAE,CAAC,GAAGD,OAAO;IAClC,MAAME,OAAO,GAAG,EAAE;;IAElB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAED,CAAC,IAAIF,UAAU,EAAE;MACnD,MAAMI,KAAK,GAAGN,OAAO,CAACO,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAAC;MAC9C,MAAMM,aAAa,GAAGF,KAAK,CAACG,GAAG,CAAC,OAAOlD,MAAM,EAAEmD,KAAK,KAAK;QACvD,MAAMlD,MAAM,GAAG,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI0C,CAAC,GAAGM,KAAK,EAAE;QACrD,OAAO,IAAI,CAACpD,iBAAiB,CAACC,MAAM,EAAEC,MAAM,CAAC;MAC/C,CAAC,CAAC;MAEF,MAAMmD,YAAY,GAAG,MAAMrB,OAAO,CAACsB,UAAU,CAACJ,aAAa,CAAC;MAC5DL,OAAO,CAACU,IAAI,CAAC,GAAGF,YAAY,CAACF,GAAG,CAAC3B,MAAM,IACrCA,MAAM,CAACN,MAAM,KAAK,WAAW,GAAGM,MAAM,CAACgC,KAAK,GAAG,IACjD,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC;IACpB;IAEA,OAAOb,OAAO;EAChB;;EAEA;EACAc,oBAAoBA,CAAA,EAAG;IACrB,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC;IACpE,OAAOF,OAAO,GAAG/C,IAAI,CAACkD,KAAK,CAACH,OAAO,CAAC,GAAG,EAAE;EAC3C;;EAEA;EACAI,aAAaA,CAACC,aAAa,EAAE;IAC3B,MAAML,OAAO,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;IAC3CC,OAAO,CAACM,OAAO,CAAC;MACd,GAAGD,aAAa;MAChBE,SAAS,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACiE,WAAW,CAAC;IACpC,CAAC,CAAC;;IAEF;IACA,MAAMC,cAAc,GAAGT,OAAO,CAACX,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3CY,YAAY,CAACS,OAAO,CAAC,8BAA8B,EAAEzD,IAAI,CAACC,SAAS,CAACuD,cAAc,CAAC,CAAC;EACtF;;EAEA;EACAE,YAAYA,CAAA,EAAG;IACbV,YAAY,CAACW,UAAU,CAAC,8BAA8B,CAAC;EACzD;;EAEA;EACAC,aAAaA,CAAA,EAAG;IACd,MAAMb,OAAO,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;IAC3C,OAAO;MACLe,cAAc,EAAEd,OAAO,CAACb,MAAM;MAC9B4B,cAAc,EAAEf,OAAO,CAACH,MAAM,CAACmB,IAAI,IAAI;QACrC,MAAMC,KAAK,GAAG,IAAI1E,IAAI,CAAC,CAAC,CAAC2E,YAAY,CAAC,CAAC;QACvC,OAAO,IAAI3E,IAAI,CAACyE,IAAI,CAACT,SAAS,CAAC,CAACW,YAAY,CAAC,CAAC,KAAKD,KAAK;MAC1D,CAAC,CAAC,CAAC9B,MAAM;MACTgC,WAAW,EAAEnB,OAAO,CAACH,MAAM,CAACmB,IAAI,IAAIA,IAAI,CAACnD,QAAQ,IAAI,CAACmD,IAAI,CAACnD,QAAQ,CAACuD,QAAQ,CAAC,SAAS,CAAC,CAAC,CAACjC,MAAM,GAAGa,OAAO,CAACb,MAAM,GAAG;IACrH,CAAC;EACH;;EAEA;EACA,MAAMzB,gBAAgBA,CAACG,QAAQ,EAAEvB,MAAM,EAAE;IACvC,IAAI;MACF2B,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAExD,QAAQ,CAAC;;MAEnC;MACA,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAACgB,QAAQ,CAAC;MACtC,IAAI,CAACjB,QAAQ,CAACQ,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,WAAWT,QAAQ,CAACU,MAAM,EAAE,CAAC;MAC/C;MAEA,MAAMgE,IAAI,GAAG,MAAM1E,QAAQ,CAAC0E,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMC,QAAQ,GAAG,aAAajF,MAAM,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MACxD,MAAMgF,SAAS,GAAG,WAAWD,QAAQ,EAAE;;MAEvC;MACA,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAEC,QAAQ,EAAE;QAAEI,IAAI,EAAEL,IAAI,CAACK;MAAK,CAAC,CAAC;MAC5D,MAAM,IAAI,CAACC,sBAAsB,CAACH,IAAI,EAAEF,QAAQ,CAAC;MAEjDtD,OAAO,CAACoD,GAAG,CAAC,WAAW,EAAEG,SAAS,CAAC;MACnC,OAAOA,SAAS;IAClB,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAM4D,sBAAsBA,CAACH,IAAI,EAAEF,QAAQ,EAAE;IAC3C,IAAI;MACF;MACA,MAAMM,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MAC7BI,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAER,QAAQ,CAAC;MAErC,MAAM3E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB,iBAAiB,EAAE;QAC5GW,MAAM,EAAE,MAAM;QACdE,IAAI,EAAE6E;MACR,CAAC,CAAC;MAEF,IAAIjF,QAAQ,CAACQ,EAAE,EAAE;QACf,MAAMQ,MAAM,GAAG,MAAMhB,QAAQ,CAACY,IAAI,CAAC,CAAC;QACpCS,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAEzD,MAAM,CAACoE,IAAI,CAAC;QACtC,OAAOpE,MAAM,CAACoE,IAAI;MACpB,CAAC,MAAM;QACL;QACA/D,OAAO,CAACgE,IAAI,CAAC,kBAAkB,CAAC;QAChC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC;QACrC,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;QACfG,IAAI,CAACI,QAAQ,GAAGlB,QAAQ;QACxBc,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACtF,IAAI,CAAC4F,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACtF,IAAI,CAAC8F,WAAW,CAACT,IAAI,CAAC;QAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;QAExBjE,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAEE,QAAQ,CAAC;QACnC,OAAO,WAAWA,QAAQ,EAAE;MAC9B;IACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B;MACA,IAAI;QACF,MAAMkE,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC;QACrC,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;QACfG,IAAI,CAACI,QAAQ,GAAGlB,QAAQ;QACxBc,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACtF,IAAI,CAAC4F,WAAW,CAACP,IAAI,CAAC;QAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;QACZP,QAAQ,CAACtF,IAAI,CAAC8F,WAAW,CAACT,IAAI,CAAC;QAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;QAExBjE,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAEE,QAAQ,CAAC;QACnC,OAAO,WAAWA,QAAQ,EAAE;MAC9B,CAAC,CAAC,OAAOyB,aAAa,EAAE;QACtB/E,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEgF,aAAa,CAAC;QACxC,MAAMhF,KAAK;MACb;IACF;EACF;;EAEA;EACAD,wBAAwBA,CAACD,SAAS,EAAED,QAAQ,EAAE;IAC5C,IAAI;MACF,MAAMoF,aAAa,GAAG;QACpBnF,SAAS,EAAEA,SAAS;QACpBD,QAAQ,EAAEA,QAAQ;QAClB0C,SAAS,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACiE,WAAW,CAAC;MACpC,CAAC;;MAED;MACAP,YAAY,CAACS,OAAO,CAAC,sBAAsB,EAAEzD,IAAI,CAACC,SAAS,CAAC+F,aAAa,CAAC,CAAC;MAE3EhF,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAE4B,aAAa,CAAC;IAC1C,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF;;EAEA;EACAkF,uBAAuBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAMD,aAAa,GAAGhD,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAClE,IAAI+C,aAAa,EAAE;QACjB,MAAME,IAAI,GAAGlG,IAAI,CAACkD,KAAK,CAAC8C,aAAa,CAAC;QACtC,OAAOE,IAAI,CAACrF,SAAS;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,IAAI;IACb;EACF;;EAEA;EACAoF,uBAAuBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAMH,aAAa,GAAGhD,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAClE,IAAI+C,aAAa,EAAE;QACjB,OAAOhG,IAAI,CAACkD,KAAK,CAAC8C,aAAa,CAAC;MAClC;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMqF,gBAAgBA,CAACC,QAAQ,EAAE;IAC/B,IAAI;MACF;MACA,MAAM1G,QAAQ,GAAG,MAAMC,KAAK,CAACyG,QAAQ,CAAC;MACtC,IAAI,CAAC1G,QAAQ,CAACQ,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,WAAWT,QAAQ,CAACU,MAAM,EAAE,CAAC;MAC/C;MAEA,MAAMgE,IAAI,GAAG,MAAM1E,QAAQ,CAAC0E,IAAI,CAAC,CAAC;MAClC,MAAMC,QAAQ,GAAG+B,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC1C,MAAM/B,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAEC,QAAQ,EAAE;QAAEI,IAAI,EAAEL,IAAI,CAACK;MAAK,CAAC,CAAC;MAE5D1D,OAAO,CAACoD,GAAG,CAAC,YAAY,EAAEiC,QAAQ,CAAC;MACnC,OAAO7B,IAAI;IACb,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,IAAI;IACb;EACF;AACF;AAEA,eAAelC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}