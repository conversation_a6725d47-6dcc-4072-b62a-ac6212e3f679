{"ast": null, "code": "import { defineStore } from 'pinia';\nexport const useVpaStore = defineStore('vpa', {\n  state: () => ({\n    // VPA当前状态\n    currentMode: 'companion',\n    // companion(陪伴模式) | interaction(交互模式) | hidden(隐藏)\n\n    // VPA可用模式\n    availableModes: {\n      companion: {\n        name: '陪伴模式',\n        description: 'VPA以小窗形式陪伴用户',\n        component: 'VPAAvatarWidget',\n        defaultSize: 'vpaWidget',\n        // 2x2\n        animation: 'idle',\n        position: {\n          x: 15,\n          y: 7\n        }\n      },\n      interaction: {\n        name: '交互模式',\n        description: 'VPA展开为交互面板',\n        component: 'VPAInteractionPanel',\n        defaultSize: 'vpaInteractionLarge',\n        // 8x9\n        animation: 'talking',\n        position: {\n          x: 9,\n          y: 1\n        }\n      },\n      hidden: {\n        name: '隐藏模式',\n        description: 'VPA完全隐藏',\n        component: null,\n        defaultSize: null,\n        animation: null,\n        position: null\n      }\n    },\n    // VPA动画状态\n    animationState: 'idle',\n    // idle | talking | listening | thinking | greeting | sleeping\n\n    // VPA可用动画\n    availableAnimations: {\n      idle: {\n        name: '待机',\n        resource: 'vpa2.gif',\n        duration: 3000,\n        loop: true\n      },\n      talking: {\n        name: '说话',\n        resource: 'vpn1.gif',\n        duration: 2000,\n        loop: true\n      },\n      listening: {\n        name: '聆听',\n        resource: 'vpa2.gif',\n        duration: 1500,\n        loop: true\n      },\n      thinking: {\n        name: '思考',\n        resource: 'vpa2.gif',\n        duration: 2500,\n        loop: true\n      },\n      greeting: {\n        name: '问候',\n        resource: 'vpn1.gif',\n        duration: 1000,\n        loop: false\n      },\n      sleeping: {\n        name: '休眠',\n        resource: 'vpa2.gif',\n        duration: 5000,\n        loop: true\n      }\n    },\n    // VPA个性化设置\n    personality: {\n      name: 'AI助手',\n      voice: 'female',\n      language: 'zh-CN',\n      responseStyle: 'friendly',\n      // friendly | professional | casual\n      emotionalLevel: 'medium' // low | medium | high\n    },\n    // VPA交互历史\n    interactionHistory: [],\n    // VPA当前对话状态\n    conversationState: {\n      isActive: false,\n      currentTopic: null,\n      contextLevel: 0,\n      // 0-5, 上下文理解深度\n      lastInteraction: null\n    },\n    // VPA能力状态\n    capabilities: {\n      voiceRecognition: true,\n      textToSpeech: true,\n      emotionRecognition: false,\n      gestureRecognition: false,\n      contextAwareness: true\n    },\n    // VPA显示配置\n    displayConfig: {\n      showName: true,\n      showStatus: true,\n      showAnimation: true,\n      transparency: 0.95,\n      blurEffect: true\n    }\n  }),\n  getters: {\n    // 获取当前VPA模式配置\n    currentModeConfig: state => {\n      return state.availableModes[state.currentMode];\n    },\n    // 获取当前动画配置\n    currentAnimationConfig: state => {\n      return state.availableAnimations[state.animationState];\n    },\n    // 检查VPA是否可见\n    isVisible: state => {\n      return state.currentMode !== 'hidden';\n    },\n    // 检查VPA是否在交互中\n    isInteracting: state => {\n      return state.conversationState.isActive;\n    },\n    // 获取VPA组件信息\n    vpaComponentInfo: state => {\n      const modeConfig = state.availableModes[state.currentMode];\n      const animationConfig = state.availableAnimations[state.animationState];\n      return {\n        component: modeConfig?.component,\n        size: modeConfig?.defaultSize,\n        position: modeConfig?.position,\n        animation: animationConfig,\n        displayConfig: state.displayConfig\n      };\n    }\n  },\n  actions: {\n    // 切换VPA模式\n    switchMode(mode) {\n      if (this.availableModes[mode]) {\n        const previousMode = this.currentMode;\n        this.currentMode = mode;\n\n        // 根据模式切换动画\n        if (mode === 'interaction') {\n          this.setAnimation('talking');\n        } else if (mode === 'companion') {\n          this.setAnimation('idle');\n        }\n        console.log(`VPA模式已切换: ${previousMode} -> ${mode}`);\n\n        // 记录交互历史\n        this.addInteractionHistory({\n          type: 'mode_switch',\n          from: previousMode,\n          to: mode,\n          timestamp: new Date()\n        });\n      }\n    },\n    // 设置VPA动画\n    setAnimation(animationName) {\n      if (this.availableAnimations[animationName]) {\n        this.animationState = animationName;\n        console.log(`VPA动画已切换到: ${animationName}`);\n      }\n    },\n    // 开始对话\n    startConversation(topic = null) {\n      this.conversationState.isActive = true;\n      this.conversationState.currentTopic = topic;\n      this.conversationState.lastInteraction = new Date();\n\n      // 切换到交互模式\n      if (this.currentMode === 'companion') {\n        this.switchMode('interaction');\n      }\n      this.setAnimation('listening');\n    },\n    // 结束对话\n    endConversation() {\n      this.conversationState.isActive = false;\n      this.conversationState.currentTopic = null;\n\n      // 切换回陪伴模式\n      if (this.currentMode === 'interaction') {\n        this.switchMode('companion');\n      }\n      this.setAnimation('idle');\n    },\n    // 添加交互历史\n    addInteractionHistory(interaction) {\n      this.interactionHistory.unshift(interaction);\n\n      // 保持历史记录在合理范围内\n      if (this.interactionHistory.length > 100) {\n        this.interactionHistory = this.interactionHistory.slice(0, 100);\n      }\n    },\n    // 更新个性化设置\n    updatePersonality(settings) {\n      this.personality = {\n        ...this.personality,\n        ...settings\n      };\n    },\n    // 更新显示配置\n    updateDisplayConfig(config) {\n      this.displayConfig = {\n        ...this.displayConfig,\n        ...config\n      };\n    },\n    // VPA问候\n    greet() {\n      this.setAnimation('greeting');\n\n      // 2秒后回到待机状态\n      setTimeout(() => {\n        this.setAnimation('idle');\n      }, 2000);\n      this.addInteractionHistory({\n        type: 'greeting',\n        timestamp: new Date()\n      });\n    },\n    // VPA休眠\n    sleep() {\n      this.setAnimation('sleeping');\n      this.conversationState.isActive = false;\n    },\n    // VPA唤醒\n    wakeUp() {\n      this.setAnimation('greeting');\n      setTimeout(() => {\n        this.setAnimation('idle');\n      }, 1000);\n    }\n  }\n});", "map": {"version": 3, "names": ["defineStore", "useVpaStore", "state", "currentMode", "availableModes", "companion", "name", "description", "component", "defaultSize", "animation", "position", "x", "y", "interaction", "hidden", "animationState", "availableAnimations", "idle", "resource", "duration", "loop", "talking", "listening", "thinking", "greeting", "sleeping", "personality", "voice", "language", "responseStyle", "emotionalLevel", "interactionHistory", "conversationState", "isActive", "currentTopic", "contextLevel", "lastInteraction", "capabilities", "voiceRecognition", "textToSpeech", "emotionRecognition", "gestureRecognition", "contextAwareness", "displayConfig", "showName", "showStatus", "showAnimation", "transparency", "blurEffect", "getters", "currentModeConfig", "currentAnimationConfig", "isVisible", "isInteracting", "vpaComponentInfo", "modeConfig", "animationConfig", "size", "actions", "switchMode", "mode", "previousMode", "setAnimation", "console", "log", "addInteractionHistory", "type", "from", "to", "timestamp", "Date", "animationName", "startConversation", "topic", "endConversation", "unshift", "length", "slice", "updatePersonality", "settings", "updateDisplayConfig", "config", "greet", "setTimeout", "sleep", "wakeUp"], "sources": ["F:/工作/theme/ai-hmi/src/store/modules/vpa.js"], "sourcesContent": ["import { defineStore } from 'pinia'\n\nexport const useVpaStore = defineStore('vpa', {\n  state: () => ({\n    // VPA当前状态\n    currentMode: 'companion', // companion(陪伴模式) | interaction(交互模式) | hidden(隐藏)\n    \n    // VPA可用模式\n    availableModes: {\n      companion: {\n        name: '陪伴模式',\n        description: 'VPA以小窗形式陪伴用户',\n        component: 'VPAAvatarWidget',\n        defaultSize: 'vpaWidget', // 2x2\n        animation: 'idle',\n        position: { x: 15, y: 7 }\n      },\n      interaction: {\n        name: '交互模式',\n        description: 'VPA展开为交互面板',\n        component: 'VPAInteractionPanel',\n        defaultSize: 'vpaInteractionLarge', // 8x9\n        animation: 'talking',\n        position: { x: 9, y: 1 }\n      },\n      hidden: {\n        name: '隐藏模式',\n        description: 'VPA完全隐藏',\n        component: null,\n        defaultSize: null,\n        animation: null,\n        position: null\n      }\n    },\n    \n    // VPA动画状态\n    animationState: 'idle', // idle | talking | listening | thinking | greeting | sleeping\n    \n    // VPA可用动画\n    availableAnimations: {\n      idle: {\n        name: '待机',\n        resource: 'vpa2.gif',\n        duration: 3000,\n        loop: true\n      },\n      talking: {\n        name: '说话',\n        resource: 'vpn1.gif',\n        duration: 2000,\n        loop: true\n      },\n      listening: {\n        name: '聆听',\n        resource: 'vpa2.gif',\n        duration: 1500,\n        loop: true\n      },\n      thinking: {\n        name: '思考',\n        resource: 'vpa2.gif',\n        duration: 2500,\n        loop: true\n      },\n      greeting: {\n        name: '问候',\n        resource: 'vpn1.gif',\n        duration: 1000,\n        loop: false\n      },\n      sleeping: {\n        name: '休眠',\n        resource: 'vpa2.gif',\n        duration: 5000,\n        loop: true\n      }\n    },\n    \n    // VPA个性化设置\n    personality: {\n      name: 'AI助手',\n      voice: 'female',\n      language: 'zh-CN',\n      responseStyle: 'friendly', // friendly | professional | casual\n      emotionalLevel: 'medium' // low | medium | high\n    },\n    \n    // VPA交互历史\n    interactionHistory: [],\n    \n    // VPA当前对话状态\n    conversationState: {\n      isActive: false,\n      currentTopic: null,\n      contextLevel: 0, // 0-5, 上下文理解深度\n      lastInteraction: null\n    },\n    \n    // VPA能力状态\n    capabilities: {\n      voiceRecognition: true,\n      textToSpeech: true,\n      emotionRecognition: false,\n      gestureRecognition: false,\n      contextAwareness: true\n    },\n    \n    // VPA显示配置\n    displayConfig: {\n      showName: true,\n      showStatus: true,\n      showAnimation: true,\n      transparency: 0.95,\n      blurEffect: true\n    }\n  }),\n  \n  getters: {\n    // 获取当前VPA模式配置\n    currentModeConfig: (state) => {\n      return state.availableModes[state.currentMode]\n    },\n    \n    // 获取当前动画配置\n    currentAnimationConfig: (state) => {\n      return state.availableAnimations[state.animationState]\n    },\n    \n    // 检查VPA是否可见\n    isVisible: (state) => {\n      return state.currentMode !== 'hidden'\n    },\n    \n    // 检查VPA是否在交互中\n    isInteracting: (state) => {\n      return state.conversationState.isActive\n    },\n    \n    // 获取VPA组件信息\n    vpaComponentInfo: (state) => {\n      const modeConfig = state.availableModes[state.currentMode]\n      const animationConfig = state.availableAnimations[state.animationState]\n      \n      return {\n        component: modeConfig?.component,\n        size: modeConfig?.defaultSize,\n        position: modeConfig?.position,\n        animation: animationConfig,\n        displayConfig: state.displayConfig\n      }\n    }\n  },\n  \n  actions: {\n    // 切换VPA模式\n    switchMode(mode) {\n      if (this.availableModes[mode]) {\n        const previousMode = this.currentMode\n        this.currentMode = mode\n        \n        // 根据模式切换动画\n        if (mode === 'interaction') {\n          this.setAnimation('talking')\n        } else if (mode === 'companion') {\n          this.setAnimation('idle')\n        }\n        \n        console.log(`VPA模式已切换: ${previousMode} -> ${mode}`)\n        \n        // 记录交互历史\n        this.addInteractionHistory({\n          type: 'mode_switch',\n          from: previousMode,\n          to: mode,\n          timestamp: new Date()\n        })\n      }\n    },\n    \n    // 设置VPA动画\n    setAnimation(animationName) {\n      if (this.availableAnimations[animationName]) {\n        this.animationState = animationName\n        console.log(`VPA动画已切换到: ${animationName}`)\n      }\n    },\n    \n    // 开始对话\n    startConversation(topic = null) {\n      this.conversationState.isActive = true\n      this.conversationState.currentTopic = topic\n      this.conversationState.lastInteraction = new Date()\n      \n      // 切换到交互模式\n      if (this.currentMode === 'companion') {\n        this.switchMode('interaction')\n      }\n      \n      this.setAnimation('listening')\n    },\n    \n    // 结束对话\n    endConversation() {\n      this.conversationState.isActive = false\n      this.conversationState.currentTopic = null\n      \n      // 切换回陪伴模式\n      if (this.currentMode === 'interaction') {\n        this.switchMode('companion')\n      }\n      \n      this.setAnimation('idle')\n    },\n    \n    // 添加交互历史\n    addInteractionHistory(interaction) {\n      this.interactionHistory.unshift(interaction)\n      \n      // 保持历史记录在合理范围内\n      if (this.interactionHistory.length > 100) {\n        this.interactionHistory = this.interactionHistory.slice(0, 100)\n      }\n    },\n    \n    // 更新个性化设置\n    updatePersonality(settings) {\n      this.personality = { ...this.personality, ...settings }\n    },\n    \n    // 更新显示配置\n    updateDisplayConfig(config) {\n      this.displayConfig = { ...this.displayConfig, ...config }\n    },\n    \n    // VPA问候\n    greet() {\n      this.setAnimation('greeting')\n      \n      // 2秒后回到待机状态\n      setTimeout(() => {\n        this.setAnimation('idle')\n      }, 2000)\n      \n      this.addInteractionHistory({\n        type: 'greeting',\n        timestamp: new Date()\n      })\n    },\n    \n    // VPA休眠\n    sleep() {\n      this.setAnimation('sleeping')\n      this.conversationState.isActive = false\n    },\n    \n    // VPA唤醒\n    wakeUp() {\n      this.setAnimation('greeting')\n      \n      setTimeout(() => {\n        this.setAnimation('idle')\n      }, 1000)\n    }\n  }\n})\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AAEnC,OAAO,MAAMC,WAAW,GAAGD,WAAW,CAAC,KAAK,EAAE;EAC5CE,KAAK,EAAEA,CAAA,MAAO;IACZ;IACAC,WAAW,EAAE,WAAW;IAAE;;IAE1B;IACAC,cAAc,EAAE;MACdC,SAAS,EAAE;QACTC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE,iBAAiB;QAC5BC,WAAW,EAAE,WAAW;QAAE;QAC1BC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;MAC1B,CAAC;MACDC,WAAW,EAAE;QACXR,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,qBAAqB;QAChCC,WAAW,EAAE,qBAAqB;QAAE;QACpCC,SAAS,EAAE,SAAS;QACpBC,QAAQ,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE;MACzB,CAAC;MACDE,MAAM,EAAE;QACNT,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IAED;IACAK,cAAc,EAAE,MAAM;IAAE;;IAExB;IACAC,mBAAmB,EAAE;MACnBC,IAAI,EAAE;QACJZ,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACPhB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDE,SAAS,EAAE;QACTjB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDG,QAAQ,EAAE;QACRlB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDI,QAAQ,EAAE;QACRnB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDK,QAAQ,EAAE;QACRpB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR;IACF,CAAC;IAED;IACAM,WAAW,EAAE;MACXrB,IAAI,EAAE,MAAM;MACZsB,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,OAAO;MACjBC,aAAa,EAAE,UAAU;MAAE;MAC3BC,cAAc,EAAE,QAAQ,CAAC;IAC3B,CAAC;IAED;IACAC,kBAAkB,EAAE,EAAE;IAEtB;IACAC,iBAAiB,EAAE;MACjBC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,CAAC;MAAE;MACjBC,eAAe,EAAE;IACnB,CAAC;IAED;IACAC,YAAY,EAAE;MACZC,gBAAgB,EAAE,IAAI;MACtBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;IACpB,CAAC;IAED;IACAC,aAAa,EAAE;MACbC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EAEFC,OAAO,EAAE;IACP;IACAC,iBAAiB,EAAGjD,KAAK,IAAK;MAC5B,OAAOA,KAAK,CAACE,cAAc,CAACF,KAAK,CAACC,WAAW,CAAC;IAChD,CAAC;IAED;IACAiD,sBAAsB,EAAGlD,KAAK,IAAK;MACjC,OAAOA,KAAK,CAACe,mBAAmB,CAACf,KAAK,CAACc,cAAc,CAAC;IACxD,CAAC;IAED;IACAqC,SAAS,EAAGnD,KAAK,IAAK;MACpB,OAAOA,KAAK,CAACC,WAAW,KAAK,QAAQ;IACvC,CAAC;IAED;IACAmD,aAAa,EAAGpD,KAAK,IAAK;MACxB,OAAOA,KAAK,CAAC+B,iBAAiB,CAACC,QAAQ;IACzC,CAAC;IAED;IACAqB,gBAAgB,EAAGrD,KAAK,IAAK;MAC3B,MAAMsD,UAAU,GAAGtD,KAAK,CAACE,cAAc,CAACF,KAAK,CAACC,WAAW,CAAC;MAC1D,MAAMsD,eAAe,GAAGvD,KAAK,CAACe,mBAAmB,CAACf,KAAK,CAACc,cAAc,CAAC;MAEvE,OAAO;QACLR,SAAS,EAAEgD,UAAU,EAAEhD,SAAS;QAChCkD,IAAI,EAAEF,UAAU,EAAE/C,WAAW;QAC7BE,QAAQ,EAAE6C,UAAU,EAAE7C,QAAQ;QAC9BD,SAAS,EAAE+C,eAAe;QAC1Bb,aAAa,EAAE1C,KAAK,CAAC0C;MACvB,CAAC;IACH;EACF,CAAC;EAEDe,OAAO,EAAE;IACP;IACAC,UAAUA,CAACC,IAAI,EAAE;MACf,IAAI,IAAI,CAACzD,cAAc,CAACyD,IAAI,CAAC,EAAE;QAC7B,MAAMC,YAAY,GAAG,IAAI,CAAC3D,WAAW;QACrC,IAAI,CAACA,WAAW,GAAG0D,IAAI;;QAEvB;QACA,IAAIA,IAAI,KAAK,aAAa,EAAE;UAC1B,IAAI,CAACE,YAAY,CAAC,SAAS,CAAC;QAC9B,CAAC,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;UAC/B,IAAI,CAACE,YAAY,CAAC,MAAM,CAAC;QAC3B;QAEAC,OAAO,CAACC,GAAG,CAAC,aAAaH,YAAY,OAAOD,IAAI,EAAE,CAAC;;QAEnD;QACA,IAAI,CAACK,qBAAqB,CAAC;UACzBC,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAEN,YAAY;UAClBO,EAAE,EAAER,IAAI;UACRS,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAR,YAAYA,CAACS,aAAa,EAAE;MAC1B,IAAI,IAAI,CAACvD,mBAAmB,CAACuD,aAAa,CAAC,EAAE;QAC3C,IAAI,CAACxD,cAAc,GAAGwD,aAAa;QACnCR,OAAO,CAACC,GAAG,CAAC,cAAcO,aAAa,EAAE,CAAC;MAC5C;IACF,CAAC;IAED;IACAC,iBAAiBA,CAACC,KAAK,GAAG,IAAI,EAAE;MAC9B,IAAI,CAACzC,iBAAiB,CAACC,QAAQ,GAAG,IAAI;MACtC,IAAI,CAACD,iBAAiB,CAACE,YAAY,GAAGuC,KAAK;MAC3C,IAAI,CAACzC,iBAAiB,CAACI,eAAe,GAAG,IAAIkC,IAAI,CAAC,CAAC;;MAEnD;MACA,IAAI,IAAI,CAACpE,WAAW,KAAK,WAAW,EAAE;QACpC,IAAI,CAACyD,UAAU,CAAC,aAAa,CAAC;MAChC;MAEA,IAAI,CAACG,YAAY,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;IACAY,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC1C,iBAAiB,CAACC,QAAQ,GAAG,KAAK;MACvC,IAAI,CAACD,iBAAiB,CAACE,YAAY,GAAG,IAAI;;MAE1C;MACA,IAAI,IAAI,CAAChC,WAAW,KAAK,aAAa,EAAE;QACtC,IAAI,CAACyD,UAAU,CAAC,WAAW,CAAC;MAC9B;MAEA,IAAI,CAACG,YAAY,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED;IACAG,qBAAqBA,CAACpD,WAAW,EAAE;MACjC,IAAI,CAACkB,kBAAkB,CAAC4C,OAAO,CAAC9D,WAAW,CAAC;;MAE5C;MACA,IAAI,IAAI,CAACkB,kBAAkB,CAAC6C,MAAM,GAAG,GAAG,EAAE;QACxC,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8C,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;MACjE;IACF,CAAC;IAED;IACAC,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B,IAAI,CAACrD,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAGqD;MAAS,CAAC;IACzD,CAAC;IAED;IACAC,mBAAmBA,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACtC,aAAa,GAAG;QAAE,GAAG,IAAI,CAACA,aAAa;QAAE,GAAGsC;MAAO,CAAC;IAC3D,CAAC;IAED;IACAC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACpB,YAAY,CAAC,UAAU,CAAC;;MAE7B;MACAqB,UAAU,CAAC,MAAM;QACf,IAAI,CAACrB,YAAY,CAAC,MAAM,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;MAER,IAAI,CAACG,qBAAqB,CAAC;QACzBC,IAAI,EAAE,UAAU;QAChBG,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACAc,KAAKA,CAAA,EAAG;MACN,IAAI,CAACtB,YAAY,CAAC,UAAU,CAAC;MAC7B,IAAI,CAAC9B,iBAAiB,CAACC,QAAQ,GAAG,KAAK;IACzC,CAAC;IAED;IACAoD,MAAMA,CAAA,EAAG;MACP,IAAI,CAACvB,YAAY,CAAC,UAAU,CAAC;MAE7BqB,UAAU,CAAC,MAAM;QACf,IAAI,CAACrB,YAAY,CAAC,MAAM,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}