{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// 布局管理Store - 16x9网格系统和组件布局\nimport { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\nexport const useLayoutStore = defineStore('layout', () => {\n  // === 状态 ===\n  const gridColumns = ref(16); // 网格列数 - 升级到16x9标准\n  const gridRows = ref(9); // 网格行数 - 升级到16x9标准\n  const gridGap = ref(16); // 网格间距\n\n  // 桌面组件布局配置 - 基于16x9网格\n  const desktopLayout = ref([{\n    id: 'dynamic-island',\n    component: 'DynamicIsland',\n    position: {\n      column: 1,\n      row: 1,\n      colspan: 16,\n      rowspan: 1\n    },\n    zIndex: 20,\n    visible: true\n  }, {\n    id: 'main-content-area',\n    component: 'MainContentArea',\n    position: {\n      column: 1,\n      row: 2,\n      colspan: 16,\n      rowspan: 8\n    },\n    zIndex: 1,\n    visible: true,\n    layout: 'zoned-fusion' // 分区融合式布局\n  }]);\n\n  // 当前活动的对话框\n  const activeDialog = ref(null);\n\n  // 屏幕尺寸\n  const screenSize = ref({\n    width: window.innerWidth,\n    height: window.innerHeight\n  });\n\n  // 当前场景布局模式\n  const currentLayoutMode = ref('family'); // family, focus, minimal, entertainment, etc.\n\n  // === 计算属性 ===\n  const visibleComponents = computed(() => {\n    return desktopLayout.value.filter(item => item.visible);\n  });\n  const gridCellSize = computed(() => {\n    const availableWidth = screenSize.value.width - gridGap.value * (gridColumns.value + 1);\n    const availableHeight = screenSize.value.height - 44 - gridGap.value * (gridRows.value + 1); // 减去动态岛高度\n\n    return {\n      width: availableWidth / gridColumns.value,\n      height: availableHeight / gridRows.value\n    };\n  });\n  const componentStyles = computed(() => {\n    const styles = {};\n    visibleComponents.value.forEach(component => {\n      const {\n        column,\n        row,\n        colspan,\n        rowspan\n      } = component.position;\n      styles[component.id] = {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`,\n        zIndex: component.zIndex\n      };\n    });\n    return styles;\n  });\n\n  // 16x9网格的CSS变量\n  const gridCSSVariables = computed(() => ({\n    '--grid-columns': gridColumns.value,\n    '--grid-rows': gridRows.value,\n    '--grid-gap': `${gridGap.value}px`,\n    '--cell-width': `${gridCellSize.value.width}px`,\n    '--cell-height': `${gridCellSize.value.height}px`,\n    '--aspect-ratio': '16/9'\n  }));\n\n  // === 方法 ===\n  const updateScreenSize = () => {\n    screenSize.value = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n\n    // 响应式调整网格 - 保持16:9比例\n    if (screenSize.value.width < 768) {\n      // 移动端：保持16:9但调整密度\n      gridGap.value = 8;\n    } else if (screenSize.value.width < 1024) {\n      // 平板端\n      gridGap.value = 12;\n    } else {\n      // 桌面端\n      gridGap.value = 16;\n    }\n  };\n  const moveComponent = (componentId, newPosition) => {\n    const component = desktopLayout.value.find(c => c.id === componentId);\n    if (component) {\n      component.position = {\n        ...component.position,\n        ...newPosition\n      };\n    }\n  };\n  const resizeComponent = (componentId, newSize) => {\n    const component = desktopLayout.value.find(c => c.id === componentId);\n    if (component) {\n      component.position = {\n        ...component.position,\n        colspan: newSize.colspan || component.position.colspan,\n        rowspan: newSize.rowspan || component.position.rowspan\n      };\n    }\n  };\n  const toggleComponentVisibility = componentId => {\n    const component = desktopLayout.value.find(c => c.id === componentId);\n    if (component) {\n      component.visible = !component.visible;\n    }\n  };\n  const showDialog = dialogConfig => {\n    activeDialog.value = dialogConfig;\n  };\n  const hideDialog = () => {\n    activeDialog.value = null;\n  };\n  const setLayoutMode = mode => {\n    currentLayoutMode.value = mode;\n  };\n\n  // 根据场景获取布局配置\n  const getSceneLayout = sceneName => {\n    const sceneLayouts = {\n      'family': {\n        cardZone: {\n          column: 1,\n          row: 2,\n          colspan: 8,\n          rowspan: 8\n        },\n        drivingZone: {\n          column: 9,\n          row: 2,\n          colspan: 8,\n          rowspan: 8\n        }\n      },\n      'focus': {\n        cardZone: {\n          column: 1,\n          row: 2,\n          colspan: 8,\n          rowspan: 8\n        },\n        drivingZone: {\n          column: 9,\n          row: 2,\n          colspan: 8,\n          rowspan: 8\n        }\n      },\n      'minimal': {\n        cardZone: {\n          column: 1,\n          row: 2,\n          colspan: 4,\n          rowspan: 8\n        },\n        drivingZone: {\n          column: 5,\n          row: 2,\n          colspan: 12,\n          rowspan: 8\n        }\n      },\n      'entertainment': {\n        cardZone: {\n          column: 1,\n          row: 2,\n          colspan: 6,\n          rowspan: 8\n        },\n        drivingZone: {\n          column: 7,\n          row: 2,\n          colspan: 10,\n          rowspan: 8\n        }\n      }\n    };\n    return sceneLayouts[sceneName] || sceneLayouts.family;\n  };\n\n  // 保存布局到本地存储\n  const saveLayout = () => {\n    localStorage.setItem('ai-hmi-layout', JSON.stringify({\n      desktopLayout: desktopLayout.value,\n      currentLayoutMode: currentLayoutMode.value,\n      gridColumns: gridColumns.value,\n      gridRows: gridRows.value,\n      gridGap: gridGap.value\n    }));\n  };\n\n  // 从本地存储加载布局\n  const loadLayout = () => {\n    const saved = localStorage.getItem('ai-hmi-layout');\n    if (saved) {\n      try {\n        const data = JSON.parse(saved);\n        desktopLayout.value = data.desktopLayout || desktopLayout.value;\n        currentLayoutMode.value = data.currentLayoutMode || currentLayoutMode.value;\n        gridColumns.value = data.gridColumns || 16;\n        gridRows.value = data.gridRows || 9;\n        gridGap.value = data.gridGap || 16;\n      } catch (error) {\n        console.warn('Failed to load layout from localStorage:', error);\n      }\n    }\n  };\n\n  // 重置为默认布局\n  const resetLayout = () => {\n    desktopLayout.value = [{\n      id: 'dynamic-island',\n      component: 'DynamicIsland',\n      position: {\n        column: 1,\n        row: 1,\n        colspan: 16,\n        rowspan: 1\n      },\n      zIndex: 20,\n      visible: true\n    }, {\n      id: 'main-content-area',\n      component: 'MainContentArea',\n      position: {\n        column: 1,\n        row: 2,\n        colspan: 16,\n        rowspan: 8\n      },\n      zIndex: 1,\n      visible: true,\n      layout: 'zoned-fusion'\n    }];\n    currentLayoutMode.value = 'family';\n    gridColumns.value = 16;\n    gridRows.value = 9;\n    gridGap.value = 16;\n  };\n\n  // 初始化布局\n  const initializeLayout = () => {\n    updateScreenSize();\n    loadLayout();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', updateScreenSize);\n  };\n  return {\n    // 状态\n    gridColumns,\n    gridRows,\n    gridGap,\n    desktopLayout,\n    activeDialog,\n    screenSize,\n    currentLayoutMode,\n    // 计算属性\n    visibleComponents,\n    gridCellSize,\n    componentStyles,\n    gridCSSVariables,\n    // 方法\n    updateScreenSize,\n    moveComponent,\n    resizeComponent,\n    toggleComponentVisibility,\n    showDialog,\n    hideDialog,\n    setLayoutMode,\n    getSceneLayout,\n    saveLayout,\n    loadLayout,\n    resetLayout,\n    initializeLayout\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useLayoutStore", "gridColumns", "gridRows", "gridGap", "desktopLayout", "id", "component", "position", "column", "row", "colspan", "rowspan", "zIndex", "visible", "layout", "activeDialog", "screenSize", "width", "window", "innerWidth", "height", "innerHeight", "currentLayoutMode", "visibleComponents", "value", "filter", "item", "gridCellSize", "availableWidth", "availableHeight", "componentStyles", "styles", "for<PERSON>ach", "gridColumn", "gridRow", "gridCSSVariables", "updateScreenSize", "moveComponent", "componentId", "newPosition", "find", "c", "resizeComponent", "newSize", "toggleComponentVisibility", "showDialog", "dialogConfig", "hideDialog", "setLayoutMode", "mode", "getSceneLayout", "scene<PERSON><PERSON>", "sceneLayouts", "cardZone", "drivingZone", "family", "saveLayout", "localStorage", "setItem", "JSON", "stringify", "loadLayout", "saved", "getItem", "data", "parse", "error", "console", "warn", "resetLayout", "initializeLayout", "addEventListener"], "sources": ["F:/工作/theme/ai-hmi/src/store/modules/layout.js"], "sourcesContent": ["// 布局管理Store - 16x9网格系统和组件布局\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useLayoutStore = defineStore('layout', () => {\n  // === 状态 ===\n  const gridColumns = ref(16) // 网格列数 - 升级到16x9标准\n  const gridRows = ref(9) // 网格行数 - 升级到16x9标准\n  const gridGap = ref(16) // 网格间距\n\n  // 桌面组件布局配置 - 基于16x9网格\n  const desktopLayout = ref([\n    {\n      id: 'dynamic-island',\n      component: 'DynamicIsland',\n      position: { column: 1, row: 1, colspan: 16, rowspan: 1 },\n      zIndex: 20,\n      visible: true\n    },\n    {\n      id: 'main-content-area',\n      component: 'MainContentArea',\n      position: { column: 1, row: 2, colspan: 16, rowspan: 8 },\n      zIndex: 1,\n      visible: true,\n      layout: 'zoned-fusion' // 分区融合式布局\n    }\n  ])\n\n  // 当前活动的对话框\n  const activeDialog = ref(null)\n  \n  // 屏幕尺寸\n  const screenSize = ref({\n    width: window.innerWidth,\n    height: window.innerHeight\n  })\n\n  // 当前场景布局模式\n  const currentLayoutMode = ref('family') // family, focus, minimal, entertainment, etc.\n\n  // === 计算属性 ===\n  const visibleComponents = computed(() => {\n    return desktopLayout.value.filter(item => item.visible)\n  })\n\n  const gridCellSize = computed(() => {\n    const availableWidth = screenSize.value.width - (gridGap.value * (gridColumns.value + 1))\n    const availableHeight = screenSize.value.height - 44 - (gridGap.value * (gridRows.value + 1)) // 减去动态岛高度\n    \n    return {\n      width: availableWidth / gridColumns.value,\n      height: availableHeight / gridRows.value\n    }\n  })\n\n  const componentStyles = computed(() => {\n    const styles = {}\n    visibleComponents.value.forEach(component => {\n      const { column, row, colspan, rowspan } = component.position\n      styles[component.id] = {\n        gridColumn: `${column} / span ${colspan}`,\n        gridRow: `${row} / span ${rowspan}`,\n        zIndex: component.zIndex\n      }\n    })\n    return styles\n  })\n\n  // 16x9网格的CSS变量\n  const gridCSSVariables = computed(() => ({\n    '--grid-columns': gridColumns.value,\n    '--grid-rows': gridRows.value,\n    '--grid-gap': `${gridGap.value}px`,\n    '--cell-width': `${gridCellSize.value.width}px`,\n    '--cell-height': `${gridCellSize.value.height}px`,\n    '--aspect-ratio': '16/9'\n  }))\n\n  // === 方法 ===\n  const updateScreenSize = () => {\n    screenSize.value = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    }\n    \n    // 响应式调整网格 - 保持16:9比例\n    if (screenSize.value.width < 768) {\n      // 移动端：保持16:9但调整密度\n      gridGap.value = 8\n    } else if (screenSize.value.width < 1024) {\n      // 平板端\n      gridGap.value = 12\n    } else {\n      // 桌面端\n      gridGap.value = 16\n    }\n  }\n\n  const moveComponent = (componentId, newPosition) => {\n    const component = desktopLayout.value.find(c => c.id === componentId)\n    if (component) {\n      component.position = { ...component.position, ...newPosition }\n    }\n  }\n\n  const resizeComponent = (componentId, newSize) => {\n    const component = desktopLayout.value.find(c => c.id === componentId)\n    if (component) {\n      component.position = { \n        ...component.position, \n        colspan: newSize.colspan || component.position.colspan,\n        rowspan: newSize.rowspan || component.position.rowspan\n      }\n    }\n  }\n\n  const toggleComponentVisibility = (componentId) => {\n    const component = desktopLayout.value.find(c => c.id === componentId)\n    if (component) {\n      component.visible = !component.visible\n    }\n  }\n\n  const showDialog = (dialogConfig) => {\n    activeDialog.value = dialogConfig\n  }\n\n  const hideDialog = () => {\n    activeDialog.value = null\n  }\n\n  const setLayoutMode = (mode) => {\n    currentLayoutMode.value = mode\n  }\n\n  // 根据场景获取布局配置\n  const getSceneLayout = (sceneName) => {\n    const sceneLayouts = {\n      'family': {\n        cardZone: { column: 1, row: 2, colspan: 8, rowspan: 8 },\n        drivingZone: { column: 9, row: 2, colspan: 8, rowspan: 8 }\n      },\n      'focus': {\n        cardZone: { column: 1, row: 2, colspan: 8, rowspan: 8 },\n        drivingZone: { column: 9, row: 2, colspan: 8, rowspan: 8 }\n      },\n      'minimal': {\n        cardZone: { column: 1, row: 2, colspan: 4, rowspan: 8 },\n        drivingZone: { column: 5, row: 2, colspan: 12, rowspan: 8 }\n      },\n      'entertainment': {\n        cardZone: { column: 1, row: 2, colspan: 6, rowspan: 8 },\n        drivingZone: { column: 7, row: 2, colspan: 10, rowspan: 8 }\n      }\n    }\n    \n    return sceneLayouts[sceneName] || sceneLayouts.family\n  }\n\n  // 保存布局到本地存储\n  const saveLayout = () => {\n    localStorage.setItem('ai-hmi-layout', JSON.stringify({\n      desktopLayout: desktopLayout.value,\n      currentLayoutMode: currentLayoutMode.value,\n      gridColumns: gridColumns.value,\n      gridRows: gridRows.value,\n      gridGap: gridGap.value\n    }))\n  }\n\n  // 从本地存储加载布局\n  const loadLayout = () => {\n    const saved = localStorage.getItem('ai-hmi-layout')\n    if (saved) {\n      try {\n        const data = JSON.parse(saved)\n        desktopLayout.value = data.desktopLayout || desktopLayout.value\n        currentLayoutMode.value = data.currentLayoutMode || currentLayoutMode.value\n        gridColumns.value = data.gridColumns || 16\n        gridRows.value = data.gridRows || 9\n        gridGap.value = data.gridGap || 16\n      } catch (error) {\n        console.warn('Failed to load layout from localStorage:', error)\n      }\n    }\n  }\n\n  // 重置为默认布局\n  const resetLayout = () => {\n    desktopLayout.value = [\n      {\n        id: 'dynamic-island',\n        component: 'DynamicIsland',\n        position: { column: 1, row: 1, colspan: 16, rowspan: 1 },\n        zIndex: 20,\n        visible: true\n      },\n      {\n        id: 'main-content-area',\n        component: 'MainContentArea',\n        position: { column: 1, row: 2, colspan: 16, rowspan: 8 },\n        zIndex: 1,\n        visible: true,\n        layout: 'zoned-fusion'\n      }\n    ]\n    currentLayoutMode.value = 'family'\n    gridColumns.value = 16\n    gridRows.value = 9\n    gridGap.value = 16\n  }\n\n  // 初始化布局\n  const initializeLayout = () => {\n    updateScreenSize()\n    loadLayout()\n    \n    // 监听窗口大小变化\n    window.addEventListener('resize', updateScreenSize)\n  }\n\n  return {\n    // 状态\n    gridColumns,\n    gridRows,\n    gridGap,\n    desktopLayout,\n    activeDialog,\n    screenSize,\n    currentLayoutMode,\n    \n    // 计算属性\n    visibleComponents,\n    gridCellSize,\n    componentStyles,\n    gridCSSVariables,\n    \n    // 方法\n    updateScreenSize,\n    moveComponent,\n    resizeComponent,\n    toggleComponentVisibility,\n    showDialog,\n    hideDialog,\n    setLayoutMode,\n    getSceneLayout,\n    saveLayout,\n    loadLayout,\n    resetLayout,\n    initializeLayout\n  }\n})\n"], "mappings": ";;;;AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAEnC,OAAO,MAAMC,cAAc,GAAGH,WAAW,CAAC,QAAQ,EAAE,MAAM;EACxD;EACA,MAAMI,WAAW,GAAGH,GAAG,CAAC,EAAE,CAAC,EAAC;EAC5B,MAAMI,QAAQ,GAAGJ,GAAG,CAAC,CAAC,CAAC,EAAC;EACxB,MAAMK,OAAO,GAAGL,GAAG,CAAC,EAAE,CAAC,EAAC;;EAExB;EACA,MAAMM,aAAa,GAAGN,GAAG,CAAC,CACxB;IACEO,EAAE,EAAE,gBAAgB;IACpBC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACxDC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC,EACD;IACER,EAAE,EAAE,mBAAmB;IACvBC,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACxDC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,cAAc,CAAC;EACzB,CAAC,CACF,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGjB,GAAG,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMkB,UAAU,GAAGlB,GAAG,CAAC;IACrBmB,KAAK,EAAEC,MAAM,CAACC,UAAU;IACxBC,MAAM,EAAEF,MAAM,CAACG;EACjB,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGxB,GAAG,CAAC,QAAQ,CAAC,EAAC;;EAExC;EACA,MAAMyB,iBAAiB,GAAGxB,QAAQ,CAAC,MAAM;IACvC,OAAOK,aAAa,CAACoB,KAAK,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACb,OAAO,CAAC;EACzD,CAAC,CAAC;EAEF,MAAMc,YAAY,GAAG5B,QAAQ,CAAC,MAAM;IAClC,MAAM6B,cAAc,GAAGZ,UAAU,CAACQ,KAAK,CAACP,KAAK,GAAId,OAAO,CAACqB,KAAK,IAAIvB,WAAW,CAACuB,KAAK,GAAG,CAAC,CAAE;IACzF,MAAMK,eAAe,GAAGb,UAAU,CAACQ,KAAK,CAACJ,MAAM,GAAG,EAAE,GAAIjB,OAAO,CAACqB,KAAK,IAAItB,QAAQ,CAACsB,KAAK,GAAG,CAAC,CAAE,EAAC;;IAE9F,OAAO;MACLP,KAAK,EAAEW,cAAc,GAAG3B,WAAW,CAACuB,KAAK;MACzCJ,MAAM,EAAES,eAAe,GAAG3B,QAAQ,CAACsB;IACrC,CAAC;EACH,CAAC,CAAC;EAEF,MAAMM,eAAe,GAAG/B,QAAQ,CAAC,MAAM;IACrC,MAAMgC,MAAM,GAAG,CAAC,CAAC;IACjBR,iBAAiB,CAACC,KAAK,CAACQ,OAAO,CAAC1B,SAAS,IAAI;MAC3C,MAAM;QAAEE,MAAM;QAAEC,GAAG;QAAEC,OAAO;QAAEC;MAAQ,CAAC,GAAGL,SAAS,CAACC,QAAQ;MAC5DwB,MAAM,CAACzB,SAAS,CAACD,EAAE,CAAC,GAAG;QACrB4B,UAAU,EAAE,GAAGzB,MAAM,WAAWE,OAAO,EAAE;QACzCwB,OAAO,EAAE,GAAGzB,GAAG,WAAWE,OAAO,EAAE;QACnCC,MAAM,EAAEN,SAAS,CAACM;MACpB,CAAC;IACH,CAAC,CAAC;IACF,OAAOmB,MAAM;EACf,CAAC,CAAC;;EAEF;EACA,MAAMI,gBAAgB,GAAGpC,QAAQ,CAAC,OAAO;IACvC,gBAAgB,EAAEE,WAAW,CAACuB,KAAK;IACnC,aAAa,EAAEtB,QAAQ,CAACsB,KAAK;IAC7B,YAAY,EAAE,GAAGrB,OAAO,CAACqB,KAAK,IAAI;IAClC,cAAc,EAAE,GAAGG,YAAY,CAACH,KAAK,CAACP,KAAK,IAAI;IAC/C,eAAe,EAAE,GAAGU,YAAY,CAACH,KAAK,CAACJ,MAAM,IAAI;IACjD,gBAAgB,EAAE;EACpB,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMgB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,UAAU,CAACQ,KAAK,GAAG;MACjBP,KAAK,EAAEC,MAAM,CAACC,UAAU;MACxBC,MAAM,EAAEF,MAAM,CAACG;IACjB,CAAC;;IAED;IACA,IAAIL,UAAU,CAACQ,KAAK,CAACP,KAAK,GAAG,GAAG,EAAE;MAChC;MACAd,OAAO,CAACqB,KAAK,GAAG,CAAC;IACnB,CAAC,MAAM,IAAIR,UAAU,CAACQ,KAAK,CAACP,KAAK,GAAG,IAAI,EAAE;MACxC;MACAd,OAAO,CAACqB,KAAK,GAAG,EAAE;IACpB,CAAC,MAAM;MACL;MACArB,OAAO,CAACqB,KAAK,GAAG,EAAE;IACpB;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAACC,WAAW,EAAEC,WAAW,KAAK;IAClD,MAAMjC,SAAS,GAAGF,aAAa,CAACoB,KAAK,CAACgB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKiC,WAAW,CAAC;IACrE,IAAIhC,SAAS,EAAE;MACbA,SAAS,CAACC,QAAQ,GAAG;QAAE,GAAGD,SAAS,CAACC,QAAQ;QAAE,GAAGgC;MAAY,CAAC;IAChE;EACF,CAAC;EAED,MAAMG,eAAe,GAAGA,CAACJ,WAAW,EAAEK,OAAO,KAAK;IAChD,MAAMrC,SAAS,GAAGF,aAAa,CAACoB,KAAK,CAACgB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKiC,WAAW,CAAC;IACrE,IAAIhC,SAAS,EAAE;MACbA,SAAS,CAACC,QAAQ,GAAG;QACnB,GAAGD,SAAS,CAACC,QAAQ;QACrBG,OAAO,EAAEiC,OAAO,CAACjC,OAAO,IAAIJ,SAAS,CAACC,QAAQ,CAACG,OAAO;QACtDC,OAAO,EAAEgC,OAAO,CAAChC,OAAO,IAAIL,SAAS,CAACC,QAAQ,CAACI;MACjD,CAAC;IACH;EACF,CAAC;EAED,MAAMiC,yBAAyB,GAAIN,WAAW,IAAK;IACjD,MAAMhC,SAAS,GAAGF,aAAa,CAACoB,KAAK,CAACgB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKiC,WAAW,CAAC;IACrE,IAAIhC,SAAS,EAAE;MACbA,SAAS,CAACO,OAAO,GAAG,CAACP,SAAS,CAACO,OAAO;IACxC;EACF,CAAC;EAED,MAAMgC,UAAU,GAAIC,YAAY,IAAK;IACnC/B,YAAY,CAACS,KAAK,GAAGsB,YAAY;EACnC,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBhC,YAAY,CAACS,KAAK,GAAG,IAAI;EAC3B,CAAC;EAED,MAAMwB,aAAa,GAAIC,IAAI,IAAK;IAC9B3B,iBAAiB,CAACE,KAAK,GAAGyB,IAAI;EAChC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,SAAS,IAAK;IACpC,MAAMC,YAAY,GAAG;MACnB,QAAQ,EAAE;QACRC,QAAQ,EAAE;UAAE7C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACvD2C,WAAW,EAAE;UAAE9C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE;MAC3D,CAAC;MACD,OAAO,EAAE;QACP0C,QAAQ,EAAE;UAAE7C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACvD2C,WAAW,EAAE;UAAE9C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE;MAC3D,CAAC;MACD,SAAS,EAAE;QACT0C,QAAQ,EAAE;UAAE7C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACvD2C,WAAW,EAAE;UAAE9C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAE;MAC5D,CAAC;MACD,eAAe,EAAE;QACf0C,QAAQ,EAAE;UAAE7C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACvD2C,WAAW,EAAE;UAAE9C,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAE;MAC5D;IACF,CAAC;IAED,OAAOyC,YAAY,CAACD,SAAS,CAAC,IAAIC,YAAY,CAACG,MAAM;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBC,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC;MACnDxD,aAAa,EAAEA,aAAa,CAACoB,KAAK;MAClCF,iBAAiB,EAAEA,iBAAiB,CAACE,KAAK;MAC1CvB,WAAW,EAAEA,WAAW,CAACuB,KAAK;MAC9BtB,QAAQ,EAAEA,QAAQ,CAACsB,KAAK;MACxBrB,OAAO,EAAEA,OAAO,CAACqB;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,eAAe,CAAC;IACnD,IAAID,KAAK,EAAE;MACT,IAAI;QACF,MAAME,IAAI,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;QAC9B1D,aAAa,CAACoB,KAAK,GAAGwC,IAAI,CAAC5D,aAAa,IAAIA,aAAa,CAACoB,KAAK;QAC/DF,iBAAiB,CAACE,KAAK,GAAGwC,IAAI,CAAC1C,iBAAiB,IAAIA,iBAAiB,CAACE,KAAK;QAC3EvB,WAAW,CAACuB,KAAK,GAAGwC,IAAI,CAAC/D,WAAW,IAAI,EAAE;QAC1CC,QAAQ,CAACsB,KAAK,GAAGwC,IAAI,CAAC9D,QAAQ,IAAI,CAAC;QACnCC,OAAO,CAACqB,KAAK,GAAGwC,IAAI,CAAC7D,OAAO,IAAI,EAAE;MACpC,CAAC,CAAC,OAAO+D,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;MACjE;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxBjE,aAAa,CAACoB,KAAK,GAAG,CACpB;MACEnB,EAAE,EAAE,gBAAgB;MACpBC,SAAS,EAAE,eAAe;MAC1BC,QAAQ,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACxDC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;IACX,CAAC,EACD;MACER,EAAE,EAAE,mBAAmB;MACvBC,SAAS,EAAE,iBAAiB;MAC5BC,QAAQ,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACxDC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;IACV,CAAC,CACF;IACDQ,iBAAiB,CAACE,KAAK,GAAG,QAAQ;IAClCvB,WAAW,CAACuB,KAAK,GAAG,EAAE;IACtBtB,QAAQ,CAACsB,KAAK,GAAG,CAAC;IAClBrB,OAAO,CAACqB,KAAK,GAAG,EAAE;EACpB,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlC,gBAAgB,CAAC,CAAC;IAClByB,UAAU,CAAC,CAAC;;IAEZ;IACA3C,MAAM,CAACqD,gBAAgB,CAAC,QAAQ,EAAEnC,gBAAgB,CAAC;EACrD,CAAC;EAED,OAAO;IACL;IACAnC,WAAW;IACXC,QAAQ;IACRC,OAAO;IACPC,aAAa;IACbW,YAAY;IACZC,UAAU;IACVM,iBAAiB;IAEjB;IACAC,iBAAiB;IACjBI,YAAY;IACZG,eAAe;IACfK,gBAAgB;IAEhB;IACAC,gBAAgB;IAChBC,aAAa;IACbK,eAAe;IACfE,yBAAyB;IACzBC,UAAU;IACVE,UAAU;IACVC,aAAa;IACbE,cAAc;IACdM,UAAU;IACVK,UAAU;IACVQ,WAAW;IACXC;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}