{"ast": null, "code": "import { computed, ref } from 'vue';\nimport { useVPAStore } from '@/store/modules/vpa';\nexport default {\n  name: 'VPADigitalHuman',\n  props: {\n    size: {\n      type: String,\n      default: 'medium',\n      // small, medium, large\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    showInfoPanel: {\n      type: Boolean,\n      default: true\n    },\n    interactive: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(props) {\n    const vpaStore = useVPAStore();\n    const imageError = ref(false);\n\n    // 计算属性\n    const vpaConfig = computed(() => vpaStore.vpaDisplayConfig);\n    const currentExpression = computed(() => vpaStore.currentExpression);\n    const currentAction = computed(() => vpaStore.currentAction);\n    const isListening = computed(() => vpaStore.isListening);\n    const isSpeaking = computed(() => vpaStore.isSpeaking);\n    const contextualGreeting = computed(() => vpaStore.contextualGreeting);\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      };\n      return expressions[currentExpression.value] || expressions.neutral;\n    });\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      };\n      return modes[vpaStore.currentMode] || '未知模式';\n    });\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭场景',\n        'focus': '专注场景',\n        'minimal': '简约场景',\n        'entertainment': '娱乐场景'\n      };\n      return scenes[vpaStore.currentScene] || '通用场景';\n    });\n\n    // 方法\n    const handleVPAClick = () => {\n      if (!props.interactive) return;\n      if (vpaStore.isActive) {\n        vpaStore.startConversation();\n      } else {\n        vpaStore.switchMode('companion');\n      }\n    };\n    const toggleListening = () => {\n      if (isListening.value) {\n        vpaStore.endConversation();\n      } else {\n        vpaStore.startConversation();\n      }\n    };\n    const switchVPAMode = () => {\n      const modes = ['companion', 'interactive', 'restricted'];\n      const currentIndex = modes.indexOf(vpaStore.currentMode);\n      const nextMode = modes[(currentIndex + 1) % modes.length];\n      vpaStore.switchMode(nextMode);\n    };\n    const handleImageError = () => {\n      imageError.value = true;\n      console.warn('VPA avatar image failed to load');\n    };\n    return {\n      vpaConfig,\n      currentExpression,\n      currentAction,\n      isListening,\n      isSpeaking,\n      contextualGreeting,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      imageError,\n      handleVPAClick,\n      toggleListening,\n      switchVPAMode,\n      handleImageError\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "useVPAStore", "name", "props", "size", "type", "String", "default", "validator", "value", "includes", "showInfoPanel", "Boolean", "interactive", "setup", "vpaStore", "imageError", "vpaConfig", "vpaDisplayConfig", "currentExpression", "currentAction", "isListening", "isSpeaking", "contextualGreeting", "expressionEmoji", "expressions", "neutral", "modeText", "modes", "currentMode", "sceneText", "scenes", "currentScene", "handleVPAClick", "isActive", "startConversation", "switchMode", "toggleListening", "endConversation", "switchVPAMode", "currentIndex", "indexOf", "nextMode", "length", "handleImageError", "console", "warn"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\vpa\\VPADigitalHuman.vue"], "sourcesContent": ["<template>\n  <div \n    class=\"vpa-digital-human\"\n    :class=\"[\n      `vpa-mode-${vpaConfig.mode}`,\n      `vpa-expression-${currentExpression}`,\n      `vpa-action-${currentAction}`,\n      { 'vpa-active': vpaConfig.isActive }\n    ]\"\n    @click=\"handleVPAClick\"\n  >\n    <!-- VPA头像容器 -->\n    <div class=\"vpa-avatar-container\">\n      <!-- 动态背景光环 -->\n      <div class=\"vpa-aura\" :class=\"{ 'pulsing': isListening || isSpeaking }\"></div>\n      \n      <!-- 头像图片 -->\n      <div class=\"vpa-avatar\">\n        <img \n          :src=\"`/docs/${vpaConfig.avatar}`\" \n          :alt=\"`VPA ${currentExpression}`\"\n          class=\"avatar-image\"\n          @error=\"handleImageError\"\n        />\n        \n        <!-- 表情覆盖层 -->\n        <div class=\"expression-overlay\">\n          <span class=\"expression-emoji\">{{ expressionEmoji }}</span>\n        </div>\n      </div>\n\n      <!-- 状态指示器 -->\n      <div class=\"status-indicators\">\n        <div v-if=\"isListening\" class=\"status-indicator listening\">\n          <div class=\"pulse-ring\"></div>\n          <span class=\"status-icon\">🎤</span>\n        </div>\n        <div v-if=\"isSpeaking\" class=\"status-indicator speaking\">\n          <div class=\"wave-animation\"></div>\n          <span class=\"status-icon\">🔊</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- VPA信息面板 -->\n    <div class=\"vpa-info-panel\" v-if=\"showInfoPanel\">\n      <div class=\"vpa-greeting\">\n        {{ contextualGreeting }}\n      </div>\n      \n      <div class=\"vpa-status\">\n        <span class=\"mode-badge\">{{ modeText }}</span>\n        <span class=\"scene-badge\">{{ sceneText }}</span>\n      </div>\n    </div>\n\n    <!-- 快速操作按钮 -->\n    <div class=\"vpa-quick-actions\" v-if=\"vpaConfig.isActive\">\n      <button \n        class=\"quick-action-btn\"\n        @click.stop=\"toggleListening\"\n        :class=\"{ active: isListening }\"\n      >\n        <span class=\"btn-icon\">{{ isListening ? '⏹️' : '🎤' }}</span>\n      </button>\n      \n      <button \n        class=\"quick-action-btn\"\n        @click.stop=\"switchVPAMode\"\n      >\n        <span class=\"btn-icon\">🔄</span>\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { useVPAStore } from '@/store/modules/vpa'\n\nexport default {\n  name: 'VPADigitalHuman',\n  props: {\n    size: {\n      type: String,\n      default: 'medium', // small, medium, large\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    showInfoPanel: {\n      type: Boolean,\n      default: true\n    },\n    interactive: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(props) {\n    const vpaStore = useVPAStore()\n    const imageError = ref(false)\n\n    // 计算属性\n    const vpaConfig = computed(() => vpaStore.vpaDisplayConfig)\n    const currentExpression = computed(() => vpaStore.currentExpression)\n    const currentAction = computed(() => vpaStore.currentAction)\n    const isListening = computed(() => vpaStore.isListening)\n    const isSpeaking = computed(() => vpaStore.isSpeaking)\n    const contextualGreeting = computed(() => vpaStore.contextualGreeting)\n\n    const expressionEmoji = computed(() => {\n      const expressions = {\n        'neutral': '😊',\n        'happy': '😄',\n        'thinking': '🤔',\n        'concerned': '😟',\n        'excited': '🤩',\n        'listening': '👂',\n        'speaking': '💬'\n      }\n      return expressions[currentExpression.value] || expressions.neutral\n    })\n\n    const modeText = computed(() => {\n      const modes = {\n        'companion': '伙伴模式',\n        'interactive': '交互模式',\n        'restricted': '访客模式'\n      }\n      return modes[vpaStore.currentMode] || '未知模式'\n    })\n\n    const sceneText = computed(() => {\n      const scenes = {\n        'family': '家庭场景',\n        'focus': '专注场景',\n        'minimal': '简约场景',\n        'entertainment': '娱乐场景'\n      }\n      return scenes[vpaStore.currentScene] || '通用场景'\n    })\n\n    // 方法\n    const handleVPAClick = () => {\n      if (!props.interactive) return\n      \n      if (vpaStore.isActive) {\n        vpaStore.startConversation()\n      } else {\n        vpaStore.switchMode('companion')\n      }\n    }\n\n    const toggleListening = () => {\n      if (isListening.value) {\n        vpaStore.endConversation()\n      } else {\n        vpaStore.startConversation()\n      }\n    }\n\n    const switchVPAMode = () => {\n      const modes = ['companion', 'interactive', 'restricted']\n      const currentIndex = modes.indexOf(vpaStore.currentMode)\n      const nextMode = modes[(currentIndex + 1) % modes.length]\n      vpaStore.switchMode(nextMode)\n    }\n\n    const handleImageError = () => {\n      imageError.value = true\n      console.warn('VPA avatar image failed to load')\n    }\n\n    return {\n      vpaConfig,\n      currentExpression,\n      currentAction,\n      isListening,\n      isSpeaking,\n      contextualGreeting,\n      expressionEmoji,\n      modeText,\n      sceneText,\n      imageError,\n      handleVPAClick,\n      toggleListening,\n      switchVPAMode,\n      handleImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-digital-human {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* VPA头像容器 */\n.vpa-avatar-container {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  margin-bottom: 10px;\n}\n\n/* 动态光环 */\n.vpa-aura {\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  border-radius: 50%;\n  background: radial-gradient(\n    circle,\n    rgba(74, 144, 226, 0.3) 0%,\n    rgba(74, 144, 226, 0.1) 50%,\n    transparent 100%\n  );\n  transition: all 0.3s ease;\n}\n\n.vpa-aura.pulsing {\n  animation: auraPulse 2s ease-in-out infinite;\n}\n\n@keyframes auraPulse {\n  0%, 100% { transform: scale(1); opacity: 0.3; }\n  50% { transform: scale(1.2); opacity: 0.6; }\n}\n\n/* 头像 */\n.vpa-avatar {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: all 0.3s ease;\n}\n\n.expression-overlay {\n  position: absolute;\n  bottom: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n/* 状态指示器 */\n.status-indicators {\n  position: absolute;\n  top: -15px;\n  right: -15px;\n}\n\n.status-indicator {\n  position: relative;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.status-indicator.listening {\n  background: rgba(76, 175, 80, 0.9);\n}\n\n.status-indicator.speaking {\n  background: rgba(33, 150, 243, 0.9);\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 2px solid rgba(76, 175, 80, 0.6);\n  border-radius: 50%;\n  animation: pulseRing 1.5s ease-out infinite;\n}\n\n@keyframes pulseRing {\n  0% { transform: scale(1); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n.wave-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(33, 150, 243, 0.6) 0%, transparent 70%);\n  animation: waveAnimation 1s ease-in-out infinite;\n}\n\n@keyframes waveAnimation {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.3); }\n}\n\n/* 信息面板 */\n.vpa-info-panel {\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 8px 12px;\n  margin-bottom: 10px;\n  max-width: 200px;\n  text-align: center;\n}\n\n.vpa-greeting {\n  color: white;\n  font-size: 12px;\n  margin-bottom: 5px;\n  line-height: 1.3;\n}\n\n.vpa-status {\n  display: flex;\n  gap: 5px;\n  justify-content: center;\n}\n\n.mode-badge,\n.scene-badge {\n  background: rgba(74, 144, 226, 0.8);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.scene-badge {\n  background: rgba(156, 39, 176, 0.8);\n}\n\n/* 快速操作按钮 */\n.vpa-quick-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.quick-action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.quick-action-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n.quick-action-btn.active {\n  background: rgba(76, 175, 80, 0.8);\n}\n\n.btn-icon {\n  font-size: 14px;\n}\n\n/* VPA模式样式 */\n.vpa-mode-companion .vpa-avatar {\n  border-color: rgba(76, 175, 80, 0.5);\n}\n\n.vpa-mode-interactive .vpa-avatar {\n  border-color: rgba(33, 150, 243, 0.5);\n}\n\n.vpa-mode-restricted .vpa-avatar {\n  border-color: rgba(158, 158, 158, 0.5);\n  opacity: 0.6;\n}\n\n/* 表情样式 */\n.vpa-expression-happy .avatar-image {\n  filter: brightness(1.1) saturate(1.2);\n}\n\n.vpa-expression-thinking .avatar-image {\n  filter: grayscale(0.3);\n}\n\n.vpa-expression-excited .avatar-image {\n  filter: brightness(1.2) saturate(1.3) hue-rotate(10deg);\n}\n\n/* 动作样式 */\n.vpa-action-listening .vpa-avatar {\n  animation: listeningBounce 2s ease-in-out infinite;\n}\n\n.vpa-action-speaking .vpa-avatar {\n  animation: speakingPulse 1s ease-in-out infinite;\n}\n\n@keyframes listeningBounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-5px); }\n}\n\n@keyframes speakingPulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .vpa-avatar-container {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .vpa-info-panel {\n    max-width: 150px;\n    padding: 6px 10px;\n  }\n  \n  .vpa-greeting {\n    font-size: 11px;\n  }\n}\n</style>\n"], "mappings": "AA6EA,SAASA,QAAQ,EAAEC,GAAE,QAAS,KAAI;AAClC,SAASC,WAAU,QAAS,qBAAoB;AAEhD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAEC,KAAI,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACjE,CAAC;IACDE,aAAa,EAAE;MACbN,IAAI,EAAEO,OAAO;MACbL,OAAO,EAAE;IACX,CAAC;IACDM,WAAW,EAAE;MACXR,IAAI,EAAEO,OAAO;MACbL,OAAO,EAAE;IACX;EACF,CAAC;EACDO,KAAKA,CAACX,KAAK,EAAE;IACX,MAAMY,QAAO,GAAId,WAAW,CAAC;IAC7B,MAAMe,UAAS,GAAIhB,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMiB,SAAQ,GAAIlB,QAAQ,CAAC,MAAMgB,QAAQ,CAACG,gBAAgB;IAC1D,MAAMC,iBAAgB,GAAIpB,QAAQ,CAAC,MAAMgB,QAAQ,CAACI,iBAAiB;IACnE,MAAMC,aAAY,GAAIrB,QAAQ,CAAC,MAAMgB,QAAQ,CAACK,aAAa;IAC3D,MAAMC,WAAU,GAAItB,QAAQ,CAAC,MAAMgB,QAAQ,CAACM,WAAW;IACvD,MAAMC,UAAS,GAAIvB,QAAQ,CAAC,MAAMgB,QAAQ,CAACO,UAAU;IACrD,MAAMC,kBAAiB,GAAIxB,QAAQ,CAAC,MAAMgB,QAAQ,CAACQ,kBAAkB;IAErE,MAAMC,eAAc,GAAIzB,QAAQ,CAAC,MAAM;MACrC,MAAM0B,WAAU,GAAI;QAClB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE;MACd;MACA,OAAOA,WAAW,CAACN,iBAAiB,CAACV,KAAK,KAAKgB,WAAW,CAACC,OAAM;IACnE,CAAC;IAED,MAAMC,QAAO,GAAI5B,QAAQ,CAAC,MAAM;MAC9B,MAAM6B,KAAI,GAAI;QACZ,WAAW,EAAE,MAAM;QACnB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE;MAChB;MACA,OAAOA,KAAK,CAACb,QAAQ,CAACc,WAAW,KAAK,MAAK;IAC7C,CAAC;IAED,MAAMC,SAAQ,GAAI/B,QAAQ,CAAC,MAAM;MAC/B,MAAMgC,MAAK,GAAI;QACb,QAAQ,EAAE,MAAM;QAChB,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,MAAM;QACjB,eAAe,EAAE;MACnB;MACA,OAAOA,MAAM,CAAChB,QAAQ,CAACiB,YAAY,KAAK,MAAK;IAC/C,CAAC;;IAED;IACA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI,CAAC9B,KAAK,CAACU,WAAW,EAAE;MAExB,IAAIE,QAAQ,CAACmB,QAAQ,EAAE;QACrBnB,QAAQ,CAACoB,iBAAiB,CAAC;MAC7B,OAAO;QACLpB,QAAQ,CAACqB,UAAU,CAAC,WAAW;MACjC;IACF;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAIhB,WAAW,CAACZ,KAAK,EAAE;QACrBM,QAAQ,CAACuB,eAAe,CAAC;MAC3B,OAAO;QACLvB,QAAQ,CAACoB,iBAAiB,CAAC;MAC7B;IACF;IAEA,MAAMI,aAAY,GAAIA,CAAA,KAAM;MAC1B,MAAMX,KAAI,GAAI,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY;MACvD,MAAMY,YAAW,GAAIZ,KAAK,CAACa,OAAO,CAAC1B,QAAQ,CAACc,WAAW;MACvD,MAAMa,QAAO,GAAId,KAAK,CAAC,CAACY,YAAW,GAAI,CAAC,IAAIZ,KAAK,CAACe,MAAM;MACxD5B,QAAQ,CAACqB,UAAU,CAACM,QAAQ;IAC9B;IAEA,MAAME,gBAAe,GAAIA,CAAA,KAAM;MAC7B5B,UAAU,CAACP,KAAI,GAAI,IAAG;MACtBoC,OAAO,CAACC,IAAI,CAAC,iCAAiC;IAChD;IAEA,OAAO;MACL7B,SAAS;MACTE,iBAAiB;MACjBC,aAAa;MACbC,WAAW;MACXC,UAAU;MACVC,kBAAkB;MAClBC,eAAe;MACfG,QAAQ;MACRG,SAAS;MACTd,UAAU;MACViB,cAAc;MACdI,eAAe;MACfE,aAAa;MACbK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}