{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, renderSlot as _renderSlot, vModelCheckbox as _vModelCheckbox, withDirectives as _withDirectives, createTextVNode as _createTextVNode, vModelText as _vModelText } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dynamic-wallpaper-manager\"\n};\nconst _hoisted_2 = [\"src\"];\nconst _hoisted_3 = {\n  key: 3,\n  class: \"loading-overlay\"\n};\nconst _hoisted_4 = {\n  class: \"loading-spinner\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"config-panel\"\n};\nconst _hoisted_6 = {\n  class: \"config-header\"\n};\nconst _hoisted_7 = {\n  class: \"config-content\"\n};\nconst _hoisted_8 = {\n  class: \"config-item\"\n};\nconst _hoisted_9 = {\n  class: \"config-item\"\n};\nconst _hoisted_10 = {\n  class: \"config-item\"\n};\nconst _hoisted_11 = {\n  class: \"config-item\"\n};\nconst _hoisted_12 = {\n  class: \"config-actions\"\n};\nconst _hoisted_13 = [\"disabled\"];\nconst _hoisted_14 = [\"disabled\"];\nconst _hoisted_15 = {\n  key: 1,\n  class: \"preview-modal\"\n};\nconst _hoisted_16 = {\n  class: \"preview-content\"\n};\nconst _hoisted_17 = {\n  class: \"preview-header\"\n};\nconst _hoisted_18 = {\n  class: \"preview-body\"\n};\nconst _hoisted_19 = {\n  class: \"preview-video-container\"\n};\nconst _hoisted_20 = [\"src\"];\nconst _hoisted_21 = {\n  class: \"preview-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 壁纸容器 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"wallpaper-container\", {\n      'loading': $setup.isLoading\n    }])\n  }, [_createCommentVNode(\" 图片壁纸 \"), $setup.currentWallpaper && $setup.currentWallpaper.type === 'image' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"wallpaper-image\",\n    style: _normalizeStyle({\n      backgroundImage: `url(${$setup.currentWallpaper.url})`\n    })\n  }, null, 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 视频壁纸 \"), $setup.currentWallpaper && $setup.currentWallpaper.type === 'video' ? (_openBlock(), _createElementBlock(\"video\", {\n    key: 1,\n    class: \"wallpaper-video\",\n    src: $setup.currentWallpaper.url,\n    autoplay: \"\",\n    muted: \"\",\n    loop: \"\",\n    playsinline: \"\"\n  }, null, 8 /* PROPS */, _hoisted_2)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 默认渐变背景 \"), !$setup.currentWallpaper ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"wallpaper-default\",\n    style: _normalizeStyle($setup.defaultGradient)\n  }, null, 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载遮罩 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[16] || (_cache[16] = _createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.loadingText), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */), _createCommentVNode(\" 内容插槽 \"), _createElementVNode(\"div\", {\n    class: \"content-overlay\",\n    style: _normalizeStyle($setup.overlayStyles)\n  }, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)], 4 /* STYLE */), _createCommentVNode(\" 配置面板 \"), $setup.showConfig ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[17] || (_cache[17] = _createElementVNode(\"h3\", null, \"壁纸设置\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = $event => $setup.showConfig = false),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"label\", null, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.config.enabled = $event),\n    onChange: _cache[2] || (_cache[2] = (...args) => $setup.onConfigChange && $setup.onConfigChange(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $setup.config.enabled]]), _cache[18] || (_cache[18] = _createTextVNode(\" 启用动态壁纸 \", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"label\", null, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.config.autoGenerate = $event),\n    onChange: _cache[4] || (_cache[4] = (...args) => $setup.onConfigChange && $setup.onConfigChange(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelCheckbox, $setup.config.autoGenerate]]), _cache[19] || (_cache[19] = _createTextVNode(\" 自动生成壁纸 \", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_10, [_cache[20] || (_cache[20] = _createElementVNode(\"label\", null, \"壁纸透明度:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"range\",\n    min: \"0.1\",\n    max: \"1\",\n    step: \"0.1\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.config.opacity = $event),\n    onInput: _cache[6] || (_cache[6] = (...args) => $setup.onConfigChange && $setup.onConfigChange(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.config.opacity]]), _createElementVNode(\"span\", null, _toDisplayString($setup.config.opacity), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_cache[21] || (_cache[21] = _createElementVNode(\"label\", null, \"卡片透明度:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"range\",\n    min: \"0.1\",\n    max: \"0.8\",\n    step: \"0.1\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.config.cardOpacity = $event),\n    onInput: _cache[8] || (_cache[8] = (...args) => $setup.onConfigChange && $setup.onConfigChange(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.config.cardOpacity]]), _createElementVNode(\"span\", null, _toDisplayString($setup.config.cardOpacity), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"button\", {\n    onClick: _cache[9] || (_cache[9] = (...args) => $setup.generateNewWallpaper && $setup.generateNewWallpaper(...args)),\n    disabled: $setup.isLoading\n  }, \" 重新生成壁纸 \", 8 /* PROPS */, _hoisted_13), _createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = (...args) => $setup.generateDynamicWallpaper && $setup.generateDynamicWallpaper(...args)),\n    disabled: $setup.isLoading || !$setup.currentWallpaper\n  }, \" 生成动态壁纸 \", 8 /* PROPS */, _hoisted_14), _createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = (...args) => $setup.resetToDefault && $setup.resetToDefault(...args))\n  }, \" 恢复默认 \")])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 配置按钮 \"), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"config-toggle\", {\n      'active': $setup.showConfig\n    }]),\n    onClick: _cache[12] || (_cache[12] = $event => $setup.showConfig = !$setup.showConfig)\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createCommentVNode(\" 动态壁纸预览模态框 \"), $setup.showDynamicPreview ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[23] || (_cache[23] = _createElementVNode(\"h3\", null, \"动态壁纸预览\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[13] || (_cache[13] = (...args) => $setup.closeDynamicPreview && $setup.closeDynamicPreview(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [$setup.previewVideoUrl ? (_openBlock(), _createElementBlock(\"video\", {\n    key: 0,\n    class: \"preview-video\",\n    src: $setup.previewVideoUrl,\n    autoplay: \"\",\n    muted: \"\",\n    loop: \"\",\n    playsinline: \"\"\n  }, null, 8 /* PROPS */, _hoisted_20)) : _createCommentVNode(\"v-if\", true)]), _cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    class: \"preview-info\"\n  }, [_createElementVNode(\"p\", null, \"动态壁纸已生成完成！\"), _createElementVNode(\"p\", null, \"预览视频效果，决定是否应用为动态壁纸。\")], -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"button\", {\n    onClick: _cache[14] || (_cache[14] = (...args) => $setup.applyDynamicWallpaper && $setup.applyDynamicWallpaper(...args)),\n    class: \"apply-btn\"\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 应用动态壁纸 \", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[15] || (_cache[15] = (...args) => $setup.closeDynamicPreview && $setup.closeDynamicPreview(...args)),\n    class: \"cancel-btn\"\n  }, _cache[26] || (_cache[26] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 取消 \", -1 /* CACHED */)]))])])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "$setup", "isLoading", "currentWallpaper", "type", "style", "_normalizeStyle", "backgroundImage", "url", "src", "autoplay", "muted", "loop", "playsinline", "defaultGradient", "_hoisted_3", "_hoisted_4", "_toDisplayString", "loadingText", "overlayStyles", "_renderSlot", "_ctx", "$slots", "undefined", "showConfig", "_hoisted_5", "_hoisted_6", "onClick", "_cache", "$event", "_hoisted_7", "_hoisted_8", "config", "enabled", "onChange", "args", "onConfigChange", "_hoisted_9", "autoGenerate", "_hoisted_10", "min", "max", "step", "opacity", "onInput", "_hoisted_11", "cardOpacity", "_hoisted_12", "generateNewWallpaper", "disabled", "_hoisted_13", "generateDynamicWallpaper", "_hoisted_14", "resetToDefault", "showDynamicPreview", "_hoisted_15", "_hoisted_16", "_hoisted_17", "closeDynamicPreview", "_hoisted_18", "_hoisted_19", "previewVideoUrl", "_hoisted_21", "applyDynamicWallpaper"], "sources": ["F:\\工作\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dynamic-wallpaper-manager\">\r\n    <!-- 壁纸容器 -->\r\n    <div class=\"wallpaper-container\" :class=\"{ 'loading': isLoading }\">\r\n      <!-- 图片壁纸 -->\r\n      <div \r\n        v-if=\"currentWallpaper && currentWallpaper.type === 'image'\"\r\n        class=\"wallpaper-image\"\r\n        :style=\"{ backgroundImage: `url(${currentWallpaper.url})` }\"\r\n      ></div>\r\n      \r\n      <!-- 视频壁纸 -->\r\n      <video \r\n        v-if=\"currentWallpaper && currentWallpaper.type === 'video'\"\r\n        class=\"wallpaper-video\"\r\n        :src=\"currentWallpaper.url\"\r\n        autoplay\r\n        muted\r\n        loop\r\n        playsinline\r\n      ></video>\r\n      \r\n      <!-- 默认渐变背景 -->\r\n      <div \r\n        v-if=\"!currentWallpaper\"\r\n        class=\"wallpaper-default\"\r\n        :style=\"defaultGradient\"\r\n      ></div>\r\n      \r\n      <!-- 加载遮罩 -->\r\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\r\n        <div class=\"loading-spinner\">\r\n          <i class=\"fas fa-spinner fa-spin\"></i>\r\n          <span>{{ loadingText }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 内容插槽 -->\r\n    <div class=\"content-overlay\" :style=\"overlayStyles\">\r\n      <slot></slot>\r\n    </div>\r\n    \r\n    <!-- 配置面板 -->\r\n    <div v-if=\"showConfig\" class=\"config-panel\">\r\n      <div class=\"config-header\">\r\n        <h3>壁纸设置</h3>\r\n        <button @click=\"showConfig = false\" class=\"close-btn\">×</button>\r\n      </div>\r\n      <div class=\"config-content\">\r\n        <div class=\"config-item\">\r\n          <label>\r\n            <input \r\n              type=\"checkbox\" \r\n              v-model=\"config.enabled\"\r\n              @change=\"onConfigChange\"\r\n            />\r\n            启用动态壁纸\r\n          </label>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>\r\n            <input \r\n              type=\"checkbox\" \r\n              v-model=\"config.autoGenerate\"\r\n              @change=\"onConfigChange\"\r\n            />\r\n            自动生成壁纸\r\n          </label>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>壁纸透明度:</label>\r\n          <input \r\n            type=\"range\" \r\n            min=\"0.1\" \r\n            max=\"1\" \r\n            step=\"0.1\"\r\n            v-model=\"config.opacity\"\r\n            @input=\"onConfigChange\"\r\n          />\r\n          <span>{{ config.opacity }}</span>\r\n        </div>\r\n        <div class=\"config-item\">\r\n          <label>卡片透明度:</label>\r\n          <input \r\n            type=\"range\" \r\n            min=\"0.1\" \r\n            max=\"0.8\" \r\n            step=\"0.1\"\r\n            v-model=\"config.cardOpacity\"\r\n            @input=\"onConfigChange\"\r\n          />\r\n          <span>{{ config.cardOpacity }}</span>\r\n        </div>\r\n        <div class=\"config-actions\">\r\n          <button @click=\"generateNewWallpaper\" :disabled=\"isLoading\">\r\n            重新生成壁纸\r\n          </button>\r\n          <button @click=\"generateDynamicWallpaper\" :disabled=\"isLoading || !currentWallpaper\">\r\n            生成动态壁纸\r\n          </button>\r\n          <button @click=\"resetToDefault\">\r\n            恢复默认\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 配置按钮 -->\r\n    <button \r\n      class=\"config-toggle\"\r\n      @click=\"showConfig = !showConfig\"\r\n      :class=\"{ 'active': showConfig }\"\r\n    >\r\n      <i class=\"fas fa-cog\"></i>\r\n    </button>\r\n    \r\n    <!-- 动态壁纸预览模态框 -->\r\n    <div v-if=\"showDynamicPreview\" class=\"preview-modal\">\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>动态壁纸预览</h3>\r\n          <button @click=\"closeDynamicPreview\" class=\"close-btn\">×</button>\r\n        </div>\r\n        <div class=\"preview-body\">\r\n          <div class=\"preview-video-container\">\r\n            <video \r\n              v-if=\"previewVideoUrl\"\r\n              class=\"preview-video\"\r\n              :src=\"previewVideoUrl\"\r\n              autoplay\r\n              muted\r\n              loop\r\n              playsinline\r\n            ></video>\r\n          </div>\r\n          <div class=\"preview-info\">\r\n            <p>动态壁纸已生成完成！</p>\r\n            <p>预览视频效果，决定是否应用为动态壁纸。</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"preview-actions\">\r\n          <button @click=\"applyDynamicWallpaper\" class=\"apply-btn\">\r\n            <i class=\"fas fa-check\"></i> 应用动态壁纸\r\n          </button>\r\n          <button @click=\"closeDynamicPreview\" class=\"cancel-btn\">\r\n            <i class=\"fas fa-times\"></i> 取消\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'\r\nimport ImageGenerationService from '@/services/ImageGenerationService'\r\nimport DynamicWallpaperService from '@/services/DynamicWallpaperService'\r\nimport ColorExtractor from '@/utils/ColorExtractor'\r\nimport AIColorAnalyzer from '@/utils/AIColorAnalyzer'\r\n\r\nexport default {\r\n  name: 'DynamicWallpaperManager',\r\n  \r\n  props: {\r\n    scenePrompt: {\r\n      type: String,\r\n      default: '现代简约风格，商务氛围'\r\n    },\r\n    autoGenerate: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    enableConfig: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  emits: ['wallpaper-changed', 'colors-extracted'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式数据\r\n    const isLoading = ref(false)\r\n    const showConfig = ref(false)\r\n    const currentWallpaper = ref(null)\r\n    const extractedColors = ref(null)\r\n    const loadingText = ref('正在生成壁纸...')\r\n    const showDynamicPreview = ref(false)\r\n    const previewVideoUrl = ref('')\r\n    const pendingDynamicWallpaper = ref(null)\r\n    \r\n    // 配置\r\n    const config = reactive({\r\n      enabled: true,\r\n      autoGenerate: true,\r\n      opacity: 0.8,\r\n      cardOpacity: 0.3,\r\n      contrastBoost: 1.2\r\n    })\r\n    \r\n    // 服务实例\r\n    const imageService = new ImageGenerationService()\r\n    const dynamicWallpaperService = new DynamicWallpaperService()\r\n    \r\n    // 计算属性\r\n    const defaultGradient = computed(() => ({\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\r\n    }))\r\n    \r\n    const overlayStyles = computed(() => {\r\n      if (!extractedColors.value) return {}\r\n      \r\n      return {\r\n        '--glass-bg': extractedColors.value.glassBackground,\r\n        '--glass-border': extractedColors.value.glassBorder,\r\n        '--text-color': extractedColors.value.text,\r\n        '--card-opacity': config.cardOpacity\r\n      }\r\n    })\r\n    \r\n    // 方法\r\n    const generateWallpaper = async (prompt = props.scenePrompt) => {\r\n      if (!config.enabled) return\r\n      \r\n      isLoading.value = true\r\n      \r\n      try {\r\n        console.log('开始生成壁纸，提示词:', prompt)\r\n        const result = await imageService.generateWallpaper(prompt)\r\n        \r\n        if (result && result.imageUrl) {\r\n          currentWallpaper.value = {\r\n            type: 'image',\r\n            url: result.imageUrl,\r\n            prompt: result.prompt,\r\n            taskId: result.taskId\r\n          }\r\n          \r\n          // 提取颜色，传递提示词用于智能降级\r\n          await extractColors(result.imageUrl, prompt)\r\n          \r\n          // 保存到历史\r\n          imageService.saveToHistory(result)\r\n          \r\n          emit('wallpaper-changed', currentWallpaper.value)\r\n          console.log('壁纸生成成功:', result.imageUrl)\r\n        }\r\n      } catch (error) {\r\n        console.error('壁纸生成失败:', error)\r\n        // 使用默认壁纸\r\n        resetToDefault()\r\n      } finally {\r\n        isLoading.value = false\r\n      }\r\n    }\r\n    \r\n    const extractColors = async (imageUrl, prompt = props.scenePrompt, optimizedPrompt = '') => {\r\n      try {\r\n        console.log('🎨 开始AI智能配色分析...')\r\n\r\n        // 使用AI智能配色分析器\r\n        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(\r\n          imageUrl,\r\n          prompt,\r\n          optimizedPrompt\r\n        )\r\n\r\n        extractedColors.value = intelligentColors\r\n        emit('colors-extracted', intelligentColors)\r\n        console.log('🎨 AI智能配色完成:', intelligentColors)\r\n\r\n        // 如果有AI分析结果，显示详细信息\r\n        if (intelligentColors.aiAnalysis) {\r\n          console.log('🧠 AI分析结果:', {\r\n            情感氛围: intelligentColors.aiAnalysis.mood,\r\n            整体亮度: intelligentColors.aiAnalysis.brightness,\r\n            氛围描述: intelligentColors.aiAnalysis.atmosphere,\r\n            主要颜色: intelligentColors.aiAnalysis.dominantColors\r\n          })\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI配色分析失败:', error)\r\n        // 降级到传统颜色提取\r\n        try {\r\n          const colors = await ColorExtractor.extractColors(imageUrl, prompt)\r\n          extractedColors.value = colors\r\n          emit('colors-extracted', colors)\r\n          console.log('降级到传统颜色提取:', colors)\r\n        } catch (fallbackError) {\r\n          console.error('传统颜色提取也失败:', fallbackError)\r\n          // 最终降级到场景智能颜色\r\n          const fallbackColors = ColorExtractor.getSceneBasedColors(prompt)\r\n          extractedColors.value = fallbackColors\r\n          emit('colors-extracted', fallbackColors)\r\n          console.log('使用场景智能颜色:', fallbackColors)\r\n        }\r\n      }\r\n    }\r\n    \r\n    const generateNewWallpaper = () => {\r\n      generateWallpaper()\r\n    }\r\n    \r\n    const generateDynamicWallpaper = async () => {\r\n      if (!currentWallpaper.value || currentWallpaper.value.type !== 'image') {\r\n        console.warn('当前没有可用的静态壁纸，无法生成动态壁纸')\r\n        return\r\n      }\r\n\r\n      isLoading.value = true\r\n      loadingText.value = '正在生成动态壁纸...这可能需要2-3分钟，请耐心等待...'\r\n\r\n      try {\r\n        console.log('开始智能生成动态壁纸')\r\n\r\n        // 获取当前壁纸信息\r\n        const wallpaperInfo = imageService.getCurrentWallpaperInfo()\r\n        const taskId = dynamicWallpaperService.generateTaskId()\r\n\r\n        let imageFile = null\r\n        let imageUrl = null\r\n\r\n        // 优先尝试从本地路径加载文件\r\n        if (wallpaperInfo && wallpaperInfo.imagePath) {\r\n          console.log('尝试从本地路径加载文件:', wallpaperInfo.imagePath)\r\n          imageFile = await imageService.loadFileFromPath(wallpaperInfo.imagePath)\r\n        }\r\n\r\n        // 如果本地文件加载失败，使用URL模式\r\n        if (!imageFile && wallpaperInfo && wallpaperInfo.imageUrl) {\r\n          console.log('本地文件不可用，使用URL模式:', wallpaperInfo.imageUrl)\r\n          imageUrl = wallpaperInfo.imageUrl\r\n        }\r\n\r\n        // 如果都没有，尝试从当前壁纸URL创建文件\r\n        if (!imageFile && !imageUrl && currentWallpaper.value.url) {\r\n          console.log('从当前壁纸URL创建文件:', currentWallpaper.value.url)\r\n          try {\r\n            const response = await fetch(currentWallpaper.value.url)\r\n            const blob = await response.blob()\r\n            imageFile = new File([blob], 'wallpaper.png', { type: 'image/png' })\r\n          } catch (error) {\r\n            console.error('从URL创建文件失败:', error)\r\n            imageUrl = currentWallpaper.value.url\r\n          }\r\n        }\r\n\r\n        // 调用智能生成服务\r\n        const result = await dynamicWallpaperService.generateDynamicWallpaper({\r\n          imageFile: imageFile,\r\n          imageUrl: imageUrl,\r\n          taskId: taskId,\r\n          onProgress: (progress) => {\r\n            loadingText.value = progress.message\r\n            console.log(`生成进度: ${progress.percentage}% - ${progress.message}`)\r\n          }\r\n        })\r\n\r\n        if (result && result.url) {\r\n          // 保存生成的动态壁纸信息，等待用户确认\r\n          pendingDynamicWallpaper.value = {\r\n            type: 'video',\r\n            url: result.url,\r\n            prompt: currentWallpaper.value.prompt,\r\n            taskId: result.taskId\r\n          }\r\n\r\n          // 显示预览模态框\r\n          previewVideoUrl.value = result.url\r\n          showDynamicPreview.value = true\r\n\r\n          console.log('动态壁纸生成成功:', result.url)\r\n        }\r\n      } catch (error) {\r\n        console.error('动态壁纸生成失败:', error)\r\n        loadingText.value = `生成失败: ${error.message}`\r\n\r\n        // 显示错误提示\r\n        setTimeout(() => {\r\n          loadingText.value = '正在生成壁纸...'\r\n        }, 3000)\r\n      } finally {\r\n        isLoading.value = false\r\n      }\r\n    }\r\n    \r\n    const applyDynamicWallpaper = () => {\r\n      if (pendingDynamicWallpaper.value) {\r\n        // 应用动态壁纸\r\n        currentWallpaper.value = { ...pendingDynamicWallpaper.value }\r\n        emit('wallpaper-changed', currentWallpaper.value)\r\n        console.log('动态壁纸已应用:', currentWallpaper.value.url)\r\n        \r\n        // 关闭预览模态框\r\n        closeDynamicPreview()\r\n      }\r\n    }\r\n    \r\n    const closeDynamicPreview = () => {\r\n      showDynamicPreview.value = false\r\n      previewVideoUrl.value = ''\r\n      pendingDynamicWallpaper.value = null\r\n    }\r\n    \r\n    const resetToDefault = () => {\r\n      currentWallpaper.value = null\r\n      // 使用基于当前场景的智能颜色\r\n      const smartColors = ColorExtractor.getSceneBasedColors(props.scenePrompt)\r\n      extractedColors.value = smartColors\r\n      emit('colors-extracted', smartColors)\r\n      console.log('重置为智能默认颜色:', smartColors)\r\n    }\r\n    \r\n    const onConfigChange = () => {\r\n      // 保存配置到本地存储\r\n      localStorage.setItem('wallpaper_config', JSON.stringify(config))\r\n      \r\n      // 如果禁用了动态壁纸，重置为默认\r\n      if (!config.enabled) {\r\n        resetToDefault()\r\n      }\r\n    }\r\n    \r\n    const loadConfig = () => {\r\n      const saved = localStorage.getItem('wallpaper_config')\r\n      if (saved) {\r\n        Object.assign(config, JSON.parse(saved))\r\n      }\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(async () => {\r\n      loadConfig()\r\n\r\n      // 确保每次页面加载都生成壁纸（如果启用了自动生成）\r\n      if (config.enabled && config.autoGenerate) {\r\n        await nextTick()\r\n        console.log('页面加载，开始自动生成壁纸')\r\n        generateWallpaper()\r\n      } else {\r\n        resetToDefault()\r\n      }\r\n    })\r\n    \r\n    // 监听场景提示词变化\r\n    watch(() => props.scenePrompt, (newPrompt) => {\r\n      if (config.enabled && config.autoGenerate) {\r\n        generateWallpaper(newPrompt)\r\n      }\r\n    })\r\n    \r\n    return {\r\n      isLoading,\r\n      showConfig,\r\n      currentWallpaper,\r\n      extractedColors,\r\n      config,\r\n      defaultGradient,\r\n      overlayStyles,\r\n      loadingText,\r\n      showDynamicPreview,\r\n      previewVideoUrl,\r\n      generateNewWallpaper,\r\n      generateDynamicWallpaper,\r\n      applyDynamicWallpaper,\r\n      closeDynamicPreview,\r\n      resetToDefault,\r\n      onConfigChange\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dynamic-wallpaper-manager {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.wallpaper-container {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: -1; /* 确保在最底层 */\r\n}\r\n\r\n.wallpaper-image {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.wallpaper-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.wallpaper-default {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.loading-spinner {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  color: white;\r\n  font-size: 16px;\r\n}\r\n\r\n.loading-spinner i {\r\n  font-size: 32px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.content-overlay {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 5;\r\n}\r\n\r\n/* 全局CSS变量，供子组件使用 */\r\n.content-overlay :deep(.glass-card) {\r\n  background: var(--glass-bg, rgba(255, 255, 255, 0.15));\r\n  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.2));\r\n  color: var(--text-color, #ffffff);\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n}\r\n\r\n.config-toggle {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  width: 50px;\r\n  height: 50px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.config-toggle:hover,\r\n.config-toggle.active {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.config-panel {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: 20px;\r\n  width: 300px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 15px;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  z-index: 1000;\r\n  color: #333;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #666;\r\n}\r\n\r\n.config-content {\r\n  padding: 20px;\r\n}\r\n\r\n.config-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.config-item label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.config-item input[type=\"range\"] {\r\n  flex: 1;\r\n  margin: 0 10px;\r\n}\r\n\r\n.config-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.config-actions button {\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  background: #4A90E2;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.config-actions button:hover {\r\n  background: #357ABD;\r\n}\r\n\r\n.config-actions button:disabled {\r\n  background: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 动态壁纸预览模态框 */\r\n.preview-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.preview-content {\r\n  width: 90%;\r\n  max-width: 800px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.preview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0;\r\n  color: #333;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-body {\r\n  padding: 20px;\r\n}\r\n\r\n.preview-video-container {\r\n  width: 100%;\r\n  height: 400px;\r\n  background: #000;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.preview-info {\r\n  text-align: center;\r\n  color: #666;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-info p {\r\n  margin: 5px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: center;\r\n}\r\n\r\n.apply-btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 10px;\r\n  background: #4CAF50;\r\n  color: white;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.apply-btn:hover {\r\n  background: #45a049;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.cancel-btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 10px;\r\n  background: #f44336;\r\n  color: white;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: #da190b;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 加载文本样式增强 */\r\n.loading-spinner span {\r\n  font-size: 14px;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n  max-width: 300px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;;;EA6BZA,KAAK,EAAC;;;EACrBA,KAAK,EAAC;AAAiB;;;EAaTA,KAAK,EAAC;;;EACtBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EAUnBA,KAAK,EAAC;AAAa;;EAUnBA,KAAK,EAAC;AAAa;;EAYnBA,KAAK,EAAC;AAAa;;EAYnBA,KAAK,EAAC;AAAgB;;;;;EAwBAA,KAAK,EAAC;;;EAC9BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EAItBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAyB;;;EAgBjCA,KAAK,EAAC;AAAiB;;uBA5IlCC,mBAAA,CAsJM,OAtJNC,UAsJM,GArJJC,mBAAA,UAAa,EACbC,mBAAA,CAiCM;IAjCDJ,KAAK,EAAAK,eAAA,EAAC,qBAAqB;MAAA,WAAsBC,MAAA,CAAAC;IAAS;MAC7DJ,mBAAA,UAAa,EAELG,MAAA,CAAAE,gBAAgB,IAAIF,MAAA,CAAAE,gBAAgB,CAACC,IAAI,gB,cADjDR,mBAAA,CAIO;;IAFLD,KAAK,EAAC,iBAAiB;IACtBU,KAAK,EAAAC,eAAA;MAAAC,eAAA,SAA4BN,MAAA,CAAAE,gBAAgB,CAACK,GAAG;IAAA;gEAGxDV,mBAAA,UAAa,EAELG,MAAA,CAAAE,gBAAgB,IAAIF,MAAA,CAAAE,gBAAgB,CAACC,IAAI,gB,cADjDR,mBAAA,CAQS;;IANPD,KAAK,EAAC,iBAAiB;IACtBc,GAAG,EAAER,MAAA,CAAAE,gBAAgB,CAACK,GAAG;IAC1BE,QAAQ,EAAR,EAAQ;IACRC,KAAK,EAAL,EAAK;IACLC,IAAI,EAAJ,EAAI;IACJC,WAAW,EAAX;4EAGFf,mBAAA,YAAe,E,CAENG,MAAA,CAAAE,gBAAgB,I,cADzBP,mBAAA,CAIO;;IAFLD,KAAK,EAAC,mBAAmB;IACxBU,KAAK,EAAAC,eAAA,CAAEL,MAAA,CAAAa,eAAe;gEAGzBhB,mBAAA,UAAa,EACFG,MAAA,CAAAC,SAAS,I,cAApBN,mBAAA,CAKM,OALNmB,UAKM,GAJJhB,mBAAA,CAGM,OAHNiB,UAGM,G,4BAFJjB,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,4BACjCI,mBAAA,CAA8B,cAAAkB,gBAAA,CAArBhB,MAAA,CAAAiB,WAAW,iB,2DAK1BpB,mBAAA,UAAa,EACbC,mBAAA,CAEM;IAFDJ,KAAK,EAAC,iBAAiB;IAAEU,KAAK,EAAAC,eAAA,CAAEL,MAAA,CAAAkB,aAAa;MAChDC,WAAA,CAAaC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q,kBAGfzB,mBAAA,UAAa,EACFG,MAAA,CAAAuB,UAAU,I,cAArB5B,mBAAA,CA8DM,OA9DN6B,UA8DM,GA7DJ1B,mBAAA,CAGM,OAHN2B,UAGM,G,4BAFJ3B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAgE;IAAvD4B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAE5B,MAAA,CAAAuB,UAAU;IAAU7B,KAAK,EAAC;KAAY,GAAC,E,GAEzDI,mBAAA,CAwDM,OAxDN+B,UAwDM,GAvDJ/B,mBAAA,CASM,OATNgC,UASM,GARJhC,mBAAA,CAOQ,gB,gBANNA,mBAAA,CAIE;IAHAK,IAAI,EAAC,UAAU;+DACNH,MAAA,CAAA+B,MAAM,CAACC,OAAO,GAAAJ,MAAA;IACtBK,QAAM,EAAAN,MAAA,QAAAA,MAAA,UAAAO,IAAA,KAAElC,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAmC,cAAA,IAAAD,IAAA,CAAc;qEADdlC,MAAA,CAAA+B,MAAM,CAACC,OAAO,E,gDAEvB,UAEJ,oB,KAEFlC,mBAAA,CASM,OATNsC,UASM,GARJtC,mBAAA,CAOQ,gB,gBANNA,mBAAA,CAIE;IAHAK,IAAI,EAAC,UAAU;+DACNH,MAAA,CAAA+B,MAAM,CAACM,YAAY,GAAAT,MAAA;IAC3BK,QAAM,EAAAN,MAAA,QAAAA,MAAA,UAAAO,IAAA,KAAElC,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAmC,cAAA,IAAAD,IAAA,CAAc;qEADdlC,MAAA,CAAA+B,MAAM,CAACM,YAAY,E,gDAE5B,UAEJ,oB,KAEFvC,mBAAA,CAWM,OAXNwC,WAWM,G,4BAVJxC,mBAAA,CAAqB,eAAd,QAAM,qB,gBACbA,mBAAA,CAOE;IANAK,IAAI,EAAC,OAAO;IACZoC,GAAG,EAAC,KAAK;IACTC,GAAG,EAAC,GAAG;IACPC,IAAI,EAAC,KAAK;+DACDzC,MAAA,CAAA+B,MAAM,CAACW,OAAO,GAAAd,MAAA;IACtBe,OAAK,EAAAhB,MAAA,QAAAA,MAAA,UAAAO,IAAA,KAAElC,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAmC,cAAA,IAAAD,IAAA,CAAc;iEADblC,MAAA,CAAA+B,MAAM,CAACW,OAAO,E,GAGzB5C,mBAAA,CAAiC,cAAAkB,gBAAA,CAAxBhB,MAAA,CAAA+B,MAAM,CAACW,OAAO,iB,GAEzB5C,mBAAA,CAWM,OAXN8C,WAWM,G,4BAVJ9C,mBAAA,CAAqB,eAAd,QAAM,qB,gBACbA,mBAAA,CAOE;IANAK,IAAI,EAAC,OAAO;IACZoC,GAAG,EAAC,KAAK;IACTC,GAAG,EAAC,KAAK;IACTC,IAAI,EAAC,KAAK;+DACDzC,MAAA,CAAA+B,MAAM,CAACc,WAAW,GAAAjB,MAAA;IAC1Be,OAAK,EAAAhB,MAAA,QAAAA,MAAA,UAAAO,IAAA,KAAElC,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAmC,cAAA,IAAAD,IAAA,CAAc;iEADblC,MAAA,CAAA+B,MAAM,CAACc,WAAW,E,GAG7B/C,mBAAA,CAAqC,cAAAkB,gBAAA,CAA5BhB,MAAA,CAAA+B,MAAM,CAACc,WAAW,iB,GAE7B/C,mBAAA,CAUM,OAVNgD,WAUM,GATJhD,mBAAA,CAES;IAFA4B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAO,IAAA,KAAElC,MAAA,CAAA+C,oBAAA,IAAA/C,MAAA,CAAA+C,oBAAA,IAAAb,IAAA,CAAoB;IAAGc,QAAQ,EAAEhD,MAAA,CAAAC;KAAW,UAE5D,iBAAAgD,WAAA,GACAnD,mBAAA,CAES;IAFA4B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAO,IAAA,KAAElC,MAAA,CAAAkD,wBAAA,IAAAlD,MAAA,CAAAkD,wBAAA,IAAAhB,IAAA,CAAwB;IAAGc,QAAQ,EAAEhD,MAAA,CAAAC,SAAS,KAAKD,MAAA,CAAAE;KAAkB,UAErF,iBAAAiD,WAAA,GACArD,mBAAA,CAES;IAFA4B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAO,IAAA,KAAElC,MAAA,CAAAoD,cAAA,IAAApD,MAAA,CAAAoD,cAAA,IAAAlB,IAAA,CAAc;KAAE,QAEhC,E,4CAKNrC,mBAAA,UAAa,EACbC,mBAAA,CAMS;IALPJ,KAAK,EAAAK,eAAA,EAAC,eAAe;MAAA,UAEDC,MAAA,CAAAuB;IAAU;IAD7BG,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAE5B,MAAA,CAAAuB,UAAU,IAAIvB,MAAA,CAAAuB,UAAU;kCAGhCzB,mBAAA,CAA0B;IAAvBJ,KAAK,EAAC;EAAY,0B,mBAGvBG,mBAAA,eAAkB,EACPG,MAAA,CAAAqD,kBAAkB,I,cAA7B1D,mBAAA,CAgCM,OAhCN2D,WAgCM,GA/BJxD,mBAAA,CA8BM,OA9BNyD,WA8BM,GA7BJzD,mBAAA,CAGM,OAHN0D,WAGM,G,4BAFJ1D,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAiE;IAAxD4B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAO,IAAA,KAAElC,MAAA,CAAAyD,mBAAA,IAAAzD,MAAA,CAAAyD,mBAAA,IAAAvB,IAAA,CAAmB;IAAExC,KAAK,EAAC;KAAY,GAAC,E,GAE1DI,mBAAA,CAgBM,OAhBN4D,WAgBM,GAfJ5D,mBAAA,CAUM,OAVN6D,WAUM,GARI3D,MAAA,CAAA4D,eAAe,I,cADvBjE,mBAAA,CAQS;;IANPD,KAAK,EAAC,eAAe;IACpBc,GAAG,EAAER,MAAA,CAAA4D,eAAe;IACrBnD,QAAQ,EAAR,EAAQ;IACRC,KAAK,EAAL,EAAK;IACLC,IAAI,EAAJ,EAAI;IACJC,WAAW,EAAX;2GAGJd,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAc,IACvBI,mBAAA,CAAiB,WAAd,YAAU,GACbA,mBAAA,CAA0B,WAAvB,qBAAmB,E,uBAG1BA,mBAAA,CAOM,OAPN+D,WAOM,GANJ/D,mBAAA,CAES;IAFA4B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAO,IAAA,KAAElC,MAAA,CAAA8D,qBAAA,IAAA9D,MAAA,CAAA8D,qBAAA,IAAA5B,IAAA,CAAqB;IAAExC,KAAK,EAAC;kCAC3CI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,iBAAK,UAC9B,mB,IACAI,mBAAA,CAES;IAFA4B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAO,IAAA,KAAElC,MAAA,CAAAyD,mBAAA,IAAAzD,MAAA,CAAAyD,mBAAA,IAAAvB,IAAA,CAAmB;IAAExC,KAAK,EAAC;kCACzCI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,2B,iBAAK,MAC9B,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}