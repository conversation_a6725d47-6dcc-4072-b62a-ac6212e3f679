{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n// VPA数字人状态管理Store\nimport { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion'); // companion, interactive, restricted\n  const avatar = ref('vpa2.gif'); // 当前使用的头像\n  const isActive = ref(true);\n  const isListening = ref(false);\n  const isSpeaking = ref(false);\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning');\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  });\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  });\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral'); // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle'); // idle, listening, speaking, thinking, gesturing\n\n  // === 对话历史 ===\n  const conversationHistory = ref([]);\n  const currentConversation = ref(null);\n\n  // === 新增：组件注册系统 ===\n  const registeredWidgets = ref(new Map());\n  const activeWidgets = ref(new Set());\n\n  // === 新增：扩展状态 ===\n  const isOnline = ref(true);\n  const isBusy = ref(false);\n  const vpaName = ref('小智');\n  const currentEmotion = ref('neutral'); // 兼容新组件系统\n\n  // === 新增：主题和视觉 ===\n  const themeColors = ref({\n    primary: '#4a90e2',\n    secondary: '#67b7dc',\n    accent: '#f39c12'\n  });\n\n  // === 新增：动画状态 ===\n  const animationState = ref({\n    currentAnimation: null,\n    isAnimating: false,\n    visualEffects: {\n      glowEnabled: true,\n      particlesEnabled: false,\n      breathingAnimation: true\n    }\n  });\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    };\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    };\n  });\n  const contextualGreeting = computed(() => {\n    const {\n      timeOfDay,\n      hasChildren\n    } = sceneContext.value;\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    };\n    return greetings[timeOfDay] || greetings.morning;\n  });\n\n  // === 新增：计算属性 ===\n  const canInteract = computed(() => {\n    return isActive.value && isOnline.value && !isBusy.value;\n  });\n  const currentMoodDescription = computed(() => {\n    const moodMap = {\n      happy: '开心愉悦',\n      sad: '略显沮丧',\n      excited: '兴奋激动',\n      neutral: '平静自然',\n      thinking: '深思熟虑',\n      surprised: '惊讶好奇'\n    };\n    return moodMap[currentEmotion.value] || '未知状态';\n  });\n  const currentAvatarResource = computed(() => {\n    const avatarMap = {\n      companion: 'vpa2.gif',\n      interactive: 'vpn1.gif',\n      restricted: 'vpa2.gif'\n    };\n    return avatarMap[currentMode.value] || 'vpa2.gif';\n  });\n  const activeWidgetCount = computed(() => activeWidgets.value.size);\n\n  // === 方法 ===\n\n  // 切换VPA模式\n  const switchMode = newMode => {\n    currentMode.value = newMode;\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif';\n\n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false;\n    } else {\n      isActive.value = true;\n    }\n  };\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName;\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n\n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName);\n  };\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = sceneName => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    };\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus;\n    currentExpression.value = behavior.expression;\n    userPreferences.value.aiPersonality = behavior.personality;\n  };\n\n  // 更新用户偏好\n  const updatePreferences = preferences => {\n    userPreferences.value = {\n      ...userPreferences.value,\n      ...preferences\n    };\n  };\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    };\n    currentAction.value = 'listening';\n    isListening.value = true;\n  };\n\n  // 添加对话消息\n  const addMessage = message => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      });\n    }\n  };\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      });\n      currentConversation.value = null;\n    }\n    currentAction.value = 'idle';\n    isListening.value = false;\n    isSpeaking.value = false;\n  };\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true;\n    currentAction.value = 'speaking';\n    currentExpression.value = emotion;\n\n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false;\n      currentAction.value = 'idle';\n      currentExpression.value = 'neutral';\n    }, text.length * 100); // 根据文本长度估算说话时间\n  };\n\n  // 设置表情\n  const setExpression = expression => {\n    currentExpression.value = expression;\n  };\n\n  // 设置动作\n  const setAction = action => {\n    currentAction.value = action;\n  };\n\n  // 情绪感知\n  const detectEmotion = () => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired'];\n    return emotions[Math.floor(Math.random() * emotions.length)];\n  };\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    };\n    sceneContext.value = {\n      ...sceneContext.value,\n      ...context\n    };\n    return context;\n  };\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }));\n  };\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa');\n    if (saved) {\n      try {\n        const data = JSON.parse(saved);\n        currentMode.value = data.currentMode || 'companion';\n        userPreferences.value = {\n          ...userPreferences.value,\n          ...data.userPreferences\n        };\n        sceneContext.value = {\n          ...sceneContext.value,\n          ...data.sceneContext\n        };\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error);\n      }\n    }\n  };\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState();\n    analyzeContext();\n\n    // 设置定期保存\n    setInterval(saveVPAState, 30000); // 每30秒保存一次\n  };\n\n  // === 新增：组件管理方法 ===\n\n  // 注册组件\n  const registerWidget = (widgetId, widgetInfo) => {\n    registeredWidgets.value.set(widgetId, {\n      ...widgetInfo,\n      registeredAt: new Date(),\n      isActive: true\n    });\n    activeWidgets.value.add(widgetId);\n    console.log(`📱 VPA组件已注册: ${widgetId}`, widgetInfo);\n    return true;\n  };\n\n  // 注销组件\n  const unregisterWidget = widgetId => {\n    registeredWidgets.value.delete(widgetId);\n    activeWidgets.value.delete(widgetId);\n    console.log(`📱 VPA组件已注销: ${widgetId}`);\n    return true;\n  };\n\n  // 通知所有组件\n  const notifyWidgets = (eventType, data) => {\n    activeWidgets.value.forEach(widgetId => {\n      const widget = registeredWidgets.value.get(widgetId);\n      if (widget && widget.onStateChange) {\n        try {\n          widget.onStateChange(eventType, data);\n        } catch (error) {\n          console.error(`组件 ${widgetId} 状态更新失败:`, error);\n        }\n      }\n    });\n  };\n\n  // === 新增：兼容新组件系统的方法 ===\n\n  // 设置模式（兼容新组件）\n  const setMode = mode => {\n    if (['companion', 'interactive', 'restricted'].includes(mode)) {\n      const oldMode = currentMode.value;\n      switchMode(mode); // 调用原有方法\n\n      // 通知所有注册的组件\n      notifyWidgets('modeChange', {\n        newMode: mode,\n        oldMode\n      });\n      console.log(`VPA模式切换到: ${mode}`);\n    }\n  };\n\n  // 设置情绪（兼容新组件）\n  const setEmotion = (emotion, duration = 3000) => {\n    if (['happy', 'sad', 'excited', 'neutral', 'thinking', 'surprised'].includes(emotion)) {\n      const oldEmotion = currentEmotion.value;\n      currentEmotion.value = emotion;\n      currentExpression.value = emotion; // 同步到表情\n\n      // 通知所有注册的组件\n      notifyWidgets('emotionChange', {\n        newEmotion: emotion,\n        oldEmotion\n      });\n      console.log(`VPA情绪变化: ${emotion}`);\n\n      // 自动恢复到neutral状态\n      if (emotion !== 'neutral' && duration > 0) {\n        setTimeout(() => {\n          if (currentEmotion.value === emotion) {\n            setEmotion('neutral', 0);\n          }\n        }, duration);\n      }\n    }\n  };\n\n  // 切换语音模式\n  const toggleVoiceMode = enabled => {\n    isListening.value = enabled;\n    if (enabled) {\n      setEmotion('thinking', 0); // 持续thinking状态\n    } else {\n      setEmotion('neutral');\n    }\n  };\n\n  // 设置交互模式\n  const setInteractionMode = interactive => {\n    if (interactive) {\n      setMode('interactive');\n      setEmotion('excited', 2000);\n    } else {\n      setMode('companion');\n      setEmotion('neutral');\n    }\n  };\n\n  // 打开聊天面板\n  const openChatPanel = () => {\n    setMode('interactive');\n    setEmotion('excited', 1000);\n    console.log('打开VPA聊天面板');\n  };\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useVPAStore", "currentMode", "avatar", "isActive", "isListening", "isSpeaking", "currentScene", "sceneContext", "timeOfDay", "weather", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSchoolDay", "userMood", "passengers", "userPreferences", "theme", "cardSizes", "aiPersonality", "voiceEnabled", "language", "currentExpression", "currentAction", "conversationHistory", "currentConversation", "registeredWidgets", "Map", "activeWidgets", "Set", "isOnline", "isBusy", "vpaName", "currentEmotion", "themeColors", "primary", "secondary", "accent", "animationState", "currentAnimation", "isAnimating", "visualEffects", "glowEnabled", "particlesEnabled", "breathingAnimation", "vpaDisplayConfig", "expressions", "expression", "value", "neutral", "mode", "contextualGreeting", "greetings", "morning", "afternoon", "evening", "night", "canInteract", "currentMoodDescription", "moodMap", "happy", "sad", "excited", "thinking", "surprised", "currentAvatarResource", "avatarMap", "companion", "interactive", "restricted", "activeWidgetCount", "size", "switchMode", "newMode", "updateScene", "scene<PERSON><PERSON>", "context", "adjustVPABehavior", "scene<PERSON><PERSON><PERSON><PERSON>", "personality", "priority", "behavior", "focus", "updatePreferences", "preferences", "startConversation", "topic", "id", "Date", "now", "startTime", "messages", "addMessage", "message", "push", "timestamp", "endConversation", "endTime", "speak", "text", "emotion", "setTimeout", "length", "setExpression", "setAction", "action", "detectEmotion", "emotions", "Math", "floor", "random", "analyzeContext", "location", "traffic", "getHours", "saveVPAState", "localStorage", "setItem", "JSON", "stringify", "loadVPAState", "saved", "getItem", "data", "parse", "error", "console", "warn", "initializeVPA", "setInterval", "registerWidget", "widgetId", "widgetInfo", "set", "registeredAt", "add", "log", "unregisterWidget", "delete", "notifyWidgets", "eventType", "for<PERSON>ach", "widget", "get", "onStateChange", "setMode", "includes", "oldMode", "setEmotion", "duration", "oldEmotion", "newEmotion", "toggleVoiceMode", "enabled", "setInteractionMode", "openChatPanel"], "sources": ["F:/工作/theme/ai-hmi/src/store/modules/vpa.js"], "sourcesContent": ["// VPA数字人状态管理Store\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useVPAStore = defineStore('vpa', () => {\n  // === VPA状态 ===\n  const currentMode = ref('companion') // companion, interactive, restricted\n  const avatar = ref('vpa2.gif') // 当前使用的头像\n  const isActive = ref(true)\n  const isListening = ref(false)\n  const isSpeaking = ref(false)\n\n  // === 场景状态 ===\n  const currentScene = ref('commute_morning')\n  const sceneContext = ref({\n    timeOfDay: 'morning',\n    weather: 'sunny',\n    hasChildren: false,\n    isSchoolDay: true,\n    userMood: 'neutral',\n    passengers: []\n  })\n\n  // === 用户偏好 ===\n  const userPreferences = ref({\n    theme: 'glassmorphism',\n    cardSizes: {},\n    aiPersonality: 'friendly',\n    voiceEnabled: true,\n    language: 'zh-CN'\n  })\n\n  // === VPA表情和动作状态 ===\n  const currentExpression = ref('neutral') // neutral, happy, thinking, concerned, excited\n  const currentAction = ref('idle') // idle, listening, speaking, thinking, gesturing\n\n  // === 对话历史 ===\n  const conversationHistory = ref([])\n  const currentConversation = ref(null)\n\n  // === 新增：组件注册系统 ===\n  const registeredWidgets = ref(new Map())\n  const activeWidgets = ref(new Set())\n\n  // === 新增：扩展状态 ===\n  const isOnline = ref(true)\n  const isBusy = ref(false)\n  const vpaName = ref('小智')\n  const currentEmotion = ref('neutral') // 兼容新组件系统\n\n  // === 新增：主题和视觉 ===\n  const themeColors = ref({\n    primary: '#4a90e2',\n    secondary: '#67b7dc',\n    accent: '#f39c12'\n  })\n\n  // === 新增：动画状态 ===\n  const animationState = ref({\n    currentAnimation: null,\n    isAnimating: false,\n    visualEffects: {\n      glowEnabled: true,\n      particlesEnabled: false,\n      breathingAnimation: true\n    }\n  })\n\n  // === 计算属性 ===\n  const vpaDisplayConfig = computed(() => {\n    const expressions = {\n      'neutral': '(^.^)',\n      'happy': '(^o^)',\n      'thinking': '(-.-)zzZ',\n      'concerned': '(>_<)',\n      'excited': '(★^O^★)',\n      'listening': '(◕‿◕)',\n      'speaking': '(^▽^)'\n    }\n\n    return {\n      expression: expressions[currentExpression.value] || expressions.neutral,\n      avatar: avatar.value,\n      mode: currentMode.value,\n      isActive: isActive.value\n    }\n  })\n\n  const contextualGreeting = computed(() => {\n    const { timeOfDay, hasChildren } = sceneContext.value\n    const greetings = {\n      morning: hasChildren ? '早上好！准备送小朋友上学了吗？' : '早上好！新的一天开始了！',\n      afternoon: '下午好！今天过得怎么样？',\n      evening: '晚上好！辛苦了一天，放松一下吧！',\n      night: '夜深了，注意安全驾驶哦！'\n    }\n\n    return greetings[timeOfDay] || greetings.morning\n  })\n\n  // === 新增：计算属性 ===\n  const canInteract = computed(() => {\n    return isActive.value && isOnline.value && !isBusy.value\n  })\n\n  const currentMoodDescription = computed(() => {\n    const moodMap = {\n      happy: '开心愉悦',\n      sad: '略显沮丧',\n      excited: '兴奋激动',\n      neutral: '平静自然',\n      thinking: '深思熟虑',\n      surprised: '惊讶好奇'\n    }\n    return moodMap[currentEmotion.value] || '未知状态'\n  })\n\n  const currentAvatarResource = computed(() => {\n    const avatarMap = {\n      companion: 'vpa2.gif',\n      interactive: 'vpn1.gif',\n      restricted: 'vpa2.gif'\n    }\n    return avatarMap[currentMode.value] || 'vpa2.gif'\n  })\n\n  const activeWidgetCount = computed(() => activeWidgets.value.size)\n\n  // === 方法 ===\n  \n  // 切换VPA模式\n  const switchMode = (newMode) => {\n    currentMode.value = newMode\n    avatar.value = newMode === 'companion' ? 'vpa2.gif' : 'vpn1.gif'\n    \n    // 根据模式调整行为\n    if (newMode === 'restricted') {\n      // 访客模式：限制功能\n      isActive.value = false\n    } else {\n      isActive.value = true\n    }\n  }\n\n  // 更新场景\n  const updateScene = (sceneName, context = {}) => {\n    currentScene.value = sceneName\n    sceneContext.value = { ...sceneContext.value, ...context }\n    \n    // 根据场景调整VPA行为\n    adjustVPABehavior(sceneName)\n  }\n\n  // 根据场景调整VPA行为\n  const adjustVPABehavior = (sceneName) => {\n    const sceneBehaviors = {\n      'family': {\n        personality: 'caring',\n        expression: 'happy',\n        priority: ['child_safety', 'education', 'entertainment']\n      },\n      'focus': {\n        personality: 'professional',\n        expression: 'neutral',\n        priority: ['efficiency', 'schedule', 'work_support']\n      },\n      'minimal': {\n        personality: 'calm',\n        expression: 'neutral',\n        priority: ['safety', 'basic_info']\n      },\n      'entertainment': {\n        personality: 'cheerful',\n        expression: 'excited',\n        priority: ['entertainment', 'relaxation', 'social']\n      }\n    }\n\n    const behavior = sceneBehaviors[sceneName] || sceneBehaviors.focus\n    currentExpression.value = behavior.expression\n    userPreferences.value.aiPersonality = behavior.personality\n  }\n\n  // 更新用户偏好\n  const updatePreferences = (preferences) => {\n    userPreferences.value = { ...userPreferences.value, ...preferences }\n  }\n\n  // 开始对话\n  const startConversation = (topic = null) => {\n    currentConversation.value = {\n      id: Date.now(),\n      topic,\n      startTime: new Date(),\n      messages: []\n    }\n    currentAction.value = 'listening'\n    isListening.value = true\n  }\n\n  // 添加对话消息\n  const addMessage = (message) => {\n    if (currentConversation.value) {\n      currentConversation.value.messages.push({\n        ...message,\n        timestamp: new Date()\n      })\n    }\n  }\n\n  // 结束对话\n  const endConversation = () => {\n    if (currentConversation.value) {\n      conversationHistory.value.push({\n        ...currentConversation.value,\n        endTime: new Date()\n      })\n      currentConversation.value = null\n    }\n    currentAction.value = 'idle'\n    isListening.value = false\n    isSpeaking.value = false\n  }\n\n  // VPA说话\n  const speak = (text, emotion = 'neutral') => {\n    isSpeaking.value = true\n    currentAction.value = 'speaking'\n    currentExpression.value = emotion\n    \n    // 模拟说话时长\n    setTimeout(() => {\n      isSpeaking.value = false\n      currentAction.value = 'idle'\n      currentExpression.value = 'neutral'\n    }, text.length * 100) // 根据文本长度估算说话时间\n  }\n\n  // 设置表情\n  const setExpression = (expression) => {\n    currentExpression.value = expression\n  }\n\n  // 设置动作\n  const setAction = (action) => {\n    currentAction.value = action\n  }\n\n  // 情绪感知\n  const detectEmotion = () => {\n    // 这里应该集成真实的情绪识别API\n    // 现在返回模拟数据\n    const emotions = ['happy', 'sad', 'neutral', 'excited', 'tired']\n    return emotions[Math.floor(Math.random() * emotions.length)]\n  }\n\n  // 上下文感知\n  const analyzeContext = () => {\n    const context = {\n      location: 'unknown',\n      weather: 'unknown',\n      traffic: 'unknown',\n      timeOfDay: new Date().getHours() < 12 ? 'morning' : \n                 new Date().getHours() < 18 ? 'afternoon' : 'evening'\n    }\n    \n    sceneContext.value = { ...sceneContext.value, ...context }\n    return context\n  }\n\n  // 保存VPA状态\n  const saveVPAState = () => {\n    localStorage.setItem('ai-hmi-vpa', JSON.stringify({\n      currentMode: currentMode.value,\n      userPreferences: userPreferences.value,\n      sceneContext: sceneContext.value\n    }))\n  }\n\n  // 加载VPA状态\n  const loadVPAState = () => {\n    const saved = localStorage.getItem('ai-hmi-vpa')\n    if (saved) {\n      try {\n        const data = JSON.parse(saved)\n        currentMode.value = data.currentMode || 'companion'\n        userPreferences.value = { ...userPreferences.value, ...data.userPreferences }\n        sceneContext.value = { ...sceneContext.value, ...data.sceneContext }\n      } catch (error) {\n        console.warn('Failed to load VPA state:', error)\n      }\n    }\n  }\n\n  // 初始化VPA\n  const initializeVPA = () => {\n    loadVPAState()\n    analyzeContext()\n\n    // 设置定期保存\n    setInterval(saveVPAState, 30000) // 每30秒保存一次\n  }\n\n  // === 新增：组件管理方法 ===\n\n  // 注册组件\n  const registerWidget = (widgetId, widgetInfo) => {\n    registeredWidgets.value.set(widgetId, {\n      ...widgetInfo,\n      registeredAt: new Date(),\n      isActive: true\n    })\n\n    activeWidgets.value.add(widgetId)\n    console.log(`📱 VPA组件已注册: ${widgetId}`, widgetInfo)\n    return true\n  }\n\n  // 注销组件\n  const unregisterWidget = (widgetId) => {\n    registeredWidgets.value.delete(widgetId)\n    activeWidgets.value.delete(widgetId)\n    console.log(`📱 VPA组件已注销: ${widgetId}`)\n    return true\n  }\n\n  // 通知所有组件\n  const notifyWidgets = (eventType, data) => {\n    activeWidgets.value.forEach(widgetId => {\n      const widget = registeredWidgets.value.get(widgetId)\n      if (widget && widget.onStateChange) {\n        try {\n          widget.onStateChange(eventType, data)\n        } catch (error) {\n          console.error(`组件 ${widgetId} 状态更新失败:`, error)\n        }\n      }\n    })\n  }\n\n  // === 新增：兼容新组件系统的方法 ===\n\n  // 设置模式（兼容新组件）\n  const setMode = (mode) => {\n    if (['companion', 'interactive', 'restricted'].includes(mode)) {\n      const oldMode = currentMode.value\n      switchMode(mode) // 调用原有方法\n\n      // 通知所有注册的组件\n      notifyWidgets('modeChange', { newMode: mode, oldMode })\n\n      console.log(`VPA模式切换到: ${mode}`)\n    }\n  }\n\n  // 设置情绪（兼容新组件）\n  const setEmotion = (emotion, duration = 3000) => {\n    if (['happy', 'sad', 'excited', 'neutral', 'thinking', 'surprised'].includes(emotion)) {\n      const oldEmotion = currentEmotion.value\n      currentEmotion.value = emotion\n      currentExpression.value = emotion // 同步到表情\n\n      // 通知所有注册的组件\n      notifyWidgets('emotionChange', { newEmotion: emotion, oldEmotion })\n\n      console.log(`VPA情绪变化: ${emotion}`)\n\n      // 自动恢复到neutral状态\n      if (emotion !== 'neutral' && duration > 0) {\n        setTimeout(() => {\n          if (currentEmotion.value === emotion) {\n            setEmotion('neutral', 0)\n          }\n        }, duration)\n      }\n    }\n  }\n\n  // 切换语音模式\n  const toggleVoiceMode = (enabled) => {\n    isListening.value = enabled\n    if (enabled) {\n      setEmotion('thinking', 0) // 持续thinking状态\n    } else {\n      setEmotion('neutral')\n    }\n  }\n\n  // 设置交互模式\n  const setInteractionMode = (interactive) => {\n    if (interactive) {\n      setMode('interactive')\n      setEmotion('excited', 2000)\n    } else {\n      setMode('companion')\n      setEmotion('neutral')\n    }\n  }\n\n  // 打开聊天面板\n  const openChatPanel = () => {\n    setMode('interactive')\n    setEmotion('excited', 1000)\n    console.log('打开VPA聊天面板')\n  }\n\n  return {\n    // 状态\n    currentMode,\n    avatar,\n    isActive,\n    isListening,\n    isSpeaking,\n    currentScene,\n    sceneContext,\n    userPreferences,\n    currentExpression,\n    currentAction,\n    conversationHistory,\n    currentConversation,\n    \n    // 计算属性\n    vpaDisplayConfig,\n    contextualGreeting,\n    \n    // 方法\n    switchMode,\n    updateScene,\n    adjustVPABehavior,\n    updatePreferences,\n    startConversation,\n    addMessage,\n    endConversation,\n    speak,\n    setExpression,\n    setAction,\n    detectEmotion,\n    analyzeContext,\n    saveVPAState,\n    loadVPAState,\n    initializeVPA\n  }\n})\n"], "mappings": ";;;;;;;;;;AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAEnC,OAAO,MAAMC,WAAW,GAAGH,WAAW,CAAC,KAAK,EAAE,MAAM;EAClD;EACA,MAAMI,WAAW,GAAGH,GAAG,CAAC,WAAW,CAAC,EAAC;EACrC,MAAMI,MAAM,GAAGJ,GAAG,CAAC,UAAU,CAAC,EAAC;EAC/B,MAAMK,QAAQ,GAAGL,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMM,WAAW,GAAGN,GAAG,CAAC,KAAK,CAAC;EAC9B,MAAMO,UAAU,GAAGP,GAAG,CAAC,KAAK,CAAC;;EAE7B;EACA,MAAMQ,YAAY,GAAGR,GAAG,CAAC,iBAAiB,CAAC;EAC3C,MAAMS,YAAY,GAAGT,GAAG,CAAC;IACvBU,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGhB,GAAG,CAAC;IAC1BiB,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE,CAAC,CAAC;IACbC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGtB,GAAG,CAAC,SAAS,CAAC,EAAC;EACzC,MAAMuB,aAAa,GAAGvB,GAAG,CAAC,MAAM,CAAC,EAAC;;EAElC;EACA,MAAMwB,mBAAmB,GAAGxB,GAAG,CAAC,EAAE,CAAC;EACnC,MAAMyB,mBAAmB,GAAGzB,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM0B,iBAAiB,GAAG1B,GAAG,CAAC,IAAI2B,GAAG,CAAC,CAAC,CAAC;EACxC,MAAMC,aAAa,GAAG5B,GAAG,CAAC,IAAI6B,GAAG,CAAC,CAAC,CAAC;;EAEpC;EACA,MAAMC,QAAQ,GAAG9B,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAM+B,MAAM,GAAG/B,GAAG,CAAC,KAAK,CAAC;EACzB,MAAMgC,OAAO,GAAGhC,GAAG,CAAC,IAAI,CAAC;EACzB,MAAMiC,cAAc,GAAGjC,GAAG,CAAC,SAAS,CAAC,EAAC;;EAEtC;EACA,MAAMkC,WAAW,GAAGlC,GAAG,CAAC;IACtBmC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAGtC,GAAG,CAAC;IACzBuC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE;MACbC,WAAW,EAAE,IAAI;MACjBC,gBAAgB,EAAE,KAAK;MACvBC,kBAAkB,EAAE;IACtB;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAG5C,QAAQ,CAAC,MAAM;IACtC,MAAM6C,WAAW,GAAG;MAClB,SAAS,EAAE,OAAO;MAClB,OAAO,EAAE,OAAO;MAChB,UAAU,EAAE,UAAU;MACtB,WAAW,EAAE,OAAO;MACpB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE;IACd,CAAC;IAED,OAAO;MACLC,UAAU,EAAED,WAAW,CAACxB,iBAAiB,CAAC0B,KAAK,CAAC,IAAIF,WAAW,CAACG,OAAO;MACvE7C,MAAM,EAAEA,MAAM,CAAC4C,KAAK;MACpBE,IAAI,EAAE/C,WAAW,CAAC6C,KAAK;MACvB3C,QAAQ,EAAEA,QAAQ,CAAC2C;IACrB,CAAC;EACH,CAAC,CAAC;EAEF,MAAMG,kBAAkB,GAAGlD,QAAQ,CAAC,MAAM;IACxC,MAAM;MAAES,SAAS;MAAEE;IAAY,CAAC,GAAGH,YAAY,CAACuC,KAAK;IACrD,MAAMI,SAAS,GAAG;MAChBC,OAAO,EAAEzC,WAAW,GAAG,iBAAiB,GAAG,cAAc;MACzD0C,SAAS,EAAE,cAAc;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,KAAK,EAAE;IACT,CAAC;IAED,OAAOJ,SAAS,CAAC1C,SAAS,CAAC,IAAI0C,SAAS,CAACC,OAAO;EAClD,CAAC,CAAC;;EAEF;EACA,MAAMI,WAAW,GAAGxD,QAAQ,CAAC,MAAM;IACjC,OAAOI,QAAQ,CAAC2C,KAAK,IAAIlB,QAAQ,CAACkB,KAAK,IAAI,CAACjB,MAAM,CAACiB,KAAK;EAC1D,CAAC,CAAC;EAEF,MAAMU,sBAAsB,GAAGzD,QAAQ,CAAC,MAAM;IAC5C,MAAM0D,OAAO,GAAG;MACdC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE,MAAM;MACfb,OAAO,EAAE,MAAM;MACfc,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,OAAO,CAAC1B,cAAc,CAACe,KAAK,CAAC,IAAI,MAAM;EAChD,CAAC,CAAC;EAEF,MAAMiB,qBAAqB,GAAGhE,QAAQ,CAAC,MAAM;IAC3C,MAAMiE,SAAS,GAAG;MAChBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,UAAU;MACvBC,UAAU,EAAE;IACd,CAAC;IACD,OAAOH,SAAS,CAAC/D,WAAW,CAAC6C,KAAK,CAAC,IAAI,UAAU;EACnD,CAAC,CAAC;EAEF,MAAMsB,iBAAiB,GAAGrE,QAAQ,CAAC,MAAM2B,aAAa,CAACoB,KAAK,CAACuB,IAAI,CAAC;;EAElE;;EAEA;EACA,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9BtE,WAAW,CAAC6C,KAAK,GAAGyB,OAAO;IAC3BrE,MAAM,CAAC4C,KAAK,GAAGyB,OAAO,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;;IAEhE;IACA,IAAIA,OAAO,KAAK,YAAY,EAAE;MAC5B;MACApE,QAAQ,CAAC2C,KAAK,GAAG,KAAK;IACxB,CAAC,MAAM;MACL3C,QAAQ,CAAC2C,KAAK,GAAG,IAAI;IACvB;EACF,CAAC;;EAED;EACA,MAAM0B,WAAW,GAAGA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/CpE,YAAY,CAACwC,KAAK,GAAG2B,SAAS;IAC9BlE,YAAY,CAACuC,KAAK,GAAG;MAAE,GAAGvC,YAAY,CAACuC,KAAK;MAAE,GAAG4B;IAAQ,CAAC;;IAE1D;IACAC,iBAAiB,CAACF,SAAS,CAAC;EAC9B,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIF,SAAS,IAAK;IACvC,MAAMG,cAAc,GAAG;MACrB,QAAQ,EAAE;QACRC,WAAW,EAAE,QAAQ;QACrBhC,UAAU,EAAE,OAAO;QACnBiC,QAAQ,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe;MACzD,CAAC;MACD,OAAO,EAAE;QACPD,WAAW,EAAE,cAAc;QAC3BhC,UAAU,EAAE,SAAS;QACrBiC,QAAQ,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,cAAc;MACrD,CAAC;MACD,SAAS,EAAE;QACTD,WAAW,EAAE,MAAM;QACnBhC,UAAU,EAAE,SAAS;QACrBiC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACnC,CAAC;MACD,eAAe,EAAE;QACfD,WAAW,EAAE,UAAU;QACvBhC,UAAU,EAAE,SAAS;QACrBiC,QAAQ,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,QAAQ;MACpD;IACF,CAAC;IAED,MAAMC,QAAQ,GAAGH,cAAc,CAACH,SAAS,CAAC,IAAIG,cAAc,CAACI,KAAK;IAClE5D,iBAAiB,CAAC0B,KAAK,GAAGiC,QAAQ,CAAClC,UAAU;IAC7C/B,eAAe,CAACgC,KAAK,CAAC7B,aAAa,GAAG8D,QAAQ,CAACF,WAAW;EAC5D,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IACzCpE,eAAe,CAACgC,KAAK,GAAG;MAAE,GAAGhC,eAAe,CAACgC,KAAK;MAAE,GAAGoC;IAAY,CAAC;EACtE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,GAAG,IAAI,KAAK;IAC1C7D,mBAAmB,CAACuB,KAAK,GAAG;MAC1BuC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdH,KAAK;MACLI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC;MACrBG,QAAQ,EAAE;IACZ,CAAC;IACDpE,aAAa,CAACyB,KAAK,GAAG,WAAW;IACjC1C,WAAW,CAAC0C,KAAK,GAAG,IAAI;EAC1B,CAAC;;EAED;EACA,MAAM4C,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAIpE,mBAAmB,CAACuB,KAAK,EAAE;MAC7BvB,mBAAmB,CAACuB,KAAK,CAAC2C,QAAQ,CAACG,IAAI,CAAC;QACtC,GAAGD,OAAO;QACVE,SAAS,EAAE,IAAIP,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIvE,mBAAmB,CAACuB,KAAK,EAAE;MAC7BxB,mBAAmB,CAACwB,KAAK,CAAC8C,IAAI,CAAC;QAC7B,GAAGrE,mBAAmB,CAACuB,KAAK;QAC5BiD,OAAO,EAAE,IAAIT,IAAI,CAAC;MACpB,CAAC,CAAC;MACF/D,mBAAmB,CAACuB,KAAK,GAAG,IAAI;IAClC;IACAzB,aAAa,CAACyB,KAAK,GAAG,MAAM;IAC5B1C,WAAW,CAAC0C,KAAK,GAAG,KAAK;IACzBzC,UAAU,CAACyC,KAAK,GAAG,KAAK;EAC1B,CAAC;;EAED;EACA,MAAMkD,KAAK,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,SAAS,KAAK;IAC3C7F,UAAU,CAACyC,KAAK,GAAG,IAAI;IACvBzB,aAAa,CAACyB,KAAK,GAAG,UAAU;IAChC1B,iBAAiB,CAAC0B,KAAK,GAAGoD,OAAO;;IAEjC;IACAC,UAAU,CAAC,MAAM;MACf9F,UAAU,CAACyC,KAAK,GAAG,KAAK;MACxBzB,aAAa,CAACyB,KAAK,GAAG,MAAM;MAC5B1B,iBAAiB,CAAC0B,KAAK,GAAG,SAAS;IACrC,CAAC,EAAEmD,IAAI,CAACG,MAAM,GAAG,GAAG,CAAC,EAAC;EACxB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIxD,UAAU,IAAK;IACpCzB,iBAAiB,CAAC0B,KAAK,GAAGD,UAAU;EACtC,CAAC;;EAED;EACA,MAAMyD,SAAS,GAAIC,MAAM,IAAK;IAC5BlF,aAAa,CAACyB,KAAK,GAAGyD,MAAM;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA;IACA,MAAMC,QAAQ,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IAChE,OAAOA,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,QAAQ,CAACL,MAAM,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMnC,OAAO,GAAG;MACdoC,QAAQ,EAAE,SAAS;MACnBrG,OAAO,EAAE,SAAS;MAClBsG,OAAO,EAAE,SAAS;MAClBvG,SAAS,EAAE,IAAI8E,IAAI,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS,GACtC,IAAI1B,IAAI,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG;IACxD,CAAC;IAEDzG,YAAY,CAACuC,KAAK,GAAG;MAAE,GAAGvC,YAAY,CAACuC,KAAK;MAAE,GAAG4B;IAAQ,CAAC;IAC1D,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC;MAChDpH,WAAW,EAAEA,WAAW,CAAC6C,KAAK;MAC9BhC,eAAe,EAAEA,eAAe,CAACgC,KAAK;MACtCvC,YAAY,EAAEA,YAAY,CAACuC;IAC7B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;IAChD,IAAID,KAAK,EAAE;MACT,IAAI;QACF,MAAME,IAAI,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;QAC9BtH,WAAW,CAAC6C,KAAK,GAAG2E,IAAI,CAACxH,WAAW,IAAI,WAAW;QACnDa,eAAe,CAACgC,KAAK,GAAG;UAAE,GAAGhC,eAAe,CAACgC,KAAK;UAAE,GAAG2E,IAAI,CAAC3G;QAAgB,CAAC;QAC7EP,YAAY,CAACuC,KAAK,GAAG;UAAE,GAAGvC,YAAY,CAACuC,KAAK;UAAE,GAAG2E,IAAI,CAAClH;QAAa,CAAC;MACtE,CAAC,CAAC,OAAOoH,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,KAAK,CAAC;MAClD;IACF;EACF,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1BR,YAAY,CAAC,CAAC;IACdT,cAAc,CAAC,CAAC;;IAEhB;IACAkB,WAAW,CAACd,YAAY,EAAE,KAAK,CAAC,EAAC;EACnC,CAAC;;EAED;;EAEA;EACA,MAAMe,cAAc,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;IAC/C1G,iBAAiB,CAACsB,KAAK,CAACqF,GAAG,CAACF,QAAQ,EAAE;MACpC,GAAGC,UAAU;MACbE,YAAY,EAAE,IAAI9C,IAAI,CAAC,CAAC;MACxBnF,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFuB,aAAa,CAACoB,KAAK,CAACuF,GAAG,CAACJ,QAAQ,CAAC;IACjCL,OAAO,CAACU,GAAG,CAAC,gBAAgBL,QAAQ,EAAE,EAAEC,UAAU,CAAC;IACnD,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAIN,QAAQ,IAAK;IACrCzG,iBAAiB,CAACsB,KAAK,CAAC0F,MAAM,CAACP,QAAQ,CAAC;IACxCvG,aAAa,CAACoB,KAAK,CAAC0F,MAAM,CAACP,QAAQ,CAAC;IACpCL,OAAO,CAACU,GAAG,CAAC,gBAAgBL,QAAQ,EAAE,CAAC;IACvC,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAACC,SAAS,EAAEjB,IAAI,KAAK;IACzC/F,aAAa,CAACoB,KAAK,CAAC6F,OAAO,CAACV,QAAQ,IAAI;MACtC,MAAMW,MAAM,GAAGpH,iBAAiB,CAACsB,KAAK,CAAC+F,GAAG,CAACZ,QAAQ,CAAC;MACpD,IAAIW,MAAM,IAAIA,MAAM,CAACE,aAAa,EAAE;QAClC,IAAI;UACFF,MAAM,CAACE,aAAa,CAACJ,SAAS,EAAEjB,IAAI,CAAC;QACvC,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,MAAMM,QAAQ,UAAU,EAAEN,KAAK,CAAC;QAChD;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA;EACA,MAAMoB,OAAO,GAAI/F,IAAI,IAAK;IACxB,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAACgG,QAAQ,CAAChG,IAAI,CAAC,EAAE;MAC7D,MAAMiG,OAAO,GAAGhJ,WAAW,CAAC6C,KAAK;MACjCwB,UAAU,CAACtB,IAAI,CAAC,EAAC;;MAEjB;MACAyF,aAAa,CAAC,YAAY,EAAE;QAAElE,OAAO,EAAEvB,IAAI;QAAEiG;MAAQ,CAAC,CAAC;MAEvDrB,OAAO,CAACU,GAAG,CAAC,aAAatF,IAAI,EAAE,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMkG,UAAU,GAAGA,CAAChD,OAAO,EAAEiD,QAAQ,GAAG,IAAI,KAAK;IAC/C,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAACH,QAAQ,CAAC9C,OAAO,CAAC,EAAE;MACrF,MAAMkD,UAAU,GAAGrH,cAAc,CAACe,KAAK;MACvCf,cAAc,CAACe,KAAK,GAAGoD,OAAO;MAC9B9E,iBAAiB,CAAC0B,KAAK,GAAGoD,OAAO,EAAC;;MAElC;MACAuC,aAAa,CAAC,eAAe,EAAE;QAAEY,UAAU,EAAEnD,OAAO;QAAEkD;MAAW,CAAC,CAAC;MAEnExB,OAAO,CAACU,GAAG,CAAC,YAAYpC,OAAO,EAAE,CAAC;;MAElC;MACA,IAAIA,OAAO,KAAK,SAAS,IAAIiD,QAAQ,GAAG,CAAC,EAAE;QACzChD,UAAU,CAAC,MAAM;UACf,IAAIpE,cAAc,CAACe,KAAK,KAAKoD,OAAO,EAAE;YACpCgD,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;UAC1B;QACF,CAAC,EAAEC,QAAQ,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIC,OAAO,IAAK;IACnCnJ,WAAW,CAAC0C,KAAK,GAAGyG,OAAO;IAC3B,IAAIA,OAAO,EAAE;MACXL,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,EAAC;IAC5B,CAAC,MAAM;MACLA,UAAU,CAAC,SAAS,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAItF,WAAW,IAAK;IAC1C,IAAIA,WAAW,EAAE;MACf6E,OAAO,CAAC,aAAa,CAAC;MACtBG,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;IAC7B,CAAC,MAAM;MACLH,OAAO,CAAC,WAAW,CAAC;MACpBG,UAAU,CAAC,SAAS,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BV,OAAO,CAAC,aAAa,CAAC;IACtBG,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;IAC3BtB,OAAO,CAACU,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC;EAED,OAAO;IACL;IACArI,WAAW;IACXC,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZO,eAAe;IACfM,iBAAiB;IACjBC,aAAa;IACbC,mBAAmB;IACnBC,mBAAmB;IAEnB;IACAoB,gBAAgB;IAChBM,kBAAkB;IAElB;IACAqB,UAAU;IACVE,WAAW;IACXG,iBAAiB;IACjBM,iBAAiB;IACjBE,iBAAiB;IACjBO,UAAU;IACVI,eAAe;IACfE,KAAK;IACLK,aAAa;IACbC,SAAS;IACTE,aAAa;IACbK,cAAc;IACdI,YAAY;IACZK,YAAY;IACZQ;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}